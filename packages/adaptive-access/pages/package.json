{"name": "@aae/pages", "version": "0.0.1", "description": "Adaptive Access", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.5.1", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/pro-light-svg-icons": "^6.5.1", "@fortawesome/pro-regular-svg-icons": "^6.5.1", "@fortawesome/pro-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.2", "@up/components": "workspace:*", "@up/std": "workspace:*", "@xc/common": "workspace:*", "@zscaler/zui-component-library": "^3.2.1", "axios": "^1.7.2", "clsx": "2.1.1", "dayjs": "^1.11.11", "i18next": "23.15.1", "lodash-es": "^4.17.21", "next": "14.2.28", "react": "18.3.1", "react-aria": "3.37.0", "react-dom": "18.3.1", "react-i18next": "15.0.2", "react-redux": "^9.1.2", "tailwind-merge": "2.6.0"}, "devDependencies": {"@chromatic-com/storybook": "catalog:storybook", "@storybook/addon-designs": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/addon-onboarding": "catalog:storybook", "@storybook/addon-webpack5-compiler-swc": "catalog:storybook", "@storybook/blocks": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@storybook/react-vite": "catalog:storybook", "@storybook/react-webpack5": "catalog:storybook", "@storybook/test": "catalog:storybook", "@types/lodash-es": "4.17.12", "@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitest/ui": "3.0.3", "autoprefixer": "10.4.20", "esbuild-plugin-copy": "^2.1.1", "eslint": "8.57.1", "postcss": "8.5.3", "postcss-import": "16.1.0", "postcss-url": "10.1.3", "prettier": "3.5.2", "storybook": "catalog:storybook", "tailwindcss": "3.4.17", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vitest": "3.1.1"}, "peerDependencies": {"@zs-nimbus/core": "catalog:nimbus", "@zs-nimbus/dataviz-colors": "catalog:nimbus", "@zs-nimbus/foundations": "catalog:nimbus", "i18next": "23.15.1", "next": "14.2.28", "react": ">= 18.3.1", "react-dom": ">= 18.3.1", "react-i18next": "15.0.2"}, "prettier": "@up/prettier-config"}