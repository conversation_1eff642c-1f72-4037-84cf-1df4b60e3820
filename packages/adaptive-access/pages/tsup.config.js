import { defineConfig } from "tsup";
import copy from "esbuild-plugin-copy";

export default defineConfig({
  entry: ["src/index.ts", "!src/**/*.test.*"],
  format: ["esm"],
  splitting: false,
  sourcemap: false,
  minify: false, //!options.watch,
  clean: true,
  silent: true,
  dts: {
    resolve: true,
  },
  external: ["react", "react-dom"],
  esbuildPlugins: [
    // Copy SCSS file(s) into "dist"
    copy({
      // Specify the files or directories to copy
      assets: [
        // Copy .scss files in zuxp-layout
        {
          from: "./src/zuxp-layout/**/*.scss",
          to: "./",
        },
        // Copy ALL SCSS from the "scss" folder
        {
          from: "./src/scss/**/*", // Copy entire SCSS folder contents
          to: "./scss", // Maintain relative location (e.g., dist/scss/main.scss)
        },
      ],
    }),
  ],
});
