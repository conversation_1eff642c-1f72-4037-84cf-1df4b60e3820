{"name": "@xc/example-page", "version": "0.0.1", "description": "An Experience Center (XC) Module Package.", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test", "lost-pixel": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel", "lost-pixel:update": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel update --configDir ./.lostpixel/config"}, "dependencies": {"@up/components": "workspace:*", "@up/std": "workspace:*", "@xc/common": "workspace:*", "clsx": "2.1.1", "i18next": "23.15.1", "next": "14.2.28", "react": "18.3.1", "react-aria": "3.37.0", "react-dom": "18.3.1", "react-i18next": "15.0.2", "tailwind-merge": "2.6.0"}, "devDependencies": {"@chromatic-com/storybook": "3.2.4", "@storybook/addon-designs": "8.2.0", "@storybook/addon-essentials": "8.6.0", "@storybook/addon-interactions": "8.6.0", "@storybook/addon-links": "8.6.0", "@storybook/addon-onboarding": "8.6.0", "@storybook/addon-webpack5-compiler-swc": "2.0.0", "@storybook/blocks": "8.6.0", "@storybook/nextjs": "8.6.0", "@storybook/react": "8.6.0", "@storybook/react-vite": "8.6.0", "@storybook/react-webpack5": "8.6.0", "@storybook/test": "8.6.0", "@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitest/ui": "3.0.3", "autoprefixer": "10.4.20", "eslint": "8.57.1", "lost-pixel": "3.22.0", "postcss": "8.5.3", "postcss-import": "16.1.0", "postcss-url": "10.1.3", "prettier": "3.5.2", "storybook": "8.6.0", "tailwindcss": "3.4.17", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vitest": "3.1.1"}, "peerDependencies": {"react": ">= 18.3.1", "react-dom": ">= 18.3.1", "next": "14.2.28", "@zs-nimbus/core": "catalog:nimbus", "@zs-nimbus/dataviz-colors": "catalog:nimbus", "@zs-nimbus/foundations": "catalog:nimbus", "i18next": "23.15.1", "react-i18next": "15.0.2"}, "prettier": "@up/prettier-config"}