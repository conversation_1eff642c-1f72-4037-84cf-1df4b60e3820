import { describe, expect, it, vi } from "vitest";
import { getZiaVersionInfo } from "./getZiaCloudVersion.js";
import { ZIA_ROUTE_PREFIX } from "../constants.js";

describe("getZiaVersionInfo", () => {
  const baseDir = `/${ZIA_ROUTE_PREFIX}`;

  it("should return version info for a valid cloud version (6.3)", () => {
    const cloudVersion = "6.3.2432.410191_114";
    const expected = {
      release: "6.3",
      build: "2432",
      packageVersion: "63.0",
      dir: `${baseDir}/63.0`,
      isGranular: true,
    };

    expect(getZiaVersionInfo(cloudVersion)).toEqual(expected);
  });

  it("should return version info for a valid cloud version (6.2r)", () => {
    const cloudVersion = `6.2r.2502_prod.72.410379_114`;
    const expected = {
      release: "6.2r",
      build: `2502_prod`,
      packageVersion: `62.2502`,
      dir: `${baseDir}/62.2502`,
      isGranular: false,
    };

    expect(getZiaVersionInfo(cloudVersion)).toEqual(expected);
  });

  it("should handle build version greater than 2502 for isGranular", () => {
    const cloudVersion = "6.3.2503.410191_114";
    const expected = {
      release: "6.3",
      build: "2503",
      packageVersion: "63.0",
      dir: `${baseDir}/63.0`,
      isGranular: true,
    };

    expect(getZiaVersionInfo(cloudVersion)).toEqual(expected);
  });

  it("should handle missing build version", () => {
    const cloudVersion = "6.3";

    const expected = {
      release: "6.3",
      build: "",
      packageVersion: "63.0",
      dir: `${baseDir}/63.0`,
      isGranular: true,
    };

    expect(getZiaVersionInfo(cloudVersion)).toEqual(expected);
  });

  it("should handle malformed cloudVersion", () => {
    const cloudVersion = "${release.version}";

    const expected = {
      build: "",
      packageVersion: "63.0",
      dir: `${baseDir}/63.0`,
      isGranular: true,
    };

    expect(getZiaVersionInfo(cloudVersion)).toMatchObject(expected);
  });

  it("should handle malformed cloudVersion zenith live", () => {
    const cloudVersion = "6.3_zenithlive_2025_v2.1.b9f53e328a3_114";

    const expected = {
      build: "1",
      packageVersion: "63.0",
      dir: `${baseDir}/63.0`,
      isGranular: true,
    };

    expect(getZiaVersionInfo(cloudVersion)).toMatchObject(expected);
  });
});
