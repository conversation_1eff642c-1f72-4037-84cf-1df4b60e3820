import { ZIA_ROUTE_PREFIX } from "../constants.js";
import { ZiaVersionInfo } from "../rbac/index.js";
import ziaPackagesManifest from "@zia/combined-packages/manifest" with { type: "json" };

const getMajorVersion = (base: string) => {
  const mapping: Record<string, string> = {
    "6.3": "63",
    "6.2r": "62",
  };

  return mapping[base] ?? "63";
};

const getMinorVersion = (cutoff: string) => {
  if (cutoff.includes("prod")) {
    const match = cutoff.match(/\d+/);

    return match ? match[0] : "0";
  }

  return "0";
};

// convert 6.2r.701_prod -> 62.701
const getPackageVersion = (base?: string, build?: string): string => {
  // Get installed versions
  const versions = ziaPackagesManifest.versions;

  // Fallback for when cloud version does not match the expected format
  const fallback = "63.0";

  if (process.env.NODE_ENV === "development") {
    if (!versions.includes(fallback)) {
      throw new Error(
        `Fallback version ${fallback} does not exist in the version list.`,
      );
    }
  }

  if (!base || !build) return fallback;

  const major = getMajorVersion(base);
  const minor = getMinorVersion(build);
  const version = `${major}.${minor}`;

  if (versions.includes(version)) {
    return version;
  }

  // If not found in installed versions, return a fallback
  return fallback;
};

const getNumericBuild = (build: string) => {
  // try to parse the whole string
  if (!isNaN(parseInt(build))) {
    return parseInt(build);
  }

  const buildNum = build.split("_").at(0);
  if (buildNum) {
    return parseInt(buildNum);
  }

  return NaN;
};

const baseDir = `/${ZIA_ROUTE_PREFIX}`;

export const getZiaVersionInfo = (cloudVersion: string): ZiaVersionInfo => {
  // 6.3.2432.410191_114 -> 6, 3, 2432
  // 6.2r.701_prod.72.410379_114 -> 6, 2r, 701_prod
  const [major, minor, build = ""] = cloudVersion.split(".");

  const release = `${major}.${minor}`; // 6.3; 6.2r
  const packageVersion = getPackageVersion(release, build); // 63.0; 62.701

  const numericBuild = getNumericBuild(build);

  let isGranular = true;
  if (!isNaN(numericBuild) && numericBuild === 2502) {
    // check for older builds using legacy rbac
    isGranular = false;
  }

  return {
    release,
    build: build,
    packageVersion,
    dir: `${baseDir}/${packageVersion}`,
    isGranular,
  };
};
