import tailwindDefault from "tailwindcss/defaultConfig";
import { datavizColorsPreset } from "@zs-nimbus/dataviz-colors";
import { foundationsPreset } from "@zs-nimbus/foundations";

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{jsx,tsx,ts}"],
  safelist: ["py-3.5"],
  presets: [tailwindDefault, foundationsPreset, datavizColorsPreset],
  theme: {
    extend: {
      spacing: {
        none: "0rem",
        xxs: "0.125rem",
        xs: "0.25rem",
        s: "0.4rem",
        default: "0.5rem",
        m: "0.75rem",
        l: "1rem",
        xl: "1.3rem",
        xxl: "1.5rem",
        xxxl: "2rem",
        "4xl": "2.5rem",
        "5xl": "3rem",
        "6xl": "4rem",
        // re-apply default spacing scales until nimbus does
        ...tailwindDefault.theme?.spacing,
      },
    },
  },
  plugins: [],
};
