import { createContext, useContext, useMemo, type ReactNode } from "react";
import i18next from "i18next";
import { isTrue } from "@up/std";
import { NavigationTree } from "../core";
import { type BuilderFn } from "./types";
import { initializeNavigationStore } from "./store";
import { NOPNavigatorImplementation, type Navigator } from "./router";

type NavigationContextState = {
  t: (key: string) => string;
  debug?: boolean;
  options: Record<string, unknown>;
  warnDuplicates?: boolean;
  navigator?: Navigator;
  defaultRoute: string;
};

export const NavigationContext = createContext<NavigationContextState>({
  t: (key: string) => key,
  debug: isTrue(process.env.NEXT_PUBLIC_NAVIGATION_DEBUG),
  warnDuplicates: isTrue(process.env.NEXT_PUBLIC_NAVIGATION_WARN_DUPLICATES),
  options: {},
  navigator: undefined,
  defaultRoute: "/",
} as NavigationContextState);

export const useNavigationContext = () => useContext(NavigationContext);

type NavigationProviderProps<T extends object> = {
  builder: BuilderFn<T>;
  t: (key: string) => string;
  children: ReactNode;
  debug?: boolean;
  warnDuplicates?: boolean;
  duplicateIgnores?: string[];
  options: Record<string, unknown>;
  entitlements?: T;
  navigator?: Navigator;
  defaultRoute: string;
};

export const NavigationProvider = <T extends object>({
  builder,
  children,
  t = (key: string) => key,
  options = {},
  debug = false,
  warnDuplicates = false,
  entitlements = undefined,
  duplicateIgnores = [],
  navigator = new NOPNavigatorImplementation(),
  defaultRoute = "/",
}: NavigationProviderProps<T>) => {
  // This is a throwaway variable to ensure that the builder function is called
  const tree = new NavigationTree<T>();

  // we do not care about the result from builder
  builder(tree, options, entitlements);
  tree.buildSearchTrie(t, entitlements);

  if (warnDuplicates) {
    const dupeRoutes = tree.duplicateRoutes(duplicateIgnores);
    if (dupeRoutes.length > 0) {
      console.log(`[navigation] ${dupeRoutes.length} duplicate route(s) found`);
      dupeRoutes.forEach((route) => console.log(`  ${route}`));
    }
  }

  if (debug) {
    console.log(tree.draw());
  }

  initializeNavigationStore(tree);

  // when the user changes the language, rebuild the search (cmd+k) trie using the new locale
  i18next.on("languageChanged", (_) => {
    tree.buildSearchTrie(t);
  });

  const navigationCtxState = useMemo(
    () => ({
      t,
      options,
      navigator,
      defaultRoute,
    }),
    [t, options, navigator, defaultRoute],
  );

  return (
    <NavigationContext.Provider value={navigationCtxState}>
      {children}
    </NavigationContext.Provider>
  );
};
