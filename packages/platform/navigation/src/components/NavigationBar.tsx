import { type PropsWithChildren } from "react";
import { cn } from "@up/components";
import { LoadingSkeleton } from "@zs-nimbus/core";
import { props, restProps, testIdFrom } from "@up/std";
import {
  filterHiddenNodes,
  Layer,
  type NavigationVisibility,
  Visibility,
  type TreeNode,
  resolveRoute,
} from "../core";
import { useNavigationContext, useNavigationStore } from "../context";
import { type BaseNavigationProps } from "./types";
import {
  buttonVariants,
  iconVaraints,
  outerWrapperVaraints,
  wrapperVaraints,
} from "./NavigationBarStyle";
import { handleKeyboardDown } from "./events";
import { currentRelativePath } from "./helpers";

type Props<T extends object> = {
  layer?: Layer;
  active?: TreeNode<T>;
  current?: TreeNode<T>;
  onHover?: (node?: TreeNode<T>, children?: boolean) => void;
  onItemClicked?: (node?: TreeNode<T>) => void;
  onClose?: () => void;
  dismissible?: boolean;
  highlightInitial?: boolean;
} & BaseNavigationProps<T>;

export function NavigationBar<T extends object>({
  orientation = "horizontal",
  children,
  layer = Layer.Primary,
  active = undefined,
  onHover,
  onItemClicked,
  entitlements,
  current,
  dismissible = false,
  onClose,
  highlightInitial = false,

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  ...rest
}: PropsWithChildren<Props<T>>): JSX.Element {
  const {
    store,
    selectedIds,
    setActive,
    selected,
    inferCorrectNode,
    isRouteKnown,
  } = useNavigationStore<T>();

  const navigationPath = selected();

  const { t, navigator } = useNavigationContext();

  const inferred = inferCorrectNode(
    active ?? "",
    Layer.Secondary,
    entitlements,
  );
  const knownRoute = isRouteKnown(currentRelativePath());

  let nodes = store.tree.layer(layer, active);
  if (nodes.length === 0) {
    const pid = inferred?.parentId;
    if (pid) {
      nodes = store.tree.layer(layer, pid);
    }
  }
  if (nodes.length > 0) {
    layer = nodes[0]?.option?.layer ?? Layer.Secondary;
  }

  if (entitlements) {
    nodes = nodes.filter((node) => !filterHiddenNodes(node, entitlements));
  }

  const isClickable = (node: TreeNode<T>) => node.option.route !== undefined;

  const isActiveItem = (item: TreeNode<T>, idx: number): boolean => {
    if (!knownRoute && layer === Layer.Primary) {
      return false;
    }
    // if we dont have anything selected, highlight the fisst one
    if (
      layer === Layer.Primary &&
      Object.keys(selectedIds()).length === 0 &&
      idx === 0
    ) {
      return true;
    }

    const resnode = navigationPath[item.option.layer ?? 0];
    // store.tree.get(
    //   navigationPath[item.option.layer ?? 0] ?? "",
    // );

    // if the resolved node is the root node (initial render), lets use the infered
    // node for the layer
    if (resnode?.id === store.tree.root_id && inferred) {
      return item.id === inferred.id;
    }

    // if we are just clicking around, we still want to highlight what we clicked.
    if (active?.id === inferred?.id) {
      return item.id === active?.id;
    }

    // if we are auto-focusing first children (ex: on Secondary bar)
    if (
      highlightInitial &&
      resnode?.parentId !== inferred?.parentId &&
      resnode?.id !== inferred?.id
    ) {
      return item.id === inferred?.id;
    }

    // check resolved node first, fallback to inferred node
    return item.id === (resnode?.id ?? inferred?.id);
  };

  const wrapperClasses = wrapperVaraints({
    orientation,
    layer: layer as number,
    dismissible,
  });
  const outerWrapperClasses = outerWrapperVaraints({
    orientation,
    layer: layer as number,
    dismissible,
  });

  const handleButtonClick = (n: TreeNode<T>, canNavigate: boolean) => {
    if (canNavigate && navigator) {
      setActive(n);

      if (navigator) {
        const href = resolveRoute(n.option.route ?? "/", entitlements);
        navigator.push(href, { external: n.option.external ?? false });
      }
    } else if (onItemClicked) {
      onItemClicked(n);
    }
  };

  return (
    <div className={outerWrapperClasses}>
      <div {...restProps(rest)} className={cn(wrapperClasses)}>
        {nodes.map((n: TreeNode<T>, idx: number) => {
          let state: NavigationVisibility = "visible";
          if (entitlements) {
            state = n.option.visibility?.(entitlements) ?? "visible";
          }

          const classList = cn(
            buttonVariants({
              layer: layer as number,
              active: isActiveItem(n, idx),
              loading: state === Visibility.LOADING,
              current: current?.id === n.id,
            }),
          );

          const canNavigate = isClickable(n);
          return (
            <button
              {...props({
                id: rest.id,
                testId: testIdFrom(rest, ["tab", idx]),
              })}
              type="button"
              key={n.id}
              className={classList}
              onMouseEnter={() => {
                if (onHover) {
                  onHover(n, !isClickable(n) ? n.children.length > 0 : false);
                }
              }}
              onClick={() => {
                handleButtonClick(n, canNavigate);
              }}
              onKeyDown={handleKeyboardDown(() => {
                if (layer === Layer.Primary) {
                  if (onHover) {
                    onHover(n, !isClickable(n) ? n.children.length > 0 : false);
                  }
                } else {
                  handleButtonClick(n, canNavigate);
                }
              })}
              tabIndex={0}
            >
              <>
                {state === Visibility.LOADING ? (
                  <div className="w-20">
                    <LoadingSkeleton variant="text" />
                  </div>
                ) : (
                  <>
                    {canNavigate ? (
                      <div className="text-left">{t(n.option.key)}</div>
                    ) : (
                      <div className="text-left">
                        {layer === Layer.Secondary && n.option.icon && (
                          <i
                            className={cn(
                              n.option.icon,
                              iconVaraints({
                                active: isActiveItem(n, idx),
                                layer: layer as number,
                              }),
                            )}
                          />
                        )}

                        <span>
                          {t(n.option.key)}
                          {layer === Layer.Primary && (
                            <i className="fa-regular fa-chevron-down ml-default" />
                          )}
                        </span>
                      </div>
                    )}
                  </>
                )}
              </>
            </button>
          );
        })}
        {children}
      </div>
      {dismissible && (
        <button
          type="button"
          onClick={() => {
            if (onClose) {
              onClose();
            }
          }}
        >
          <i className="fa-regular fa-close cursor-pointer text-semantic-content-interactive-primary-active mr-xl" />
        </button>
      )}
    </div>
  );
}
