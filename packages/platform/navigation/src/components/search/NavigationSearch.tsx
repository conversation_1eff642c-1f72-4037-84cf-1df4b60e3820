import { cn } from "@up/components";
import { useOnClickOutside } from "@up/std";
import {
  type ChangeEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
  type HTMLAttributes,
} from "react";
import { type SearchMetaObj } from "../../core/trie/search_trie";
import { useNavigationContext, useNavigationStore } from "../../context";
import { type NavigateFn } from "../types";
import { isVisibleState } from "../../core";
import { SearchKeyboardShortcut } from "./SearchKeyboardShortcut";
import { SearchResults } from "./SearchResults";

type NavigationSearchProps<T> = HTMLAttributes<HTMLDivElement> & {
  triggerKey?: string;
  placeholder?: string;
  onClose: () => void;
  entitlements?: T;
  onNavigate: NavigateFn<T>;
};

export function NavigationSearch<T extends object>({
  triggerKey = "k",
  placeholder = "SEARCH_MENU",
  onClose,
  entitlements,
  onNavigate,
  ...rest
}: NavigationSearchProps<T>) {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);
  const { t } = useNavigationContext();
  const { store } = useNavigationStore();

  const [query, setQuery] = useState<string>("");
  const [results, setResults] = useState<Array<SearchMetaObj<T>>>([]);

  const [focused, setFocused] = useState<boolean>(false);

  useOnClickOutside(searchRef, () => {
    onClose();
  });

  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleResetSearchQuery = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    setResults([]);
    setQuery("");
  }, [inputRef]);

  const filterResult = (r: SearchMetaObj<T>): boolean => {
    return (
      !entitlements || isVisibleState(r.visibility?.(entitlements) ?? "visible")
    );
  };

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (value === "") {
      setResults([]);
      setQuery("");
    } else {
      const searchResults = store.tree.search2(value, filterResult);
      setResults(searchResults ?? []);
      setQuery(value);
    }
  };

  return (
    <div
      ref={searchRef}
      className={cn(
        "z-20 absolute left-1/2 transform -translate-x-1/2 mt-rem-160 top-full gap-default cursor-pointer bg-semantic-surface-base-primary",
        {
          "shadow-large rounded-80 overflow-hidden": focused,
        },
      )}
      data-testid="search"
      {...rest}
    >
      <div className="relative flex items-center">
        <i
          aria-label={t("SEARCH_ICON")}
          className="z-10 text-semantic-content-base-secondary absolute left-default px-xxs fa-regular fa-magnifying-glass"
        />
        <input
          ref={inputRef}
          type="text"
          name="searchInput"
          id="global-search-input"
          autoComplete="off"
          maxLength={30}
          placeholder={t(placeholder)}
          className={cn(
            "typography-paragraph1 leading-[1.28] flex-1 py-rem-100 shadow-elevation-4 pl-xxxl pr-[1.75rem] w-[500px] text-semantic-content-base-secondary bg-semantic-surface-fields-default rounded-80 border-semantic-border-base-primary",
            {
              "rounded-b-none shadow-none": focused,
            },
          )}
          onChange={handleInputChange}
          onFocus={() => setFocused(true)}
        />
        <SearchKeyboardShortcut
          onClear={handleResetSearchQuery}
          triggerKey={triggerKey}
          query={query}
        />
      </div>
      {focused && (
        <SearchResults<T>
          onNavigate={onNavigate}
          results={results}
          query={query}
          onClose={onClose}
          entitlements={entitlements}
        />
      )}
    </div>
  );
}

export default NavigationSearch;
