import { cn } from "@up/components";
import { props, testIdFrom } from "@up/std";
import { Layer, type TreeNode } from "../core";
import { useNavigationContext } from "../context";
import { type BaseNavigationProps } from "./types";

type Props<T> = BaseNavigationProps<T> & {
  node: TreeNode<T>;
  onToggleExpansion: (node: TreeNode<T>) => void;
  active?: boolean;
  layer?: Layer;
  primaryLayer?: Layer;
};

export const MenuExpansion = <T extends object>({
  node,
  onToggleExpansion,
  active = false,
  layer = Layer.Secondary,
  primaryLayer = Layer.Tertiary,
  ...rest
}: Props<T>) => {
  const { t } = useNavigationContext();

  const nodeLayer = node.option?.layer ?? layer;

  return (
    <button
      type="button"
      className="flex justify-between"
      onKeyDown={() => console.log("TODO: Keydown")}
      onClick={() => {
        onToggleExpansion(node);
      }}
      tabIndex={0}
      {...props({
        testId: testIdFrom(rest, ["expansion", "button"]),
      })}
    >
      <i
        aria-label={node.expanded ? t("DOWN_ICON") : t("RIGHT_ICON")}
        className={cn(
          "fa-solid ml-auto text-semantic-content-base-subdued rounded-40",
          {
            "px-[5px] fa-caret-down":
              node.expanded && nodeLayer <= primaryLayer,
          },
          {
            "px-[6px] fa-caret-right":
              !node.expanded && nodeLayer <= primaryLayer,
          },
          {
            "px-[5px] fa-chevron-down fa-xs":
              node.expanded && nodeLayer > primaryLayer,
          },
          {
            "px-[6px] fa-chevron-right fa-xs":
              !node.expanded && nodeLayer > primaryLayer,
          },
          {
            "text-semantic-content-interactive-primary-default": active,
          },
        )}
      />
    </button>
  );
};
