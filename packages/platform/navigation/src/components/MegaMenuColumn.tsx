import { cn } from "@up/components";
import { LoadingSkeleton } from "@zs-nimbus/core";
import { props, restProps, testIdFrom } from "@up/std";
import { useCallback } from "react";
import { useNavigationContext, useNavigationStore } from "../context";
import {
  Layer,
  type NavigationVisibility,
  Visibility,
  type TreeNode,
  resolveRoute,
  isVisibleState,
} from "../core";
import { useOverflowTooltip } from "../hooks";
import { type BaseNavigationProps } from "./types";
import { handleKeyboardDown } from "./events";
import TruncatedTextTooltip from "./TruncatedTooltip";

type Props<T extends object> = {
  active: TreeNode<T>;
  onClicked: () => void;
} & BaseNavigationProps<T>;

export const MegaMenuColumn = <T extends object>({
  active,
  entitlements,
  onClicked,
  ...rest
}: Props<T>) => {
  const { store, setActive, current } = useNavigationStore<T>();
  const { onTooltipEnter, onTooltipLeave, tooltip } = useOverflowTooltip({
    width: 180,
  });

  const { t, navigator } = useNavigationContext();
  const cid = current()?.id;

  const node = store.tree.get(active);
  let items = store.tree.layer(Layer.Quinary, active);

  const handleClick = useCallback(
    (n: TreeNode<T>) => {
      if (onClicked) {
        onClicked();
      }

      if (navigator) {
        setActive(n);

        const href = resolveRoute(n.option.route ?? "/", entitlements);
        navigator?.push(href, { external: n.option.external ?? false });
        navigator?.refresh();
      }
    },
    [onClicked, navigator, setActive, entitlements],
  );

  const handlePrefetch = useCallback(
    (n: TreeNode<T>) => {
      if (navigator) {
        const href = resolveRoute(n.option.route ?? "/", entitlements);
        navigator.prefetch(href);
      }
    },
    [navigator, entitlements],
  );

  if (entitlements) {
    items = items.filter((n) => {
      return n.option.visibility === undefined
        ? "visible"
        : n.option.visibility?.(entitlements) !== "hidden";
    });
  }

  if (items.length === 0) {
    return null;
  }

  return (
    <div
      className="flex flex-col min-w-[196px] gap-default"
      {...restProps(rest)}
    >
      <span className="relative">
        <div
          className="pl-4 typography-paragraph2-strong text-semantic-content-base-secondary w-fit max-w-[180px] zs-text-truncate-default mt-rem-120"
          onMouseEnter={(e) => onTooltipEnter(e, node?.option.key ?? "")}
          onMouseLeave={onTooltipLeave}
        >
          {t(node?.option.key ?? "")}
        </div>
        {tooltip === node?.option.key && (
          <TruncatedTextTooltip
            content={t(node?.option.key)}
            heading
            id={`truncated-tooltip-${node?.id ?? ""}`}
          />
        )}
      </span>
      <div className="flex mb-rem-180 flex-col gap-default">
        {items.map((n: TreeNode<T>, idx: number) => {
          let cl = "";
          let state: NavigationVisibility = "visible";
          const isActive = cid === n.id;

          if (entitlements) {
            state = n.option.visibility?.(entitlements) ?? "visible";
          }
          if (state === "disabled") {
            cl = "text-semantic-content-interactive-primary-disabled";
          }

          if (isVisibleState(state)) {
            cl =
              "hover:text-semantic-content-interactive-primary-hover focus-visible-default";
          }

          return (
            <div
              className="flex items-center gap-x-2 text-semantic-content-interactive-primary-default w-[196px]"
              key={n.option.key ?? n.id}
              id={n.option.key}
            >
              <div className="w-2 flex items-center justify-center">
                {isActive && <i className="fa-regular fa-check fa-2xs   " />}
              </div>
              <>
                {state === Visibility.LOADING ? (
                  <LoadingSkeleton variant="text" />
                ) : (
                  <div className="relative">
                    <div className="flex items-center justify-start gap-x-2 w-[180px] relative">
                      <a
                        href={resolveRoute(n.option.route ?? "/", entitlements)}
                        key={n.id}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          if (state !== Visibility.DISABLED) {
                            handleClick(n);
                          }
                        }}
                        onKeyDown={handleKeyboardDown(() => handleClick(n))}
                        onMouseEnter={(e) => {
                          onTooltipEnter(e, n?.option.key ?? "");
                          handlePrefetch(n);
                        }}
                        onMouseLeave={onTooltipLeave}
                        className={cn(
                          "space-x-xs text-semantic-content-interactive-primary-default typography-paragraph2 whitespace-nowrap text-left overflow-hidden overflow-ellipsis",
                          cl,
                        )}
                        {...props({
                          testId: testIdFrom(rest, ["link", idx]),
                        })}
                        tabIndex={0}
                      >
                        {t(n.option.key)}
                      </a>
                      {n.option.external && (
                        <div className="text-semantic-content-interactive-primary-default">
                          <i
                            aria-label={t("UP_RIGHT_ICON")}
                            className="fa-regular fa-arrow-up-right-from-square fa-2xs"
                          />
                        </div>
                      )}
                      {state === Visibility.NEW && (
                        <div className="text-semantic-content-interactive-primary-default">
                          NEW
                        </div>
                      )}
                    </div>
                    {tooltip === n?.option.key && (
                      <TruncatedTextTooltip
                        content={t(n?.option.key)}
                        heading
                        id={`truncated-tooltip-${n?.id ?? ""}`}
                      />
                    )}
                  </div>
                )}
              </>
            </div>
          );
        })}
      </div>
    </div>
  );
};
