/* eslint-disable @up/unified-platform/max-file-lines */
import { Layer } from "./layers";
import { NavigationTrie, SearchTrie } from "./trie";
import { type SearchMetaObj } from "./trie/search_trie";
import {
  type FilterFn,
  type Menu,
  type NavigationMatch,
  type TreeNode,
} from "./types";

export const ContainerKey = "_wrapper_";
export const NodeIDPrefix = "xcid";

export class NavigationTree<T extends object> {
  private nodes: Map<string, TreeNode<T>>;
  private nav_trie: NavigationTrie<string, T>;
  private search_trie: SearchTrie<T>;
  private counter: number;
  private last_resolved: Map<string, TreeNode<T> | undefined>;

  // caches layer path resolution since menus are built at the start. Would need to be cleared if we duynamically inject
  private path_cache: Record<string, Record<number, string | TreeNode<T>>>;
  root_id: string;
  root: TreeNode<T>;

  constructor() {
    this.nodes = new Map<string, TreeNode<T>>();
    this.nav_trie = new NavigationTrie<string, T>();
    this.search_trie = new SearchTrie<T>();
    this.counter = 0;
    this.path_cache = {};
    this.root = {} as TreeNode<T>;
    this.root_id = this.addRootNode();
    // doing this in a map works around a readonly object issue when accessed via a valtio store
    this.last_resolved = new Map<string, TreeNode<T> | undefined>();
  }

  // size returns the number of total menu nodes. This does not include the root node, but **it does** include
  // placehodler container nodes as they are significant
  size(): number {
    // size the menu, minus the root node
    return this.nodes.size - 1;
  }

  // depth returns the deepest ordinal that is in the tree
  depth(): Layer {
    let depth = Layer.Root;

    // eslint-disable-next-line
    for (const node of this.nodes.values()) {
      if (node.option.layer && node.option.layer > depth) {
        depth = node.option.layer;
      }
    }
    return depth;
  }

  // reset resets the tree  by removing all routes, resetting the counter and adding a new root node
  reset(): void {
    this.nodes.clear();
    this.nav_trie = new NavigationTrie<string, T>();
    this.search_trie = new SearchTrie<T>();
    this.counter = 0;
    this.root = {} as TreeNode<T>;
    this.root_id = this.addRootNode();
  }

  // insert will add a navigation menu to the tree. Typically this is managed using NavigationBuilder
  insert(option: Menu<T>, parentId?: string, entitlements?: T): TreeNode<T> {
    let parentNode: TreeNode<T>;

    if (!parentId) {
      parentNode = this.nodes.get(this.root_id)!;
    } else {
      if (this.nodes.has(parentId)) {
        parentNode = this.nodes.get(parentId)!;
      } else {
        throw new Error(`parent with id "${parentId}" not found.`);
      }
    }

    const parentLayer = parentNode.option.layer ?? Layer.Root;

    const altIndex = option.index ?? parentNode.option.index ?? 0;

    // if we have an add that skips a layer (ex: no vertical tabs), add a blank container as a parent)
    if (option.layer && option.layer == ((parentLayer + 2) as Layer)) {
      parentNode = this.insert(
        {
          key: ContainerKey,
          layer: parentLayer + 1,
          container: true,
          index: altIndex,
        },
        parentNode.id,
      );
    }

    const newNode: TreeNode<T> = {
      id: this.generateId(),

      option: {
        ...option,
        layer: (parentNode.option.layer ?? 0) + 1, // Automatically set layer
        index: altIndex,
      },
      parentId: parentNode.id,
      children: [],
      expanded: false,
    };

    // Add the new node to the map
    this.nodes.set(newNode.id, newNode);

    if (newNode.option.route && newNode.option.route !== "") {
      // the search trie is just route -> id vs whole treenode, just saving on memory and has negligable impact
      // on perf
      this.nav_trie.insert(newNode.id, newNode.option.route, entitlements);
    }

    // Add the new node to the parent's children
    (parentNode.children as Array<TreeNode<T>>).push(newNode);
    return newNode;
  }

  exists(route: string): boolean {
    return this.nav_trie.find(route) !== undefined;
  }

  // searchKey will find a node by its options.key
  searchKey(key: string): TreeNode<T> | undefined {
    let match: TreeNode<T> | undefined;

    this.nodes.forEach((value: TreeNode<T>) => {
      if (value.option?.key === key) {
        match = value;
      }
    });

    return match;
  }

  // searchRoute will return a Navigation the node for that route or undefined.
  // NOTE: If there are dupe routes, it finds the first match

  search(
    route: string,
    layer: Layer = Layer.Secondary,
  ): NavigationMatch<T> | undefined {
    const match = {} as NavigationMatch<T>;

    const found = this.nav_trie.find(route);

    const node = this.nodes.get(found ?? "");
    if (!node) {
      return undefined;
    }

    match.node = node;
    match.parent = this.nodes.get(node.parentId ?? "");
    match.rootParent = this.traverseToLayer(node, layer);

    if (match.rootParent) {
      match.tree = this.siblings(match.rootParent);
      match.matchTree = this.pathToParent(match.node, match.rootParent);
    } else if (node?.option.layer === layer) {
      const pnode = this.nodes.get(node.parentId ?? "");
      if (pnode) {
        match.rootParent = { ...pnode, children: [] };
        //match.tree = pnode.children;
        match.matchTree = this.pathToParent(match.node, pnode);
      }
    } else if (node?.option.layer === Layer.Primary) {
      match.tree = this.resolve(node.children);
    }

    return match;
  }

  search2(
    phrase: string,
    filter?: (item: SearchMetaObj<T>) => boolean,
  ): Array<SearchMetaObj<T>> | undefined {
    let smo = this.search_trie.search(phrase);

    if (filter) {
      smo = smo.filter(filter);
    }

    return smo;
  }

  defaultForLayer(layer: Layer = Layer.Primary): TreeNode<T> | undefined {
    return this.layer(layer)[0];
  }

  //
  // Traversal & Navigation
  //

  // get returns the node for id. If expanded is true it returns the node with the children resolved out
  get(id: string | TreeNode<T>): TreeNode<T> | undefined {
    if (typeof id === "string" && this.last_resolved.get("-")?.id === id) {
      return this.last_resolved.get("-");
    }

    if (
      this.last_resolved.get("-") &&
      (id as TreeNode<T>).id === this.last_resolved.get("-")?.id
    ) {
      return this.last_resolved.get("-");
    }

    const node = this.resolveNode(id, undefined);

    if (!node) {
      return undefined;
    }

    this.last_resolved.set("-", node);

    return node;
  }

  childFirstLayer(
    id: string | TreeNode<T>,
    layer: Layer,
  ): TreeNode<T> | undefined {
    const node = this.resolveNode(id, undefined);
    if (!node) {
      return undefined;
    }

    if (node.option.layer === layer) {
      return node;
    }

    for (const child of node.children) {
      const result = this.childFirstLayer(child, layer);
      if (result) {
        return result;
      }
    }

    return undefined;
  }

  // layer returns array of nodes for the specified layer, from the optional root.
  layer(layer: Layer, parent?: TreeNode<T> | string): Array<TreeNode<T>> {
    const resp: Array<TreeNode<T>> = [];

    if (typeof parent === "string") {
      parent = this.get(parent);
    }

    const resolvedParent = this.resolveNode(parent, this.root);
    // Ensure we are getting only from a parent that can
    if (!this.isParentLayer(resolvedParent, layer)) {
      // TODO: Throw?
      return [];
    }

    resolvedParent.children.forEach((tn) => {
      resp.push(tn as TreeNode<T>);
    });

    return resp;
  }

  primary(): Array<TreeNode<T>> {
    return this.layer(Layer.Primary, undefined);
  }

  secondary(parent: TreeNode<T> | string): Array<TreeNode<T>> {
    return this.layer(Layer.Secondary, parent);
  }

  // siblings returns all siblings for a node. NOTE: Node is included in sibling list
  siblings(
    node: TreeNode<T> | string,
    filterContainers?: boolean,
  ): Array<TreeNode<T>> {
    const resolvedNode = this.resolveNode(node, undefined);

    const pnode = this.nodes.get(resolvedNode.parentId ?? "");

    if (pnode) {
      let nodes =
        typeof pnode.children[0] === "string"
          ? this.resolve(pnode.children)
          : (pnode.children as Array<TreeNode<T>>);

      if (filterContainers) {
        nodes = nodes.filter((n) => !this.isContainer(n));
      }

      return nodes;
    }

    return [];
  }

  expandNode(node: TreeNode<T>): TreeNode<T> {
    return this.tree(node);
  }

  traverseDownToLayer(
    id: string | TreeNode<T>,
    layer: Layer,
    entitlements: T,
    filter: FilterFn<T> = () => true,
  ): TreeNode<T> | undefined {
    const node = this.resolveNode(id, undefined);
    if (!node) {
      return undefined;
    }

    if (node.option.layer === layer) {
      return node;
    }

    let kids = node.children as Array<TreeNode<T>>;
    if (entitlements && filter) {
      kids = kids.filter((n) => {
        return !filter(n, entitlements);
      });
    }

    for (const child of kids) {
      const result = this.traverseDownToLayer(
        child,
        layer,
        entitlements,
        filter,
      );
      if (result) {
        return result;
      }
    }

    return undefined;
  }

  // traverseToLayer will take a node and find the closest parent on a certain layer
  // ex: used for findinng a sub-menu root from a route
  traverseToLayer(
    node: TreeNode<T>,
    targetLayer: Layer,
  ): TreeNode<T> | undefined {
    let currentNode: TreeNode<T> | undefined = node;

    while (currentNode) {
      const parentNode: TreeNode<T> | undefined = currentNode.parentId
        ? this.nodes.get(currentNode.parentId)
        : undefined;

      if (parentNode?.option.layer === targetLayer) {
        return parentNode;
      }

      currentNode = parentNode; // Move upward to the parent
    }

    return undefined; // No matching parent found
  }

  // pathToLayer returns a flat slice of tree nodes up to and inclusive of a layer. User for building active states
  pathToLayer(
    node: TreeNode<T>,
    layer: Layer,
    expanded = false,
  ): Record<number, string | TreeNode<T>> {
    const cached = this.path_cache[node.id];
    if (cached !== undefined) {
      return cached;
    }

    const matchTree: Record<number, string | TreeNode<T>> = {};

    matchTree[node.option.layer!] = expanded ? node : node.id;

    let currentNode: TreeNode<T> | undefined = node;

    while (currentNode) {
      const parentNode: TreeNode<T> | undefined = currentNode.parentId
        ? this.nodes.get(currentNode.parentId)
        : undefined;

      if (parentNode) {
        matchTree[parentNode.option.layer!] = expanded
          ? parentNode
          : parentNode.id;

        if (parentNode.option.layer === layer) {
          this.path_cache[node.id] = matchTree;
          return matchTree;
        }
      }

      currentNode = parentNode; // Move upward to the parent
    }

    return {};
  }

  breadcrumbs(
    node: TreeNode<T>,
    layer: Layer = Layer.Primary,
  ): Array<TreeNode<T>> {
    const matchTree: Array<TreeNode<T>> = [node];

    let currentNode: TreeNode<T> | undefined = node;

    while (currentNode) {
      const parentNode: TreeNode<T> | undefined = currentNode.parentId
        ? this.nodes.get(currentNode.parentId)
        : undefined;

      if (parentNode) {
        matchTree.unshift({ ...parentNode, children: [] });

        if (parentNode.option.layer === layer) {
          return matchTree;
        }
      }

      currentNode = parentNode; // Move upward to the parent
    }

    return [];
  }

  // pathToParent will return a flat array of nodes from the current node to a parent node. Used to build
  // paths for cdk+k menus, etc
  pathToParent(node: TreeNode<T>, parent: TreeNode<T>): Array<TreeNode<T>> {
    const matchTree: Array<TreeNode<T>> = [node];
    let currentNode: TreeNode<T> | undefined = node;

    while (currentNode) {
      const parentNode: TreeNode<T> | undefined = currentNode.parentId
        ? this.nodes.get(currentNode.parentId)
        : undefined;

      if (parentNode) {
        matchTree.unshift({ ...parentNode, children: [] });
      }

      if (parentNode && parentNode.id == parent.id) {
        return matchTree;
      }

      currentNode = parentNode; // Move upward to the parent
    }

    return []; // No matching parent found
  }

  //
  // Visualizing
  //
  // draw will render the navigation tree as a string, like the unix tree command
  draw(starting_id?: string, layers?: boolean): string {
    const buildTree = (
      node: TreeNode<T>,
      prefix = "",
      isLastChild = true,
    ): string => {
      // Draw the current node
      const clickable =
        node.option.route && node.option.route.length > 0 ? "[click] " : "";
      const layer = layers ? `(${Layer[node.option.layer ?? 0]})` : "";

      const nkey = node.option.container ? "<container>" : node.option.key;
      const currentLine = `${prefix}${isLastChild ? "└─ " : "├─ "}${nkey} ${clickable}${layer}\n`;

      // Recurse through children
      const childrenLines = this.resolve(node.children)
        .map((child, index) => {
          const childIsLast = index === node.children.length - 1;
          return buildTree(
            child,
            `${prefix}${isLastChild ? "   " : "│  "}`,
            childIsLast,
          );
        })
        .join("");

      return currentLine + childrenLines;
    };

    if (!starting_id) {
      starting_id = this.root_id;
    }

    // Start with the root node
    const rootNode = this.nodes.get(starting_id);
    if (!rootNode)
      throw new Error("invalid tree structure: root node not found.");

    return buildTree(rootNode);
  }

  // tree will return the fully expanded tree starting from the provided node
  tree(from: TreeNode<T>): TreeNode<T> {
    return this.get(from.id)!;
    // MTL: I am leaving this for now, will clean up in a follow on pr
    //
    // const startingNode = this.get(from.id, false);
    // const recurse = (node: TreeNode<T>): TreeNode<T> => {
    //   const children = this.resolveChildren(node);
    //   const c = children.map(recurse);
    //   if (c && c.length > 0) {
    //     node.children = c;
    //   }

    //   return Object.assign({}, { ...node, children });
    // };

    // return Object.assign({}, { ...recurse(startingNode!) });
  }

  // tree will return the fully expanded tree starting from the provided node
  childrenOf(from: TreeNode<T>): Array<TreeNode<T>> {
    // const t = this.tree(from);
    // return (t.children as Array<TreeNode<T>>) ?? [];

    const t = this.get(from);
    return (t?.children as Array<TreeNode<T>>) ?? [];
  }

  buildSearchTrie(fn: (key: string) => string, entitlements?: T) {
    this.search_trie.build(this, fn, entitlements);
  }

  //
  // Helper/Diagnostic Functions
  //

  getAlternateNode(
    node: string | TreeNode<T>,
    index = 0,
  ): TreeNode<T> | undefined {
    const id = typeof node === "string" ? node : node.id;
    const rnode = this.nodes.get(id);
    const pnode = this.get(rnode?.parentId ?? "");
    const size = pnode?.children?.length ?? 0;

    if (index <= size) {
      return this.get(pnode?.children[index] ?? "");
    }

    return this.get(pnode?.parentId ?? "");
  }

  getParent(node: string | TreeNode<T>): TreeNode<T> | undefined {
    const id = typeof node === "string" ? node : node.id;
    const pnode = this.nodes.get(id);

    return this.get(pnode?.parentId ?? "");
  }

  hasAlternates(
    node: string | TreeNode<T>,
    data?: T,
    layer = Layer.Primary,
  ): boolean {
    if (!node) {
      return false;
    }

    let pnode = this.get(node);
    if (pnode?.option.layer !== layer) {
      pnode = this.getParent(pnode!);
    }

    if (pnode?.option.layer === layer && pnode.option.options?.alternates) {
      if (typeof pnode.option.options?.alternates === "function") {
        return pnode.option.options?.alternates?.(data);
      }
      return pnode.option.options?.alternates;
    }

    return false;
  }

  indexForNode(node: string | TreeNode<T>): number {
    const pnode = this.get(node);
    if (!pnode) {
      return 0;
    }

    return pnode?.option.index ?? 0;
  }

  isClickable(node: TreeNode<T>): boolean {
    return node.option.route !== undefined && node.option.route !== "";
  }

  // routes returns a string array of all unique routes
  routes(): string[] {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [uniqueRoutes, _] = this.routeDetails();
    return uniqueRoutes;
  }

  duplicates(): Record<string, number> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [_, dupes] = this.routeDetails();
    return dupes;
  }

  duplicateRoutes(ignoreList: string[] = []): string[] {
    return Object.keys(this.duplicates()).filter(
      (k) => !ignoreList.includes(k),
    );
  }

  // routeNodes returns a **flat** array of tree nodes
  routeNodes(): Array<TreeNode<T>> {
    const resp = new Set<TreeNode<T>>();

    for (const node of this.nodes.values()) {
      if (node.option.route && node.option.route.length > 0) {
        resp.add(node);
      }
    }
    return Array.from(resp);
  }

  // routeDetails returns unique routes and duplicate routes with counts
  routeDetails(): [string[], Record<string, number>] {
    const uniqueRoutes = new Set<string>();
    const duplicateRoutes: Record<string, number> = {};

    for (const node of this.nodes.values()) {
      if (node.option.route) {
        const route =
          typeof node.option.route === "function"
            ? node.option.route()
            : node.option.route;

        if (route !== "") {
          if (uniqueRoutes.has(route)) {
            if (!duplicateRoutes[route]) {
              duplicateRoutes[route] = 1;
            } else {
              duplicateRoutes[route] = (duplicateRoutes[route] ?? 0) + 1;
            }
          } else {
            uniqueRoutes.add(route);
          }
        }
      }
    }
    return [Array.from(uniqueRoutes), duplicateRoutes];
  }

  //
  // Modification
  //

  setNode(node: TreeNode<T>): void {
    this.nodes.set(node.id, node);
  }

  set(node: TreeNode<T>): TreeNode<T> {
    return this.tree(node);
  }

  //
  // Resolution
  //

  resolveNode(id?: string | TreeNode<T>, fallback?: TreeNode<T>): TreeNode<T> {
    if (!id) {
      return fallback ?? this.root;
    }

    const rnode =
      typeof id == typeof id ? this.resolve(id) : [id as TreeNode<T>];

    if (rnode.length === 0) {
      return fallback ?? this.root;
    }
    return rnode[0]!;
  }

  isContainer(id: string | TreeNode<T>): boolean {
    const rnode =
      typeof id == typeof id ? this.resolve(id) : [id as TreeNode<T>];

    if (!rnode || rnode.length === 0) {
      return false;
    }

    return rnode[0]?.option.key === ContainerKey;
  }

  //
  // Private Functions
  //

  private isParentLayer(node: TreeNode<T>, layer: Layer): boolean {
    return node.option.layer === ((layer - 1) as Layer);
  }

  // shouldResolve determins if node.children is string array or a TreeNode array
  private shouldResolve(nodes: string[] | Array<TreeNode<T>>): boolean {
    if (!Array.isArray(nodes)) {
      return false;
    }
    if (typeof nodes[0] === "string") {
      return true;
    }

    return false;
  }

  // resolveChildren returns children as a TreeNode array
  resolveChildren(
    node: TreeNode<T> | string,
    skipContainers = false,
    alternateIndex = 0,
  ): Array<TreeNode<T>> {
    let rnode = this.resolveNode(node);

    if (alternateIndex > 0 && this.hasAlternates(node)) {
      rnode = this.getAlternateNode(node, alternateIndex)!;
    }

    if (!rnode || rnode.children.length === 0) {
      return [];
    }

    // if (this.shouldResolve(rnode.children)) {
    //   return this.resolve(rnode.children as string[]);
    // }

    const kids = rnode.children as Array<TreeNode<T>>;

    if (skipContainers && kids && this.isContainer(kids[0]!)) {
      return kids[0]?.children as Array<TreeNode<T>>;
    }

    return kids;
  }

  private resolve(
    id: string | string[] | TreeNode<T> | Array<TreeNode<T>>,
  ): Array<TreeNode<T>> {
    const nlist: Array<TreeNode<T>> = [];

    if (typeof id === "object" && !Array.isArray(id)) {
      id = [id];
    }

    if (typeof id === "string") {
      id = [id];
    }

    if (this.shouldResolve(id)) {
      id.forEach((i) => {
        const n = this.nodes.get(i as string);

        if (n) {
          nlist.push(n);
        }
      });

      return nlist;
    }

    return id as Array<TreeNode<T>>;
  }

  //
  // Bootstrap
  //

  // addRouteNote add's the initial root node
  private addRootNode(): string {
    const id = this.generateId();
    const node: TreeNode<T> = {
      id,
      option: { layer: Layer.Root, key: "root", index: 0 },
      children: [],
      expanded: false,
    };

    this.root = node;
    this.nodes.set(id, node);

    return id;
  }

  // generateId returns a new node id.
  // We use a counter vs size since if we delete nodes, we could have collisions
  private generateId = (): string => `${NodeIDPrefix}:${(this.counter += 1)}`;
}
