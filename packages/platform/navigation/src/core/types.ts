import { type Layer } from "./layers";

export type NavigationVisibility =
  | "default"
  | "visible"
  | "hidden"
  | "disabled"
  | "loading"
  | "external"
  | "new";

// we want to uppercase keys for use in a constant
type UppercaseVisibilityRecord = {
  [K in NavigationVisibility as Uppercase<K>]: NavigationVisibility;
};

export const Visibility: UppercaseVisibilityRecord = {
  DEFAULT: "default",
  VISIBLE: "visible",
  HIDDEN: "hidden",
  DISABLED: "disabled",
  LOADING: "loading",
  EXTERNAL: "external",
  NEW: "new",
};

export type VisibilityFn<T> = (data: T) => NavigationVisibility;

export type AddMenuFn = (parentId: string) => void;
export type RouteFn<T> = (data?: T) => string;
export type DataFn<T> = (data?: T) => boolean;

export type FilterFn<T> = (node: TreeNode<T>, entitlements: T) => boolean;

export type NavigationMatch<T> = {
  node: TreeNode<T>;
  parent: TreeNode<T> | undefined;
  rootParent: TreeNode<T> | undefined;
  tree: Array<TreeNode<T>>;
  matchTree: Array<TreeNode<T>>;
};

export type MenuOptions<T> = {
  hideSelector?: boolean;
  alternateLabel?: string;
  alternates?: boolean | DataFn<T>;
  disabledTooltip?: string;
};

export type MenuFn<T> = () => Menu<T>;

export type Menu<T> = {
  container?: boolean;
  layer?: Layer;
  key: string;
  index?: number;
  route?: string | RouteFn<T>;
  icon?: string;
  children?: Array<Menu<T>>;
  visibility?: VisibilityFn<T>;
  disabled?: boolean;
  force_root?: boolean;
  external?: boolean;
  state?: NavigationVisibility;
  options?: MenuOptions<T>;
};

export type TreeNode<T> = {
  id: string;
  option: Menu<T>;
  parentId?: string;
  children: string[] | Array<TreeNode<T>>;
  expanded: boolean;
};

export type NavigationBuilderOptions = {
  issuer?: string;
};
