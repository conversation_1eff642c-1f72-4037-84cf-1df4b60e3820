import { NavigationVisibility, type RouteFn, type TreeNode } from "./types";

function processChildren<T>(node: TreeNode<T>): Array<TreeNode<T>> {
  let list: Array<TreeNode<T>> = [];

  node.children.forEach((c: TreeNode<T> | string) => {
    const cn = c as TreeNode<T>;
    list.push(cn);
    if (cn.children.length > 0) {
      list = [...list, ...processChildren(cn)];
    }
  });

  return list;
}

export function flattenTree<T>(node: TreeNode<T>): Array<TreeNode<T>> {
  let list: Array<TreeNode<T>> = [node];

  if (node.children.length > 0) {
    list = [...list, ...processChildren(node)];
    return [node];
  }

  return list;
}

export function resolveRoute<T>(route: string | RouteFn<T>, data?: T): string {
  if (typeof route === "string") {
    return route;
  }
  return route(data);
}

export function isVisibleState(state: NavigationVisibility): boolean {
  return state === "default" || state === "visible" || state === "new";
}
