import { Layer } from "./layers";
import { Stack } from "./stack";
import { Container<PERSON>ey, NavigationTree } from "./tree";
import {
  type NavigationBuilderOptions,
  type Menu,
  type TreeNode,
  type MenuFn,
} from "./types";

// eslint-disable-next-line
export class NavigationBuilder<T extends object> {
  private tree: NavigationTree<T>;
  private parentStack: Stack<string>;
  private options: NavigationBuilderOptions;
  private entitlements?: T;

  constructor(
    tree?: NavigationTree<T>,
    options: NavigationBuilderOptions = {},
    entitlements?: T,
  ) {
    this.tree = tree ?? new NavigationTree<T>();
    this.parentStack = new Stack();
    this.parentStack.push(this.tree.root_id);
    this.options = options;
    this.entitlements = entitlements;
  }

  OptionStringOr(key: keyof NavigationBuilderOptions, orelse = ""): string {
    return this.options[key] ?? orelse;
  }

  // Pill is sugar around group to make the coding more readable. It represents the top level of navigation
  // TODO: I am not fond of the name, even if it is sugar
  Pill(nav: Menu<T> | string, fn?: () => void): this {
    if (typeof nav === "string") {
      nav = { key: nav } as Menu<T>;
    }
    if (!nav.force_root) {
      nav.force_root = true;
    }
    return this.Group(nav, fn);
  }

  // VerticalTab is sugar around group to make the coding more readable. It represents te veritcal tabs in a mega menu
  // container. NOTE: This forcibly sets the layer to tertiary
  VerticalTab(nav: Menu<T> | string, fn?: () => void): this {
    if (typeof nav === "string") {
      nav = { key: nav } as Menu<T>;
    }
    nav.layer = Layer.Tertiary;

    return this.Group(nav, fn);
  }

  // EmptyTab is sugar around container to make the coding more readable
  EmptyTab(fn: () => void): this {
    // nav.layer = NavigationLayer.Secondary;
    return this.Container(fn);
  }

  // Tab is sugar around group to make the coding more readable
  // TODO: maybe combine tjis with Tab and infer vertical/horizontal given the layer
  Tab(nav: Menu<T> | string, fn?: () => void): this {
    // for now comment this out in case we want tab to be generic
    // nav.layer = NavigationLayer.Secondary;
    return this.Group(nav, fn);
  }

  // Container is sugar around group to make the coding more readable
  Container(fn: () => void, index = 0): this {
    return this.Group({ key: ContainerKey, container: true, index }, fn);
  }

  // Columns is sugar around a container to make the coding more readable
  Columns(fn?: () => void): this {
    // nav.layer = NavigationLayer.Secondary;
    return this.Group({ key: ContainerKey, container: true }, fn);
  }

  // Column is sugar around group to make the coding more readable
  Column(key: Menu<T> | string, fn?: () => void): this {
    return this.Group(key, fn);
  }

  Group(nav?: Menu<T> | string | MenuFn<T>, fn?: () => void): this {
    if (typeof nav === "string") {
      nav = { key: nav } as Menu<T>;
    }

    if (typeof nav === "function") {
      nav = nav();
    }

    // if we do not have a nav instance, create an anonymous container
    if (!nav) {
      nav = { key: ContainerKey, container: true };
    }

    // if we do not have a key, make an anonymous container
    if (!nav?.key) {
      nav.key = ContainerKey;
      nav.container = true;
    }

    const msg = this.validate(nav, true);
    if (msg && msg.length > 0) {
      throw new Error(msg);
    }

    const node = this.tree.insert(
      nav,
      this.parentStack.peek(),
      this.entitlements,
    );

    if (node) {
      this.parentStack.push(node.id);
    }

    if (fn) {
      fn();
    }

    this.parentStack.pop();

    return this;
  }

  Menu(nav: Menu<T>): this {
    return this.Add(nav);
  }

  Add(nav: Menu<T>): this {
    const msg = this.validate(nav, false);
    if (msg && msg.length > 0) {
      console.error("[error] bad menu definition", nav);
      throw new Error(msg);
    }
    this.tree.insert(nav, this.parentStack.peek(), this.entitlements);
    return this;
  }

  //
  // Helpers
  //
  Draw(layers = false): string {
    return this.tree.draw(this.tree.root_id, layers);
  }

  //
  // Validation
  //
  private validate(nav: Menu<T>, group: boolean): string | undefined {
    if (nav.force_root && this.parentStack.peek() !== this.tree.root_id) {
      return `pill "${nav.key}" must only be used when it is the child of the root node.`;
    }
    if (!nav.key && !group) {
      return 'menu option passed to Add is missing tjhe "key" property.';
    }
    if (!nav.route && !group) {
      return `menu option "${nav.key}" passed to Add is missing the "route" property.`;
    }
  }

  // From will populate the stack and allow additions
  From(node: TreeNode<T> | string, fn: () => void): this {
    const fnode = this.tree.resolveNode(node, undefined);

    // TODO: throw
    if (!fnode) {
      return this;
    }

    const nodes = this.tree.pathToParent(fnode, this.tree.root);

    nodes.forEach((n) => this.parentStack.push(n.id));

    fn();
    return this;
  }

  Stack() {
    return this.parentStack;
  }
}
