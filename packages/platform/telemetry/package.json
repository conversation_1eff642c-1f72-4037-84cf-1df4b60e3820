{"name": "@up/telemetry", "version": "0.0.1", "description": "Unified Platform UI Telemetry Package", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test"}, "dependencies": {"@opentelemetry/api": "1.9.0", "@opentelemetry/core": "1.30.1", "@opentelemetry/context-zone": "1.2.0", "@opentelemetry/exporter-trace-otlp-http": "0.55.0", "@opentelemetry/instrumentation": "0.55.0", "@opentelemetry/instrumentation-document-load": "0.42.0", "@opentelemetry/instrumentation-fetch": "0.55.0", "@opentelemetry/resources": "1.28.0", "@opentelemetry/sdk-trace-web": "1.28.0", "@opentelemetry/semantic-conventions": "1.27.0", "@up/std": "workspace:*", "@zs-nimbus/core": "1.2.0", "@zs-nimbus/dataviz-colors": "1.1.0", "@zs-nimbus/foundations": "1.3.0", "clsx": "2.1.1", "react": "18.3.1", "react-aria": "3.37.0", "react-dom": "18.3.1", "tailwind-merge": "2.6.0"}, "devDependencies": {"@chromatic-com/storybook": "catalog:storybook", "@storybook/addon-designs": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/addon-mdx-gfm": "catalog:storybook", "@storybook/addon-onboarding": "catalog:storybook", "@storybook/addon-webpack5-compiler-swc": "catalog:storybook", "@storybook/blocks": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@storybook/react-vite": "catalog:storybook", "@storybook/react-webpack5": "catalog:storybook", "@storybook/test": "catalog:storybook", "@types/node": "22.10.8", "@types/react": "18.3.12", "@types/react-dom": "18.3.0", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitest/ui": "3.0.3", "autoprefixer": "10.4.20", "eslint": "8.57.1", "postcss": "8.4.49", "postcss-import": "16.1.0", "postcss-url": "10.1.3", "prettier": "3.4.2", "storybook": "catalog:storybook", "tailwindcss": "3.4.17", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vitest": "3.1.1"}, "peerDependencies": {"react": ">= 18.3.1", "react-dom": ">= 18.3.1"}, "prettier": "@up/prettier-config"}