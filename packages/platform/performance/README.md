# @up/performance

`@up/performance` is a lightweight, flexible performance tracking library for React applications. It helps capture key metrics like Largest Contentful Paint (LCP) and route change performance, making it easier to monitor and optimize user experience.

## Key Features

- Capture Largest Contentful Paint (LCP) elements and their data-\* attributes.
- Track route change durations with fine-grained detail.
- Lightweight and optimized for use with modern React frameworks like Next.js.

## Installation

### Using pnpm

```bash
pnpm add @up/performance
```

### Using npm

```bash
npm install @up/performance
```

### Using bun

```bash
bun add @up/performance
```

## Prerequisites

Ensure you have React 18.3.1 or later installed as a peer dependency.

## 1. Getting Started with `useLCPElementLogger`

The `useLCPElementLogger` hook captures the LCP element and logs its data-\* attributes if a specific sessionStorage flag is enabled.

### Setup

Add the following hook to your component or app entry point to enable LCP logging:

```tsx
import { useLCPElementLogger } from "@up/performance";

export function App() {
  useLCPElementLogger();

  return (
    <div>
      <h1 data-testid="hero-image">Welcome to the App</h1>
      <p>Track your app's LCP element effortlessly.</p>
    </div>
  );
}
```

### Enabling LCP Logging

To enable LCP logging, set the following sessionStorage key in the browser console or your code:

```js
sessionStorage.setItem("GET_LCP_ELEMENT", "true");
```

### Expected Output

Once the LCP element is detected, you should see logs like:

```
🟢 [LCP Element]: <h1 data-testid="hero-image">Welcome to the App</h1>
📋 [LCP Element data-* attribute selectors]: [data-testid="hero-image"]
```

## 2. Using `RouteChangePerformanceProvider`

The `RouteChangePerformanceProvider` component captures route change performance and triggers a callback with detailed metrics.

### Setup

Wrap your top-level component (e.g., `_app.tsx` or `App.tsx`) with the provider:

```tsx
import { RouteChangePerformanceProvider } from "@up/performance";

export default function MyApp({ Component, pageProps }) {
  const handlePerformanceDetails = (duration, details) => {
    console.log("Route Change Duration:", duration);
    console.log("Details:", details);
  };

  return (
    <RouteChangePerformanceProvider
      onPerformanceDetails={handlePerformanceDetails}
    >
      <Component {...pageProps} />
    </RouteChangePerformanceProvider>
  );
}
```

### Expected Output

When a route change is detected, you will see logs similar to:

```
Route Change Duration: 123ms
Details: { from: '/home', to: '/about' }
```

## 3. Using `NavigationPerformanceTracker`

The `NavigationPerformanceTracker` component starts measuring navigation performance when a specific DOM element appears.

### Setup

Add the tracker to the parent component responsible for rendering the primary content:

```tsx
import { NavigationPerformanceTracker } from "@up/performance";

export default function CyberSecurityParentComponent() {
  return (
    <div>
      <h2>Cyber Security</h2>
      <NavigationPerformanceTracker selector="[data-testid='cybersecurity-your-cs-trans-card-description']" />
      <CyberSecurityComponent />
    </div>
  );
}

function CyberSecurityComponent() {
  return (
    <p data-testid="cybersecurity-your-cs-trans-card-description">
      Protect your data with our advanced cybersecurity solutions.
    </p>
  );
}
```

### Expected Output

When the specified element appears in the DOM, the performance tracking will start for that element.

## Contributing

Contributions are welcome! If you have ideas to improve this library, feel free to open a PR or start a discussion. Let's make performance tracking easier, together.
