/**
 * Patches the browser's history events to dispatch custom events when the history state changes.
 * This function is a no-op if the window object is not defined.
 *
 * @returns {void}
 */
export function patchHistoryEvents() {
  if (typeof window === "undefined") return; // Exit early if not running in a browser environment

  /**
   * The methods of the History API that we want to patch.
   * @type {Array<keyof History>}
   */
  const methods: Array<keyof History> = ["pushState", "replaceState"];

  methods.forEach((method) => {
    const original = history[method] as (...args: any[]) => any;

    (history as any)[method] = function (...args: any[]) {
      const result = original.apply(this, args);
      // Create a new event with the name of the method that was called.
      const event = new Event(method);
      // Dispatch the event on the window object.
      window.dispatchEvent(event);
      return result;
    };
  });
}
