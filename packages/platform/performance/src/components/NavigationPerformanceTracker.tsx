import { useState } from "react";
import { useNavigationPerformanceMeasure } from "../hooks/navigation-performance-measure";
import { useWaitForSelector } from "../hooks/wait-for-selector";

type NavigationPerformanceTrackerProps = {
  /**
   * CSS selector for the target DOM element that signifies page render completion.
   * Example: '[data-testid="lcp-element"]'
   */
  selector: string;
};

/**
 * Tracks navigation performance and starts measurement
 * when the specified DOM element appears in the document.
 */
export function NavigationPerformanceTracker({
  selector,
}: NavigationPerformanceTrackerProps) {
  const [shouldMeasure, setShouldMeasure] = useState(false);

  useWaitForSelector(
    selector,
    () => {
      setShouldMeasure(true);
    },
    true,
  );

  useNavigationPerformanceMeasure({ enabled: shouldMeasure });

  return null;
}
