import { useRouteChangeMarker } from "../hooks/route-change-marker";
import { useRouteChangePerformanceObserver } from "../hooks/route-change-performance-observer";
import { MEASURE_TYPE } from "../constants";
import { type NavPerformanceDetails } from "../types";

/**
 * Props for the RouteChangePerformanceProvider component.
 */
type RouteChangePerformanceProviderProps = {
  /**
   * A callback function that will be called with performance details when a route change occurs.
   * @param duration The duration of the route change in milliseconds.
   * @param detail Navigation performance details.
   */
  onPerformanceDetails: (
    duration: number,
    detail: NavPerformanceDetails,
  ) => void;
  /**
   * A flag to enable or disable the route change performance tracking.
   * @default true
   */
  enabled?: boolean;
};

/**
 * A provider component that tracks route change performance and calls the provided callback with performance details.
 *
 * @param props RouteChangePerformanceProviderProps
 * @returns {null} This provider does not render any content.
 */
export function RouteChangePerformanceProvider({
  enabled = true,
  onPerformanceDetails,
}: RouteChangePerformanceProviderProps) {
  // Enable route change marker to track route changes.
  useRouteChangeMarker({ enabled });

  /**
   * Create a performance observer to monitor route changes and track performance metrics.
   * The observer will call the provided callback with the measure entry, its duration, and any available details.
   */
  useRouteChangePerformanceObserver(
    MEASURE_TYPE,
    (
      duration: number,
      _entry: PerformanceMeasure,
      detail?: NavPerformanceDetails,
    ) => {
      try {
        // Ignore if no detail is available.
        if (!detail) return;
        // Call the callback with performance details.
        onPerformanceDetails(duration, detail);
      } catch (error) {
        // Log any error that occurs while logging navigation performance.
        console.error("Error logging navigation performance:", error);
      }
    },
  );

  // This provider does not render any content.
  return null;
}
