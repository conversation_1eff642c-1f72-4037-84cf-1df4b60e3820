import { useEffect, useRef } from "react";
import { ROUTE_CHANGE_START } from "../constants";

/**
 * Options for the useRouteChangeMarker hook.
 */
type UseRouteChangeMarkerOptions = {
  /**
   * Whether the hook is enabled.
   * @default true
   */
  enabled?: boolean;
};

/**
 * Returns the current URL of the page.
 *
 * If the function is called in a non-browser environment (i.e., `window` is
 * undefined), an empty string is returned.
 *
 * @returns {string} The current URL of the page.
 */
function getCurrentUrl(): string {
  if (typeof window === "undefined") return "";
  const { pathname, search } = window.location;
  return search ? `${pathname}${search}` : pathname;
}

/**
 * Determines if the current navigation was a page load/reload.
 *
 * @returns {boolean} True if the navigation was a reload or direct page load, false otherwise.
 */
function isPageLoadOrReload(): boolean {
  const [entry] = performance.getEntriesByType(
    "navigation",
  ) as PerformanceNavigationTiming[];
  return entry?.type === "reload" || entry?.type === "navigate";
}

/**
 * useRouteChangeMarker Hook
 *
 * This hook is used to mark route changes in the application.
 * It listens for 'popstate', 'pushState', and 'replaceState' events on the window object.
 * When a route change is detected, it marks the start of the route change using the 'performance.mark' API.
 *
 * @param {UseRouteChangeMarkerOptions} [options={}] Options for the hook.
 * @returns {void}
 */
export function useRouteChangeMarker(
  options: UseRouteChangeMarkerOptions = { enabled: true },
): void {
  // Store the previous URL and its load type (pageload/reload or soft navigation)
  const previousUrlRef = useRef<string | null>(getCurrentUrl());
  const previousWasPageLoadRef = useRef<boolean>(isPageLoadOrReload());

  useEffect(() => {
    // If the hook is disabled, do nothing.
    if (!options.enabled) return;

    /**
     * Handle route change event.
     * Marks the start of the route change using the 'performance.mark' API.
     */
    const handleRouteChange: EventListener = () => {
      const currentUrl = getCurrentUrl();
      // Skip marking if the navigation is to the same page.
      if (previousUrlRef.current === currentUrl) return;

      // Only set the marker if the previous page was a page load or reload.
      if (previousWasPageLoadRef.current) {
        // Mark the start of the route change with the 'from' and 'to' URLs.
        performance.mark(ROUTE_CHANGE_START, {
          detail: {
            from: previousUrlRef.current,
            to: currentUrl,
          },
        });
      }

      // Update the previous URL ref and mark the current page as a soft navigation.
      previousUrlRef.current = currentUrl;
      // Reset to soft navigation after first route change
      previousWasPageLoadRef.current = false;
    };

    // Listen for 'popstate', 'pushState', and 'replaceState' events on the window object.
    window.addEventListener("popstate", handleRouteChange);
    window.addEventListener("pushState", handleRouteChange);
    window.addEventListener("replaceState", handleRouteChange);

    return () => {
      window.removeEventListener("popstate", handleRouteChange);
      window.removeEventListener("pushState", handleRouteChange);
      window.removeEventListener("replaceState", handleRouteChange);
    };
  }, [options.enabled]);
}
