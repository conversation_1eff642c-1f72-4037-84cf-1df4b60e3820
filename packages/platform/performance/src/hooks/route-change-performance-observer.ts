import { useEffect, useMemo } from "react";
import { ROUTE_CHANGE_START } from "../constants";
import { type NavPerformanceDetails } from "../types";

/**
 * Hook to observe route change performance measures.
 *
 * @param {string} measureName - The name of the performance measure to observe.
 * @param {RouteChangeCallback} callback - The callback to execute on measure.
 * @returns {void}
 */
export function useRouteChangePerformanceObserver(
  measureName: string,
  callback: (
    duration: number,
    entry: PerformanceMeasure,
    detail?: NavPerformanceDetails,
  ) => void,
) {
  const observer = useMemo(
    () =>
      new PerformanceObserver((list) => {
        const entries = list.getEntriesByName(measureName);

        entries.forEach((entry) => {
          const measureEntry = entry as PerformanceMeasure;

          const startMark = getStartMark(measureEntry);

          callback(
            measureEntry.duration,
            measureEntry,
            startMark?.detail as NavPerformanceDetails,
          );

          performance.clearMeasures(measureEntry.name);

          /**
           * Clearing route-change-start mark to not to measure further
           * without creating new.
           **/
          performance.clearMarks(ROUTE_CHANGE_START);
        });
      }),
    [callback, measureName],
  );

  useEffect(() => {
    observer.observe({ type: "measure", buffered: true });
    return () => observer.disconnect();
  }, [observer]);

  useEffect(() => {
    performance.clearMeasures(measureName);
  }, [measureName]);
}

/**
 * Get the start mark corresponding to a performance measure.
 *
 * @param {PerformanceMeasure} measureEntry - The measure to match with a mark.
 * @returns {PerformanceMark | undefined} The corresponding start mark, if found.
 */
function getStartMark(
  measureEntry: PerformanceMeasure,
): PerformanceMark | undefined {
  const startMarks: PerformanceMark[] = performance
    .getEntriesByName(ROUTE_CHANGE_START, "mark")
    .filter(
      (mark): mark is PerformanceMark =>
        mark.entryType === "mark" && mark.startTime === measureEntry.startTime,
    );

  return startMarks[0];
}
