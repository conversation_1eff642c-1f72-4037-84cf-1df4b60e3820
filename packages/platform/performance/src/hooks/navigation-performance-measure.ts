import {
  ELEMENT_RENDER_END,
  MEASURE_TYPE,
  ROUTE_CHANGE_START,
} from "../constants";
import { usePerformanceMeasure } from "./performance-measure";

/**
 * Options for the useNavigationPerformanceMeasure hook.
 */
type NavigationPerformanceMeasureOptions = {
  /**
   * Whether to enable navigation performance measurement.
   * @default true
   */
  enabled?: boolean;
};

/**
 * Hook to measure navigation performance.
 *
 * @param {NavigationPerformanceMeasureOptions} [options] Options for the hook.
 * @returns {void}
 */
export const useNavigationPerformanceMeasure = (
  options: NavigationPerformanceMeasureOptions = { enabled: false },
): void => {
  usePerformanceMeasure(MEASURE_TYPE, ROUTE_CHANGE_START, ELEMENT_RENDER_END, {
    enabled: options.enabled,
  });
};
