import { useEffect } from "react";

/**
 * Extracts all data-* attributes from an element as an array of selector strings.
 * Example: [data-testid="hero-image"]
 */
function getDataAttributeSelectors(el: Element): string[] {
  return Array.from(el.attributes)
    .filter((attr) => attr.name.startsWith("data-"))
    .map((attr) => `[${attr.name}="${attr.value}"]`);
}

/**
 * Hook to log the Largest Contentful Paint (LCP) element
 * and its data-* attributes if a sessionStorage flag is enabled.
 *
 * This is useful for debugging which element is considered
 * the LCP candidate by the browser during page load.
 *
 * @param flagKey The sessionStorage key to enable LCP logging. Default is 'GET_LCP_ELEMENT'.
 *
 * To enable logging in the browser:
 * ```js
 * sessionStorage.setItem('GET_LCP_ELEMENT', 'true');
 * ```
 */
export function useLCPElementLogger(flagKey = "GET_LCP_ELEMENT"): void {
  useEffect(() => {
    if (typeof window === "undefined") return;

    const shouldLog = sessionStorage.getItem(flagKey) === "true";
    if (!shouldLog) return;

    const observer = new PerformanceObserver(
      (list: PerformanceObserverEntryList) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];

        if (
          lastEntry?.entryType === "largest-contentful-paint" &&
          "element" in lastEntry
        ) {
          const lcpEntry = lastEntry as LargestContentfulPaint & {
            element?: Element;
          };

          const el = lcpEntry.element;
          if (el) {
            console.log("🟢 [LCP Element]:", el);

            const selectors = getDataAttributeSelectors(el);
            console.log(
              "📋 [LCP Element data-* attribute selectors]:",
              selectors,
            );
          }
        }
      },
    );

    observer.observe({ type: "largest-contentful-paint", buffered: true });

    return () => observer.disconnect();
  }, [flagKey]);
}
