import { useEffect } from "react";

/**
 * Hook that observes the DOM for an element matching the selector and triggers a callback once it appears.
 *
 * @param selector - CSS selector to observe.
 * @param onFound - Callback fired when the element is found.
 * @param once - If true, disconnects observer after first match (default: true).
 */
export function useWaitForSelector(
  selector: string,
  onFound: (el: Element) => void,
  once = true,
) {
  useEffect(() => {
    if (typeof window === "undefined") return;
    if (
      !selector ||
      typeof selector !== "string" ||
      selector.trim().length === 0
    )
      return;

    const existing = document.querySelector(selector);
    if (existing) {
      onFound(existing);
      return;
    }

    const observer = new MutationObserver(() => {
      const el = document.querySelector(selector);
      if (el) {
        onFound(el);
        if (once) observer.disconnect();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [selector, onFound, once]);
}
