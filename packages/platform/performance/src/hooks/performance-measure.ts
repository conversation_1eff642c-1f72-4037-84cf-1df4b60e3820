import { useEffect } from "react";

/**
 * Options for the usePerformanceMeasure hook.
 */
type UsePerformanceMeasureOptions = {
  /**
   * Whether the hook is enabled.
   * @default true
   */
  enabled?: boolean;
};

/**
 * usePerformanceMeasure hook.
 *
 * This hook uses the performance API to measure the time between two marks.
 * It checks if the start mark exists before measuring the performance.
 *
 * @param {string} measureName The name of the performance measure.
 * @param {string} startMarkName The name of the start mark.
 * @param {string} endMarkName The name of the end mark.
 * @param {UsePerformanceMeasureOptions} [options] Options for the hook.
 * @returns {void}
 */
export const usePerformanceMeasure = (
  measureName: string,
  startMarkName: string,
  endMarkName: string,
  options?: UsePerformanceMeasureOptions,
) => {
  useEffect(() => {
    // If the hook is disabled, do nothing
    if (!options?.enabled) return;

    try {
      // Create the end mark
      performance.mark(endMarkName);

      // Check for start mark existence at the time of measurement
      const startMarkExists =
        performance.getEntriesByName(startMarkName, "mark").length > 0;

      // If the start mark exists, measure the performance
      if (startMarkExists) {
        performance.measure(measureName, startMarkName, endMarkName);
      }
    } catch (error) {
      // Log any errors that occur during measurement
      console.error("Error measuring performance:", error);
    }
  }, [startMarkName, endMarkName, measureName, options?.enabled]);
};
