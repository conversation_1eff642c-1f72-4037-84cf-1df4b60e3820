{"name": "@up/performance", "version": "0.0.1", "description": "Performance metrics", "main": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "private": false, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint"}, "peerDependencies": {"react": "18.3.1"}, "devDependencies": {"@types/node": "22.13.5", "@types/react": "18.3.12", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "eslint": "8.57.1", "prettier": "3.5.2", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3"}, "prettier": "@up/prettier-config"}