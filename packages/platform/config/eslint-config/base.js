/** @type {import("eslint").Linter.Config} */
module.exports = {
  parser: "@typescript-eslint/parser",
  extends: [
    "eslint:recommended",
    "prettier",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "plugin:prettier/recommended",
    "plugin:@typescript-eslint/recommended-type-checked",
    "plugin:@typescript-eslint/stylistic-type-checked",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:react/jsx-runtime",
    "plugin:jsx-a11y/recommended",
  ],
  plugins: [
    "jsx-a11y",
    "typescript-sort-keys",
    "prettier",
    "@typescript-eslint",
    "@up/unified-platform",
  ],
  globals: {
    React: true,
    JSX: true,
  },
  env: {
    node: true,
  },
  parserOptions: {
    // projectService: true,
    tsconfigRootDir: __dirname,
  },
  settings: {
    "import/resolver": {
      typescript: true,
      node: true,
    },
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"],
    },
    react: {
      version: "detect"
    }
  },
  rules: {
    "prettier/prettier": "error",
    "no-underscore-dangle": "off",
    "no-use-before-define": "off",
    "no-continue": "off",
    "no-param-reassign": "off",
    "arrow-body-style": "off",
    "prefer-arrow-callback": "off",
    "no-shadow": "warn",
    "no-restricted-syntax": "off",
    "no-plusplus": [
      "error",
      {
        allowForLoopAfterthoughts: true,
      },
    ],
    "no-unused-expressions": [
      "error",
      {
        allowShortCircuit: true,
        allowTernary: true,
      },
    ],
    indent: [
      "warn",
      2,
      {
        ignoredNodes: ["TemplateLiteral"],
        SwitchCase: 1,
      },
    ],
    "typescript-sort-keys/interface": 0,
    "typescript-sort-keys/string-enum": 0,
    "@up/unified-platform/max-file-lines": 1,
  },
  ignorePatterns: [
    // Ignore dotfiles
    ".*.js",
    "node_modules/",
    "dist/",
  ],
  overrides: [
    {
      files: ["*.js?(x)", "*.ts?(x)"],
    },
  ],
};
