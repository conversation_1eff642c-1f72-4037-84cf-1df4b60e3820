{"name": "@up/eslint-config", "version": "0.0.8", "private": false, "files": ["library.js", "component-library.js", "nextjs.js", "base.js"], "peerDependencies": {"prettier": "3.5.2", "eslint": "8.57.1"}, "dependencies": {"@typescript-eslint/eslint-plugin": "8.25.0", "@typescript-eslint/parser": "8.25.0", "@up/eslint-plugin-unified-platform": "workspace:*", "eslint-config-next": "15.1.6", "eslint-config-prettier": "10.0.2", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-storybook": "0.11.3", "eslint-plugin-typescript-sort-keys": "3.3.0", "typescript": "5.7.3"}}