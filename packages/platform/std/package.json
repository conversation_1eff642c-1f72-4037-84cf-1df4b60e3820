{"name": "@up/std", "version": "0.0.2", "description": "Unified Platform (UP) Standard Library", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:watch": "vitest --watch", "format": "pnpm prettier:config --write", "prettier": "pnpm prettier:config --check", "prettier:config": "prettier 'src/**/*.ts'", "lint": "eslint 'src/**/*.ts'", "lint:fix": "eslint 'src/**/*.ts' --fix"}, "files": ["./dist"], "devDependencies": {"@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@typescript-eslint/parser": "8.25.0", "@typescript-eslint/eslint-plugin": "8.25.0", "@vitest/ui": "3.0.3", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "eslint": "8.57.1", "prettier": "3.5.2", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vitest": "3.1.1"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}, "volta": {"extends": "../../../package.json"}, "prettier": "@up/prettier-config"}