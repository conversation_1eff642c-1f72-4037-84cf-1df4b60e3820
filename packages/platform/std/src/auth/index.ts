import { HOUR_MS, MINUTE_MS } from "../time";
import { ServiceInfo } from "../types";

export const BEARER_TOKEN = "bearer-token";

export const BEARER_TOKEN_EXPIRY = "bearer-token-expiry";

// eslint-disable-next-line @typescript-eslint/no-inferrable-types
export const TokenUpdatedEvent: string = "auth-token-updated";

export const getBearerToken = (): string | null => {
  if (
    (process.env.NODE_ENV === "development" || process.env.NEXT_PUBLIC_LOCAL) &&
    process.env.NEXT_PUBLIC_TOKEN
  ) {
    return process.env.NEXT_PUBLIC_TOKEN ?? null;
  } else {
    return sessionStorage.getItem(BEARER_TOKEN) ?? null;
  }
};

export const getBearerTokenExpiry = () => {
  if (process.env.NEXT_PUBLIC_LOCAL_SERVICE) {
    return sessionStorage.getItem(BEARER_TOKEN_EXPIRY) ?? null;
  } else if (
    process.env.NODE_ENV === "development" ||
    process.env.NEXT_PUBLIC_LOCAL
  ) {
    return (Date.now() + HOUR_MS) / 1000;
  }

  return sessionStorage.getItem(BEARER_TOKEN_EXPIRY) ?? null;
};

export const setBearerToken = (bearerToken: string, expiry: string) => {
  // dont set anything in memory on local development
  if (process.env.NODE_ENV === "development" && process.env.NEXT_PUBLIC_TOKEN) {
    return;
  }
  sessionStorage.setItem(BEARER_TOKEN, bearerToken);

  if (expiry) {
    sessionStorage.setItem(BEARER_TOKEN_EXPIRY, expiry);
  }

  dispatchEvent(
    new CustomEvent(TokenUpdatedEvent, {
      detail: { token: bearerToken },
    }),
  );
};

export const isTokenValid = () => {
  const expiry = getBearerTokenExpiry();
  if (!expiry) return false;
  const diff = Number(expiry) * 1000 - Date.now();

  return !(!expiry || diff < MINUTE_MS);
};

export const clearTokenFromStorage = () => {
  sessionStorage.removeItem(BEARER_TOKEN);
  sessionStorage.removeItem(BEARER_TOKEN_EXPIRY);
};

export const decodeBearerToken = (): Record<string, unknown> => {
  const token = getBearerToken();
  if (token) {
    const [_headerB64, payloadB64] = token.split(".");
    if (!payloadB64) {
      return {};
    }
    const payload = payloadB64.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      atob(payload)
        .split("")
        .map(function (c) {
          return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join(""),
    );

    return JSON.parse(jsonPayload) as Record<string, unknown>;
  }

  return {};
};

export function tenantIdFromToken(): string {
  const payload = decodeBearerToken();
  if (!payload.tenantId) {
    return "";
  }

  return payload.tenantId as string;
}

export const getSessionInfo = (): ServiceInfo[]=> {
    const parseToken = decodeBearerToken();
    return parseToken?.["service-info"] as ServiceInfo[];
  };