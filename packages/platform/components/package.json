{"name": "@up/components", "version": "0.0.5", "description": "A frontend Component Package.", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test", "lost-pixel": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel", "lost-pixel:update": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel update --configDir ./.lostpixel/config"}, "dependencies": {"@tanstack/react-table": "8.21.2", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "react": "18.3.1", "react-aria": "3.37.0", "react-aria-components": "1.7.1", "react-dom": "18.3.1", "react-i18next": "15.0.2", "tailwind-merge": "2.6.0", "lodash.merge": "4.6.2"}, "devDependencies": {"@chromatic-com/storybook": "catalog:storybook", "@storybook/addon-designs": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/addon-mdx-gfm": "catalog:storybook", "@storybook/addon-onboarding": "catalog:storybook", "@storybook/addon-webpack5-compiler-swc": "catalog:storybook", "@storybook/addon-styling-webpack": "catalog:storybook", "@storybook/blocks": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@storybook/react-vite": "catalog:storybook", "@storybook/react-webpack5": "catalog:storybook", "@storybook/test": "catalog:storybook", "@types/lodash.merge": "4.6.9", "@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitest/ui": "3.0.3", "autoprefixer": "10.4.20", "css-loader": "7.1.2", "style-loader": "3.3.3", "eslint": "8.57.1", "lost-pixel": "3.22.0", "postcss": "8.5.3", "postcss-loader": "8.1.1", "postcss-url": "10.1.3", "prettier": "3.5.2", "storybook": "catalog:storybook", "tailwindcss": "3.4.17", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vitest": "3.1.1"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1", "@zs-nimbus/core": "1.2.0", "@zs-nimbus/dataviz-colors": "1.1.0", "@zs-nimbus/foundations": "1.3.0", "@fortawesome/fontawesome-pro": "6.5.1", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/free-brands-svg-icons": "6.6.0", "@fortawesome/free-solid-svg-icons": "6.6.0", "@fortawesome/pro-light-svg-icons": "6.5.1", "@fortawesome/pro-regular-svg-icons": "6.5.1", "@fortawesome/pro-solid-svg-icons": "6.5.1", "@fortawesome/react-fontawesome": "0.2.2", "@amcharts/amcharts5": "5.10.5", "@amcharts/amcharts5-geodata": "5.1.4"}, "prettier": "@up/prettier-config", "publishConfig": {"registry": "https://nexus.corp.zscaler.com/repository/up-npm-hosted/"}}