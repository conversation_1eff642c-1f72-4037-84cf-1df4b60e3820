import type * as am5 from "@amcharts/amcharts5";
import { type ILabelSettings } from "@amcharts/amcharts5";
import type * as am5xy from "@amcharts/amcharts5/xy";

export type ThemeType = "light" | "dark";

export type ColorsUseType = {
  fillColor?: string;
  strokeColor?: string;
  shadowFillColor?: string;
  shadowStrokeColor?: string;
};

export type BackgroundSetting = ColorsUseType & am5.IGraphicsSettings;
export type ContainerSetting = am5.IContainerSettings & {
  backgroundSetting?: BackgroundSetting;
};

export type TooltipBackgroundSetting = am5.IPointedRectangleSettings &
  ColorsUseType;

export type CommonTooltipData = {
  label: string;
  key?: string | number;
  value?: string | number;
  valuePercent?: number | string;
  icon?: string;
  link?: string;
  data?: CommonTooltipData[];
  sub_data?: CommonTooltipData[];
  container_class?: string;
};

export type ChartProps = {
  chartSettings?: am5xy.IXYChartSettings;
  leftContainer?: am5.IContainerSettings;
  rightContainer?: am5.IContainerSettings;
  chartContainer?: am5.IContainerSettings;
  bottomAxesContainer?: am5.IContainerSettings;
  topAxesContainer?: am5.IContainerSettings;
  gridContainer?: am5.IContainerSettings;
  chartBackgroundSetting?: BackgroundSetting;
};

export type GradientStop = {
  color: string;
  opacity?: number;
  offset?: number;
};

export type GradientProps = {
  stops: GradientStop[];
  showGradient?: boolean;
  rotation?: number;
};

export type LineProps = am5.IGraphicsSettings & {
  lineColor: string;
  gradientProps?: GradientProps;
};

export type DualAxisLabelProps = { text: string; y: number; fill: string };

export type DualYAxisProps = {
  showDualAxis?: boolean;
  gradientProps?: GradientProps;
  labels?: DualAxisLabelProps[];
  rightContainerProps?: am5.IContainerSettings;
  yAxisProps?: am5xy.IAxisRendererYSettings;
};

export type CursorProps = {
  cursorSetting?: am5xy.IXYCursorSettings;
  showCursor?: boolean;
  yCursorSetting?: am5xy.IXYCursorSettings & { strokeColor?: string };
  xCursorSetting?: am5xy.IXYCursorSettings & { strokeColor?: string };
};

export type LegendDirection = "right" | "bottom" | "left" | "up";

export type CustomLegendData = {
  name?: string;
  legendIcon?: string;
};

export type LegendsProps = {
  showLegend?: boolean;
  customLegend?: boolean;
  gridLayout?: boolean;
  legendsData?: CustomLegendData[];
  direction?: LegendDirection;
  showCustomIcon?: boolean;
  legendMarkerProps?: am5.IContainerSettings;
  legendLabelProps?: am5.ILabelSettings & { legendLabelColor?: string };
  legendItemContainerProps?: am5.ILegendSettings;
  xLegendPosition?: number;
  yLegendPosition?: number;
  legendSetting?: am5.ILegendSettings;
  legendMarkerSetting?: am5.ILabelSettings;
  legendLabelsSetting?: am5.ILabelSettings;
  legendContainerSetting?: am5.IContainerSettings;
  /**
   * am5.IRoundedRectangleSettings extends am5.IRectangleSettings
   * so we can use IRoundedRectangleSettings which have both features
   * of IRoundedRectangleSettings and IRectangleSettings
   * */
  legendRectangleProps?: am5.IRoundedRectangleSettings;
  legendRectangle?: am5.IRoundedRectangleSettings;
};

export type LegendType<T> = {
  chart: T;
  root: am5.Root;
  legendsProps: LegendsProps;
};

export type ZoomControlProps = {
  minusButtonBgProps?: am5.ILabelSettings;
  plusButtonBgProps?: am5.ILabelSettings;
};

export type WorldMapZoomControlProps = {
  minusButtonBgProps?: ILabelSettings;
  plusButtonBgProps?: ILabelSettings;
};

export type ChartTooltipUiProp = {
  key?: string;
  title?: string;
  titleClass?: string;
  classes?: string;
  iconClass?: string;
  value?: string | number;
  valueClass?: string;
  data?: ChartTooltipUiProp[];
  yAxisDataKey?: string;
};

export type Tooltip = {
  tooltipSettingsConfig?: am5.ITooltipSettings;
  tooltipGraphicsConfig?: Omit<
    am5.IGraphicsSettings,
    "fill" | "fillOpacity"
  > & {
    tooltipBackgroundOpacity?: number;
  };
  tooltipLabelConfig?: am5.ILabelSettings;
  tooltipHTML?: string;
  tooltipData?: CommonTooltipData[];
  renderTooltip?: (d: CommonTooltipData[]) => string;
};
