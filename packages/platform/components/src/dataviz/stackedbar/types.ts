import type * as am5 from "@amcharts/amcharts5";
import type * as am5xy from "@amcharts/amcharts5/xy";
import { type Tooltip, type ThemeType, LegendsProps } from "../common/types";

export type StackedBarSeries = {
  label: string;
  propertyName: string;
  colorCode: string;
};

export type StackedBarChartColors = {
  labelColor: string;
  tooltipLabelColor: string;
  tooltipStrokeColor: string;
  tooltipFillColor: string;
  tooltipShadowColor?: string;
};

export type StackedBarDefaultProps = {
  containerClass?: string;
  tooltipProps: Tooltip;
  gridContainerProps?: am5.IContainerSettings;

  chartColors: StackedBarChartColors;
  columnProps?: am5.IRoundedRectangleSettings;
  legendProps?: LegendsProps;

  xGridProps?: am5xy.IGridSettings;
  yGridProps?: am5xy.IGridSettings;
  yAxisProps?: Omit<
    am5xy.ICategoryAxisSettings<am5xy.AxisRenderer>,
    "categoryField" | "renderer"
  >;
  xAxisProps?: Omit<am5xy.IValueAxisSettings<am5xy.AxisRenderer>, "renderer">;
  xAxisLabelProps?: am5xy.IAxisLabelSettings;
  xAxisGridProps?: am5xy.IAxisRendererXSettings;
  chartLabelProps?: am5.ILabelSettings;
};

export type StackedBarConfig = {
  stackedBarProps: Omit<
    StackedBarDefaultProps,
    "tooltipProps" | "chartColors" | "legendProps"
  > & {
    tooltipProps?: Tooltip;
    chartColors?: StackedBarChartColors;
    legendProps?: LegendsProps;
  };
};

export type StackedBarProps = {
  id?: string;
  data: {
    chartData: Array<{
      total: number;
      id: string;
      [a: string]: string | number;
    }>;
    seriesData: StackedBarSeries[];
  };
  dataKey: string;
  customConfig: StackedBarConfig;
  chartId?: string;
  theme: ThemeType;
  onBarClick?: (
    data: StackedBarProps["data"]["chartData"],
    valueXField: string,
  ) => void;
};
