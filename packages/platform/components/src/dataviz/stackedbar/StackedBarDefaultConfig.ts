import { ThemeType } from "../common/types";
import { StackedBarDefaultProps } from "./types";
import { colors } from "@zs-nimbus/foundations";

export const getDefaultConfig = (theme: ThemeType): StackedBarDefaultProps => {
  return {
    containerClass: "flex h-[220px]",
    legendProps: {
      legendContainerSetting: {
        maxHeight: 50,
      },
      legendLabelProps: {
        marginRight: 14,
        fontSize: 14,
        fontWeight: "400",
      },
      xLegendPosition: 0,
      yLegendPosition: 0,
      legendMarkerProps: {
        width: 8,
        height: 8,
      },
      legendSetting: {
        maxHeight: 100,
      },
    },
    columnProps: {
      height: 55,
    },
    chartColors: {
      labelColor: colors[theme].content.base.tertiary,
      tooltipLabelColor: colors[theme].background.primary,
      tooltipStrokeColor: colors[theme].surface.fields.default,
      tooltipFillColor: colors[theme].background.primary,
      tooltipShadowColor: "rgba(0,0,0,1)", // No Nimbus equivalent
    },
    tooltipProps: {
      tooltipSettingsConfig: {
        pointerOrientation: "down",
      },
      tooltipLabelConfig: {
        fontSize: 12,
      },
      tooltipHTML: ` 
                          <span class="text-semantic-content-base-primary typography-paragraph1-strong">{name}</span>
                          <br/>
                          <span class="text-semantic-content-base-primary typography-paragraph1">{valueX}</span>
                     `,
    },
  };
};
