import { type StackedBarProps } from "./types";

export const StackedBarData: StackedBarProps["data"] = {
  chartData: [
    {
      id: "notifications",
      lowMemory: 523,
      highCPU: 300,
      wifiIssues: 177,
      total: 523 + 300 + 177,
    },
  ],
  seriesData: [
    {
      label: "Low Memory",
      propertyName: "lowMemory",
      colorCode: "rgba(25, 76, 187, 1)",
    },
    {
      label: "High CPU",
      propertyName: "highCPU",
      colorCode: "rgba(37, 186, 226, 1)",
    },
    {
      label: "Wifi Issues",
      propertyName: "wifiIssues",
      colorCode: "rgba(159, 70, 215, 1)",
    },
  ],
};
