import { Switch } from "react-aria-components";
import { useEffect, useState } from "react";
import { cn } from "../common/utils";
import {
  containerVaraints,
  labelVariants,
  toggleVariants,
} from "./ToggleStyles";

export type ToggleSize = "md";

type ToggleProps = {
  enabled?: boolean;
  disabled?: boolean;
  onChange?: (enabled: boolean) => void;
  leftIcon?: string;
  rightIcon?: string;
  size?: ToggleSize;
  label?: string | JSX.Element;
  hideIcons?: boolean;
  id?: string;
};

export const Toggle = ({
  onChange,
  enabled = false,
  disabled = false,
  size = "md",
  label = undefined,
  hideIcons = false,
  leftIcon = "fa-check",
  rightIcon = "fa-times",
  id = "",
}: ToggleProps) => {
  const [isEnabled, setIsEnabled] = useState<boolean>(enabled);

  const handleToggle = (selected: boolean) => {
    if (onChange) {
      onChange(selected);
    }
  };

  useEffect(() => {
    setIsEnabled(enabled);
  }, [enabled]);

  const classes = containerVaraints({ size, enabled });

  const switchClasses = toggleVariants({ size, enabled });

  return (
    <div>
      <Switch
        className="group flex items-center gap-rem-80 text-2xl text-semantic-brand-default cursor-pointer relative text"
        defaultSelected={isEnabled}
        onChange={handleToggle}
        isDisabled={disabled}
      >
        <div className={classes} data-testid={`${id}-toggle`}>
          {isEnabled && !hideIcons && (
            <i
              className={cn(
                "fa-solid text-semantic-brand-white fa-2xs text-xs",
                leftIcon,
              )}
            />
          )}
          <span className={switchClasses} />
          {!isEnabled && !hideIcons && (
            <i
              className={cn(
                "fa-solid fa-2xs text-xs pl-[17px]",
                "text-semantic-content-interactive-primary-default",
                rightIcon,
              )}
            />
          )}
        </div>
        {label && <span className={labelVariants({ size })}>{label}</span>}
      </Switch>
    </div>
  );
};
