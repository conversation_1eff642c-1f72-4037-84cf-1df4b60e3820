import {
  useState,
  useEffect,
  type HTMLAttributes,
  type ReactNode,
  Children,
} from "react";
import { cn } from "..";

type CarouselProps = HTMLAttributes<HTMLDivElement> & {
  autoSlide?: boolean;
  hideSideNav?: boolean;
  hideFooterNav?: boolean;
  slideInterval?: number;
  containerClass?: string;
  sideNavButtons: {
    left: ReactNode;
    right: ReactNode;
  };
  style?: {
    sideNavClass?: string;
    footerNavClass?: {
      generic?: string;
      active?: string;
      inactive?: string;
    };
  };
};

const Carousel = ({
  children,
  autoSlide = true,
  slideInterval = 3000,
  hideSideNav = false,
  hideFooterNav = false,
  sideNavButtons,
  style,
  containerClass = "",
  id,
  ...rest
}: CarouselProps) => {
  const [current, setCurrent] = useState(0);
  const [enableAutoSlide, setAutoSlide] = useState<boolean>(autoSlide);
  const [showSideNav, setShowSideNav] = useState<boolean>(!hideSideNav);
  useEffect(() => setAutoSlide(autoSlide), [autoSlide]);
  useEffect(() => setShowSideNav(!hideSideNav), [hideSideNav]);

  const [components, setComponents] = useState(Children.toArray(children));
  const [length, setLength] = useState<number>(
    Children.toArray(children).length ?? 0,
  );
  useEffect(() => {
    setComponents(Children.toArray(children));
    if (length !== Children.toArray(children).length) {
      setCurrent(0);
    }
    setLength(Children.toArray(children).length);
  }, [children, length]);

  useEffect(() => {
    if (!enableAutoSlide) return;
    const timer = setInterval(() => {
      setCurrent((prev) => (prev === length - 1 ? 0 : prev + 1));
    }, slideInterval);

    return () => clearInterval(timer);
  }, [length, enableAutoSlide, slideInterval]);

  if (!Array.isArray(components) || components.length === 0) {
    return null;
  }

  const { sideNavClass, footerNavClass } = style ?? {};

  const { generic, active, inactive } = footerNavClass ?? {};

  const buttonClass =
    "absolute top-1/2 transform -translate-y-1/2 flex items-center justify-center w-10 h-10 rounded-240 border border-semantic-surface-interactive-primary-default shadow-2xl text-semantic-surface-interactive-primary-default";

  const styles = {
    cardSize: cn("relative overflow-visible", containerClass),
    leftButton: cn(buttonClass, "left-rem-10", sideNavClass ?? ""),
    rightButton: cn(buttonClass, "right-rem-10", sideNavClass ?? ""),
  };

  const ID = "carousel" + (id ? `-${id}` : "");

  return (
    <div className={styles.cardSize} {...rest} data-testid={ID}>
      <div
        className="flex transition-transform duration-700 ease-in-out justify-stretch"
        style={{ transform: `translateX(-${current * 100}%)` }}
      >
        {components.map((component, index) => (
          <div
            key={index}
            className="w-full flex-shrink-0"
            data-testid={`${ID}-${index}`}
          >
            {component}
          </div>
        ))}
      </div>

      {showSideNav && length > 1 && current !== 0 && (
        <button
          onClick={() => setCurrent(current === 0 ? length - 1 : current - 1)}
          className={styles.leftButton}
          data-testid={`${ID}-show-side-left`}
        >
          {sideNavButtons.left}
        </button>
      )}
      {showSideNav && length > 1 && current !== length - 1 && (
        <button
          onClick={() => setCurrent(current === length - 1 ? 0 : current + 1)}
          className={styles.rightButton}
          data-testid={`${ID}-show-side-right`}
        >
          {sideNavButtons.right}
        </button>
      )}

      {!hideFooterNav && length > 1 && (
        <div
          className="absolute bottom-0 left-0 right-0 flex justify-center space-x-2 pb-4"
          data-testid={`${ID}-dots`}
        >
          {components.map((_, index) => (
            <button
              key={index}
              data-testid={`${ID}-dot-${index}`}
              onClick={() => setCurrent(index)}
              className={`w-3 h-3 rounded-240 ${
                index === current
                  ? `bg-semantic-surface-interactive-primary-default ${active ?? ""}`
                  : `bg-semantic-content-base-subdued ${inactive ?? ""}`
              } ${generic ?? ""}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Carousel;
