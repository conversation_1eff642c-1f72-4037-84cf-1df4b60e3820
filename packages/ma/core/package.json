{"name": "@ma/core", "version": "1.0.0", "description": "Mobile Admin UI", "type": "module", "files": ["/bun_oneui_pages_dist", "/package.json"], "exports": {"./main.css": "./bun_oneui_pages_dist/css/main.css", "./ma-zs-nimbus.css": "./bun_oneui_pages_dist/css/ma-zs-nimbus.css", "./store": "./bun_oneui_pages_dist/src/store/index.js", "./http": "./bun_oneui_pages_dist/src/utils/http/http.js", "./component/LayoutLevelWrapper": "./bun_oneui_pages_dist/src/components/layoutLevelWrapper/index.js", "./ducks/permissions": "./bun_oneui_pages_dist/src/ducks/permissions/index.js", "./page/admin/ZscalerDeception": "./bun_oneui_pages_dist/src/pages/administration/zscaler-deception/index.js", "./page/admin/ClientConnectorNotifications": "./bun_oneui_pages_dist/src/pages/administration/client-connector-notifications/index.js", "./page/admin/UserAgent": "./bun_oneui_pages_dist/src/pages/administration/user-agent/index.js", "./page/admin/DedicatedProxyPort": "./bun_oneui_pages_dist/src/pages/administration/dedicated-proxy-port/index.js", "./page/admin/AdministrationManagement": "./bun_oneui_pages_dist/src/pages/administration/administration-management/index.js", "./page/admin/ApplicationBypass": "./bun_oneui_pages_dist/src/pages/administration/application-bypass/index.js", "./page/admin/AuditLogs": "./bun_oneui_pages_dist/src/pages/administration/audit-logs/index.js", "./page/admin/ClientConnectorAppStore": "./bun_oneui_pages_dist/src/pages/administration/client-connector-app-store/index.js", "./page/admin/ClientConnectorSupport": "./bun_oneui_pages_dist/src/pages/administration/client-connector-support/index.js", "./page/admin/DeviceGroups": "./bun_oneui_pages_dist/src/pages/administration/device-groups/index.js", "./page/admin/DevicePosture": "./bun_oneui_pages_dist/src/pages/administration/device-posture/index.js", "./page/admin/DDILConfiguration": "./bun_oneui_pages_dist/src/pages/administration/ddil-configuration/index.js", "./page/admin/ForwardingProfile": "./bun_oneui_pages_dist/src/pages/administration/forwarding-profile/index.js", "./page/admin/PublicApi": "./bun_oneui_pages_dist/src/pages/administration/public-api/index.js", "./page/admin/TrustedNetworks": "./bun_oneui_pages_dist/src/pages/administration/trusted-networks/index.js", "./page/admin/ZiaPostureProfile": "./bun_oneui_pages_dist/src/pages/administration/zia-posture-profile/index.js", "./page/admin/ZpaPartnerLogins": "./bun_oneui_pages_dist/src/pages/administration/zpa-partner-logins/index.js", "./page/admin/ZscalerServiceEntitlement": "./bun_oneui_pages_dist/src/pages/administration/zscaler-service-entitlement/index.js", "./page/dashboard/DevicePostureDashboard": "./bun_oneui_pages_dist/src/pages/dashboard/device-posture/index.js", "./page/dashboard/PlatformDetails": "./bun_oneui_pages_dist/src/pages/dashboard/platform-details/index.js", "./page/enrolledDevice/EnrolledDevices": "./bun_oneui_pages_dist/src/pages/enrolled-device/enrolled-devices/index.js", "./page/enrolledDevice/EnrolledPartnerDevices": "./bun_oneui_pages_dist/src/pages/enrolled-device/enrolled-partner-devices/index.js", "./page/enrolledDevice/FailedPostureDevices": "./bun_oneui_pages_dist/src/pages/enrolled-device/failed-posture-devices/index.js", "./page/enrolledDevice/MachineTunnel": "./bun_oneui_pages_dist/src/pages/enrolled-device/machine-tunnel/index.js", "./page/oneUi/PlatformSettings": "./bun_oneui_pages_dist/src/pages/one-ui/platform-settings/index.js"}, "scripts": {"copy-dist": "node src/scripts/copy.js", "postinstall": "pnpm run copy-dist"}, "dependencies": {"@zuxp/ma": "4.4.1-dev.582"}, "repository": {"type": "git", "url": "https://bitbucket.corp.zscaler.com/projects/MOB/repos/zappadminui/browse"}}