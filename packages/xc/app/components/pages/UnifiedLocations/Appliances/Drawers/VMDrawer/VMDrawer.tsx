import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Drawer } from "@xc/legacy-components";
import { REGEX_SUBNET, REGEX_IPV4 } from "@up/std";
import DrawerFooter from "../DrawerComponents/DrawerFooter";
import IpAddressInfo from "../DrawerComponents/IPAddressInfo";
import {
  type FormInputProps,
  type HighAvailabilityInfoProps,
  type VMDrawerProps,
} from "../../types";
import FormInput from "../DrawerComponents/FormInput";
import DrawerHeader from "../DrawerComponents/DrawerHeader";
import {
  drawerFooterConfig,
  ipAddressConfig,
  highAvailabilityConfig,
} from "./VMDrawer.data";
import { getDataTestId } from "@/utils/utils";

const HighAvailabilityInfo = ({
  config,
  heading,
  onChange,
  formData,
  validateOnChange,
}: HighAvailabilityInfoProps) => {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const validateIps = (id: string, value: string) => {
    const validation: Record<string, RegExp> = {
      loadBalanceIp: REGEX_SUBNET,
      haVirtualIp: REGEX_IPV4,
    };
    const errorMessage = !validation[id].test(value)
      ? t("appliances.drawer.shared.cidrError", {
          withcidr: id == "loadBalanceIp" ? "with CIDR" : "",
        })
      : "";
    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  useEffect(() => {
    errors?.haVirtualIp || errors?.loadBalanceIp
      ? validateOnChange?.(true)
      : validateOnChange?.(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors]);

  return (
    <>
      {heading && (
        <div className="flex justify-between text-semantic-content-base-primary mt-rem-80">
          <span className="typography-header5">{t(heading)}</span>
        </div>
      )}
      {config?.map(({ label, id, isReadOnly, placeholder }: FormInputProps) => (
        <FormInput
          key={id}
          header={label}
          value={formData[id] ?? ""}
          errorMessage={errors[id]}
          formValidation={!!errors[id]}
          id={id}
          onChange={(value: string) => {
            onChange(value, id);
            validateIps(id, value);
          }}
          readOnly={isReadOnly}
          placeholder={placeholder}
        />
      ))}
    </>
  );
};

const VMDrawer = ({
  heading,
  ipAddressInfoLabel,
  IPAddressInfoData,
  highAvailabilityLabel,
  openDrawer,
  setOpenDrawer,
  onSave,
  initFormData,
}: VMDrawerProps) => {
  const { t } = useTranslation();

  const drawerRef = useRef<{ hide: () => void; show: () => void }>();

  useEffect(() => {
    if (openDrawer) {
      drawerRef?.current?.show();
    }
  }, [openDrawer]);

  const VMDrawerContent = () => {
    const [isFormChange, setIsFormChange] = useState<boolean>(false);
    const [formData, setFormData] = useState(initFormData);
    const [saveDisabled, setSaveDisabled] = useState<boolean>(false);

    const handleDhcp = () => {
      setFormData({ ...formData, dhcpEnabled: !formData.dhcpEnabled });
    };

    const onTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
    };
    const checkValidation = (value: boolean) => {
      setSaveDisabled(value);
    };
    const onHighAvailTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
    };

    const onSaveClick = () => {
      onSave?.(formData);
    };

    const onCancel = () => {
      drawerRef?.current?.hide();
      setOpenDrawer(false);
    };

    useEffect(() => {
      setIsFormChange(
        saveDisabled
          ? false
          : JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData, saveDisabled]);
    drawerFooterConfig.save.onButtonClick = onSaveClick;
    drawerFooterConfig.save.disabled = !isFormChange;
    drawerFooterConfig.cancel.onButtonClick = onCancel;

    return (
      <div className="flex flex-col h-full relative">
        <div className="bg-semantic-content-inverted-base-primary h-[calc(100vh-65px)] overflow-auto">
          <DrawerHeader
            heading={heading}
            onCancel={onCancel}
            dataTestId={getDataTestId(
              "wan-vm-drawer-close-icon",
              "unified-locations",
            )}
          />
          <div className="gap-rem-160 p-rem-160 flex flex-col">
            <div className="flex flex-col gap-rem-160">
              <IpAddressInfo
                config={ipAddressConfig}
                IPInfoHeader={t(ipAddressInfoLabel)}
                isDhcpEnabled={!!formData.dhcpEnabled}
                formData={{
                  ipAddress: formData.ipAddress,
                  defaultGateway: formData.defaultGateway ?? "",
                  primaryDNS: formData.primaryDNS,
                  secondaryDNS: formData.secondaryDNS,
                }}
                dhcpData={[
                  {
                    ...IPAddressInfoData?.locationSwitch,
                    handleToggle: () => handleDhcp(),
                    isEnabled: !!formData.dhcpEnabled,
                  },
                ]}
                onChange={onTextChange}
                validateOnChange={checkValidation}
              />
            </div>
            <div className="flex flex-col gap-rem-160">
              <HighAvailabilityInfo
                heading={highAvailabilityLabel}
                config={highAvailabilityConfig}
                formData={{
                  loadBalanceIp: formData.loadBalanceIp,
                  haVirtualIp: formData.haVirtualIp,
                }}
                onChange={onHighAvailTextChange}
                validateOnChange={checkValidation}
              />
            </div>
          </div>
        </div>
        <DrawerFooter drawerFooterConfig={drawerFooterConfig} />
      </div>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      onClose={() => drawerRef?.current?.show()}
      contentRenderer={() => VMDrawerContent()}
      backdrop={true}
      customBackdrop={true}
      width={{
        max: "558px",
        min: "558px",
        default: "fit",
      }}
      id={"wan-interface-vm"}
    />
  );
};

export default VMDrawer;
