import { IP_CIDR_PLACEHOLDER } from "../../../constants";
import { type LANInterfaceFormDataProps } from "../../types";
import { type DrawerFooterConfig } from "../DrawerComponents/DrawerFooter";

export const drawerFooterConfig: DrawerFooterConfig = {
  save: {
    text: "SAVE",
    type: "primary",
    onButtonClick: () => console.log("Save"),
  },
  cancel: {
    text: "CANCEL",
    type: "tertiary",
    onButtonClick: () => console.log("Cancel"),
  },
};

export const highAvailabilityConfig = [
  {
    label: "appliances.drawer.lanInterface.id",
    id: "id",
    metaText: "appliances.drawer.lanInterface.id-metatext",
    placeholder: "",
  },
  {
    label: "appliances.drawer.lanInterface.virtualIp",
    id: "haVirtualIp",
    metaText: "appliances.drawer.lanInterface.virtualIp-metatext",
    placeholder: "x.x.x.x",
  },
  {
    label: "appliances.drawer.lanInterface.passphrase",
    id: "passphrase",
    placeholder: "",
  },
];

export const LAN_INTERFACE_DATA = {
  heading: "appliances.drawer.lanInterface.heading",
  radioLabel: "appliances.drawer.shared.radioLabel",
  lanInterfaceLabels: {
    mtuBytes: {
      id: "mtu",
      label: "appliances.drawer.shared.mtuBytes",
    },
    ipAddress: {
      id: "ipAddress",
      label: "appliances.drawer.shared.ipaddress",
      placeholder: IP_CIDR_PLACEHOLDER,
      suffix: "appliances.drawer.lanInterface.cidr",
    },
    description: {
      id: "description",
      label: "appliances.drawer.shared.description",
      required: false,
    },
  },
  preferred: {
    id: "preferred",
    isEnabled: false,
    desc: "appliances.drawer.lanInterface.preferred",
    metaText: "appliances.drawer.lanInterface.preferred-metatext",
    handleToggle: () => console.log("preferred"),
  },
  dhcpOptionsLabel: "appliances.drawer.lanInterface.dhcpOptionsLabel",
  staticIpLabel: "appliances.drawer.lanInterface.staticIpLabel",
  openDrawer: false,
  setOpenDrawer: () => true,
  customDNS: {
    id: "customDNS",
    isEnabled: false,
    desc: "appliances.drawer.shared.customDNS",
    handleToggle: () => console.log("Custom DNS"),
  },
  customDnsInputValue: {
    primaryDNS: "*******",
    secDNS: "*******",
  },

  customDNSData: [
    {
      label: "appliances.drawer.shared.primaryDNS",
      id: "primaryDNS",
    },
    {
      label: "appliances.drawer.shared.secDNS",
      id: "secDNS",
    },
  ],
  dhcpRangeOptions: {
    ipStart: {
      label: "appliances.drawer.lanInterface.startRange",
      id: "ipStart",
    },
    ipEnd: {
      label: "appliances.drawer.lanInterface.endRange",
      id: "ipEnd",
    },
  },
  dhcpLeaseLabel: "appliances.drawer.lanInterface.dhcp-lease",
  defaultLeaseTimeInput: {
    label: "appliances.drawer.lanInterface.leaseTime",
    id: "defaultLeaseTime",
    suffix: "appliances.drawer.lanInterface.seconds",
  },
  maxLeaseTimeInput: {
    label: "appliances.drawer.lanInterface.maxLeaseTime",
    id: "maxLeaseTime",
    suffix: "appliances.drawer.lanInterface.seconds",
  },
  peerDHCP: {
    id: "peerDHCP",
    label: "appliances.drawer.shared.peerDHCP",
  },
  dhcpServer: {
    id: "dhcpServer",
    isEnabled: false,
    desc: "appliances.drawer.shared.dhcpServer",
    handleToggle: () => console.log("dhcp server"),
  },
  highAvailability: {
    id: "highAvailability",
    isEnabled: false,
    desc: "appliances.drawer.shared.highAvailability",
    handleToggle: () => console.log("high availability"),
  },
  lanInterfaceData: {
    mtuBytes: "1500",
    ipAddress: "",
    description: "",
  },
  adminStatus: {
    selectedValue: "Up",
    options: [
      {
        label: "appliances.drawer.shared.up",
        value: "Up",
      },
      {
        label: "appliances.drawer.shared.down",
        value: "Down",
      },
    ],
  },
  dhcpServerData: [
    {
      id: 1,
      value: "",
      selectedOption: "Default Gateway",
    },
    {
      id: 2,
      value: "",
      selectedOption: "Default Gateway",
    },
  ],

  dhcpServerDropDownOption: [
    {
      id: "Default Gateway",
      label: "Default Gateway",
      disable: false,
    },
    {
      id: "DNS Server",
      label: "DNS Server",
      disable: false,
    },
    {
      id: "Domain Name",
      label: "Domain Name",
      disable: false,
    },
  ],
  dhcpServerStaticIp: [
    {
      id: 1,
      addressType: "",
      ip: "",
    },
    {
      id: 2,
      addressType: "",
      ip: "",
    },
  ],
  peerDhcp: "",
  toggleStates: {
    dhcpServer: false,
    customDNS: false,
    highAvailability: false,
  },
  haConfig: {
    id: "",
    passphrase: "",
    preferred: false,
    virtualIpAddress: "",
  },
  initFormData: {
    adminStatus: "Up",
    mtu: "",
    ipAddress: "",
    description: "",
    customDNSInputVal: {
      primaryDNS: "",
      secDNS: "",
    },
    id: "",
    haVirtualIp: "",
    passphrase: "",
    toggles: {
      dhcpServer: false,
      customDNS: false,
      highAvailability: false,
      preferred: false,
    },
    peerDhcpIp: "",
    dhcpServerData: [
      {
        id: 1,
        value: "",
        selectedOption: "Default Gateway",
      },
      {
        id: 2,
        value: "",
        selectedOption: "DNS Server",
      },
      {
        id: 3,
        value: "",
        selectedOption: "Domain Name",
      },
    ],
    dhcpServerStaticIp: [
      {
        id: 1,
        addressType: "",
        ip: "",
      },
      {
        id: 2,
        addressType: "",
        ip: "",
      },
    ],
    dhcpRangeData: [
      {
        id: 1,
        ipStart: "",
        ipEnd: "",
      },
    ],
    defaultLeaseTime: "",
    maxLeaseTime: "",
  },
  isLanSubInterfaceDrawer: false,
  vlanIdLabel: "appliances.drawer.shared.vlanid",
  onSave: (formData: LANInterfaceFormDataProps) => console.log(formData),
};
