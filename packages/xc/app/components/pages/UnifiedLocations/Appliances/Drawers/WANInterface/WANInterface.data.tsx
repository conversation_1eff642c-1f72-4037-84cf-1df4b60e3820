import { IP_CIDR_PLACEHOLDER, IP_PLACEHOLDER } from "../../../constants";
import { type IPAddressInfoData } from "../../types";
import { type DrawerFooterConfig } from "../DrawerComponents/DrawerFooter";

export const drawerFooterConfig: DrawerFooterConfig = {
  save: {
    text: "SAVE",
    type: "primary",
    onButtonClick: () => console.log("Save"),
  },
  cancel: {
    text: "CANCEL",
    type: "tertiary",
    onButtonClick: () => console.log("Cancel"),
  },
};

export const ipAddressConfig = [
  {
    label: "appliances.drawer.shared.ipAddress",
    id: "ipAddress",
    placeholder: IP_CIDR_PLACEHOLDER,
  },
  {
    label: "appliances.drawer.shared.gateway",
    id: "defaultGateway",
    placeholder: IP_PLACEHOLDER,
  },
  {
    label: "appliances.drawer.shared.priDNS",
    id: "primaryDNS",
    placeholder: IP_PLACEHOLDER,
  },
  {
    label: "appliances.drawer.shared.secDNS",
    id: "secondaryDNS",
    placeholder: IP_PLACEHOLDER,
  },
];

const initFormData = {
  ipAddress: "",
  defaultGateway: "",
  primaryDNS: "",
  secondaryDNS: "",
  adminStatus: "Up",
  dhcpEnabled: true,
  upLinkMode: "STANDBY",
  mtu: "",
  description: "",
  vlanId: "",
};

export const WAN_INTERFACE_DATA = {
  heading: "appliances.drawer.wanInterface.heading",
  radioLabel: "",
  ipAddressInfoLabel: "",
  uplinkModeLabel: "appliances.drawer.shared.uplinkModeHeading",
  mtuBytes: "appliances.drawer.shared.mtuBytes",
  vlanIdLabel: "appliances.drawer.shared.vlanid",
  descrition: "appliances.drawer.shared.description",
  wanInterfaceMTUByte: {
    mtu: "1500",
    description: "",
    vlanId: "100",
  },
  isWanSubInterfaceDrawer: false,
  IPAddressInfoData: {
    ipAddressInfo: {
      ipAddress: "***********",
      defaultGateway: "*************",
      primaryDNS: "",
      secondaryDNS: "",
    },
    mtuBytes: "1500",
    upLinkMode: {
      selectedValue: "ACTIVE",
      options: [
        { label: "appliances.drawer.shared.active", value: "ACTIVE" },
        {
          label: "appliances.drawer.shared.standby",
          value: "STANDBY",
        },
      ],
    },

    locationSwitch: {
      id: "dhcp",
      isEnabled: true,
      desc: "appliances.drawer.shared.dhcp",
      handleToggle: () => console.log("Location Toggle"),
    },
  },
  initFormData,
  onSave: (formData: IPAddressInfoData) => console.log(formData),
};
