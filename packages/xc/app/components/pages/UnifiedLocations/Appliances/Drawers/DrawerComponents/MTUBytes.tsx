import { InlineFeedback } from "@zs-nimbus/core";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  DEFAULT_INTERFACE_MTU,
  SUBINTERFACE_MTU_BUFFER,
} from "../../../constants";

export type MTUBytesProps = {
  header: string;
  id: string;
  value: string | undefined;
  onChange: (value: string, id: string) => void;
  placeholder?: string;
  isError?: boolean;
  parentMtu?: string;
  isWanDrawer?: boolean;
};

export default function MTUBytes({
  header,
  id,
  value,
  placeholder = "Enter",
  onChange,
  isError,
  parentMtu,
  isWanDrawer,
}: MTUBytesProps) {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<string>("");
  const validateInput = (value: string) => {
    const getValue = Number(value);
    const getParentMtu = Number(parentMtu);
    const errorMessage =
      parentMtu && getValue > getParentMtu - SUBINTERFACE_MTU_BUFFER
        ? t("appliances.drawer.wanInterface.mtuBytes_max_errors", {
            limit: getParentMtu - SUBINTERFACE_MTU_BUFFER,
          })
        : getValue > DEFAULT_INTERFACE_MTU
          ? t("appliances.drawer.wanInterface.mtuBytes_max_errors", {
              limit: DEFAULT_INTERFACE_MTU,
            })
          : "";
    setErrors(errorMessage);
  };

  return (
    <div>
      <div
        className="flex flex-col gap-rem-80"
        data-testid={`form-container-${id}`}
      >
        <div
          className={`${isError || errors ? " text-semantic-content-status-danger-secondary" : "text-semantic-content-base-secondary"} ${isWanDrawer ? "ml-0" : "ml-32"} typography-paragraph2`}
        >
          {header}
        </div>
        <input
          type="text"
          value={value}
          className={`${isWanDrawer ? "ml-0" : "ml-32"} border border-semantic-border-base-primary bg-semantic-surface-fields-default rounded-40 px-rem-80 py-[0.4rem] text-semantic-content-base-primary typography-paragraph1 typography-paragraph1 placeholder:text-semantic-content-base-tertiary w-[72px]`}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
            onChange(event.target.value, id);
            validateInput(event.target.value);
          }}
          placeholder={placeholder}
          data-testid={`form-input-${id}`}
        />
      </div>
      <div>
        {isError ||
          (errors && (
            <InlineFeedback size="sm" type="danger">
              {isError
                ? t("appliances.drawer.wanInterface.mtuBytes_err_message")
                : errors}
            </InlineFeedback>
          ))}
      </div>
    </div>
  );
}
