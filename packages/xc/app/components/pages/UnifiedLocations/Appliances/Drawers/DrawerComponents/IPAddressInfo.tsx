import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { REGEX_SUBNET, REGEX_IPV4 } from "@up/std";
import { type ToggleSwitch } from "../../../Locations/types";
import LocationToggleSwitch from "../../../Locations/LocationToggleSwitch/LocationToggleSwitch";
import FormInput from "./FormInput";

type FormInputProps = {
  label: string;
  id: string;
  isReadOnly?: boolean;
  required?: boolean;
  placeholder: string;
};

type Props = {
  config: FormInputProps[];
  IPInfoHeader: string;
  dhcpData: ToggleSwitch[];
  isDhcpEnabled: boolean;
  formData: Record<string, string | undefined>;
  onChange: (value: string, id: string) => void;
  id?: string;
  validateOnChange?: (value: boolean) => void;
};

export default function IpAddressInfo({
  config,
  IPInfoHeader,
  dhcpData,
  isDhcpEnabled,
  onChange,
  formData,
  id,
  validateOnChange,
}: Props) {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const hasAnyError = (obj: Record<string, string>) => {
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === "object") {
        if (hasAnyError(obj[key])) return true;
      } else if (obj[key]) {
        return true;
      }
    }

    return false;
  };

  const validateIps = (id: string, value: string) => {
    const validation: Record<string, RegExp> = {
      ipAddress: REGEX_SUBNET,
      defaultGateway: REGEX_IPV4,
      primaryDNS: REGEX_IPV4,
      secondaryDNS: REGEX_IPV4,
    };
    const errorMessage = !validation[id].test(value)
      ? t("appliances.drawer.shared.cidrError", {
          withcidr: id == "ipAddress" ? "with CIDR" : "",
        })
      : "";
    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  useEffect(() => {
    validateOnChange?.(hasAnyError(errors));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors]);

  const filterConfig = isDhcpEnabled
    ? config
        .filter((item) => ["primaryDNS", "secondaryDNS"].includes(item.id))
        ?.map((item) => ({
          ...item,
          placeholder: `${t("appliances.drawer.shared.automatic")} (${item.placeholder})`,
          required: false,
        }))
    : config;

  const showFilterConfig = isDhcpEnabled
    ? config.filter((item) => ["ipAddress", "defaultGateway"].includes(item.id))
    : [];

  return (
    <div
      className={`flex ${IPInfoHeader ? "justify-between" : ""} flex-col  text-semantic-content-base-secondary typography-header5 mt-rem-160`}
      data-testid={`ip-addr-${id}`}
    >
      <div className="flex pb-rem-120">
        {IPInfoHeader}
        <LocationToggleSwitch locationSwitch={dhcpData} id={`ip-addr-${id}`} />
      </div>

      {showFilterConfig?.map((data, index) => (
        <div
          className="inline-flex justify-start items-start gap-6 pt-rem-40"
          key={index}
        >
          <div className="typography-paragraph2 text-semantic-content-base-tertiary w-rem-1280 break-all">
            {t(data.label)}
          </div>

          <div className="inline-flex gap-1 ">
            {formData[data.id] && (
              <div className="typography-paragraph2 text-semantic-content-base-primary">
                {t("appliances.drawer.shared.automatic")}
              </div>
            )}
            <div className="typography-paragraph2 text-semantic-content-base-tertiary max-w-[300px] break-all">
              {formData[data.id]
                ? `(${formData[data.id]})`
                : `${t("appliances.drawer.shared.automaticExample", { example: "(x.x.x.x)" })}`}
            </div>
          </div>
        </div>
      ))}

      <div className="flex flex-col gap-rem-80 pt-rem-120">
        {filterConfig?.map(
          ({
            label,
            id,
            isReadOnly,
            required,
            placeholder,
          }: FormInputProps) => (
            <FormInput
              key={id}
              header={label}
              value={formData[id] ?? ""}
              errorMessage={errors[id]}
              formValidation={!!errors[id]}
              id={id}
              onChange={(value: string) => {
                onChange(value, id);
                validateIps(id, value);
              }}
              readOnly={isReadOnly}
              required={required}
              placeholder={placeholder}
            />
          ),
        )}
      </div>
    </div>
  );
}
