import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Drawer } from "@xc/legacy-components";
import DrawerFooter from "../DrawerComponents/DrawerFooter";
import IpAddressInfo from "../DrawerComponents/IPAddressInfo";
import { type MGTDrawerProps } from "../../types";
import DrawerHeader from "../DrawerComponents/DrawerHeader";
import { drawerFooterConfig, ipAddressConfig } from "./MGTDrawer.data";
import { getDataTestId } from "@/utils/utils";

const MGTDrawer = ({
  heading,
  ipAddressInfoLabel,
  IPAddressInfoData,
  openDrawer,
  setOpenDrawer,
  onSave,
  initFormData,
}: MGTDrawerProps) => {
  const { t } = useTranslation();

  const drawerRef = useRef<{ hide: () => void; show: () => void }>();

  useEffect(() => {
    if (openDrawer) {
      drawerRef?.current?.show();
    }
  }, [openDrawer]);

  const MGTDrawerContent = () => {
    const [isFormChange, setIsFormChange] = useState<boolean>(false);
    const [saveDisabled, setSaveDisabled] = useState<boolean>(false);
    const [formData, setFormData] = useState(initFormData);

    const handleDhcp = () => {
      setFormData({ ...formData, dhcpEnabled: !formData.dhcpEnabled });
    };

    const onTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
    };

    const checkValidation = (value: boolean) => {
      setSaveDisabled(value);
    };
    const onSaveClick = () => {
      onSave?.(formData);
    };

    useEffect(() => {
      setIsFormChange(
        saveDisabled
          ? false
          : JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData, saveDisabled]);

    const onCancel = () => {
      drawerRef?.current?.hide();
      setOpenDrawer(false);
    };
    drawerFooterConfig.save.onButtonClick = onSaveClick;
    drawerFooterConfig.save.disabled = !isFormChange;
    drawerFooterConfig.cancel.onButtonClick = onCancel;

    return (
      <div className="flex flex-col h-full relative">
        <div className="bg-semantic-content-inverted-base-primary h-[calc(100vh-65px)] overflow-auto">
          <DrawerHeader
            heading={heading}
            onCancel={onCancel}
            dataTestId={getDataTestId(
              "unified-locations-mgt-drawer-close-icon",
              "unified-locations",
            )}
          />
          <div className="gap-rem-160 px-rem-160 py-rem-80 flex flex-col">
            <div>
              <IpAddressInfo
                config={ipAddressConfig}
                IPInfoHeader={t(ipAddressInfoLabel)}
                isDhcpEnabled={!!formData.dhcpEnabled}
                formData={{
                  ipAddress: formData.ipAddress,
                  defaultGateway: formData.defaultGateway ?? "",
                  primaryDNS: formData.primaryDNS,
                  secondaryDNS: formData.secondaryDNS,
                }}
                dhcpData={[
                  {
                    ...IPAddressInfoData?.locationSwitch,
                    handleToggle: () => handleDhcp(),
                    isEnabled: !!formData.dhcpEnabled,
                  },
                ]}
                onChange={onTextChange}
                validateOnChange={checkValidation}
              />
            </div>
          </div>
        </div>
        <DrawerFooter drawerFooterConfig={drawerFooterConfig} />
      </div>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      onClose={() => drawerRef?.current?.show()}
      contentRenderer={() => MGTDrawerContent()}
      backdrop={true}
      customBackdrop={true}
      width={{
        max: "558px",
        min: "558px",
        default: "fit",
      }}
    />
  );
};

export default MGTDrawer;
