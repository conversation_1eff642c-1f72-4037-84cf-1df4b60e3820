import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Drawer } from "@xc/legacy-components";
import { RadioGroup } from "@zs-nimbus/core";
import DrawerFooter from "../DrawerComponents/DrawerFooter";
import IpAddressInfo from "../DrawerComponents/IPAddressInfo";
import { type WANInterfaceProps } from "../../types";
import FormInput from "../DrawerComponents/FormInput";
import MTUBytes from "../DrawerComponents/MTUBytes";
import DrawerHeader from "../DrawerComponents/DrawerHeader";
import FormTextArea from "../DrawerComponents/FormTextArea";
import { drawerFooterConfig, ipAddressConfig } from "./WANInterface.data";
import { getDataTestId } from "@/utils/utils";

const WANInterface = ({
  heading,
  radioLabel,
  ipAddressInfoLabel,
  descrition,
  IPAddressInfoData,
  uplinkModeLabel,
  openDrawer,
  mtuBytes,
  vlanIdLabel,
  isWanSubInterfaceDrawer,
  setOpenDrawer,
  onSave,
  initFormData,
}: WANInterfaceProps) => {
  const { t } = useTranslation();

  const drawerRef = useRef<{ hide: () => void; show: () => void }>();
  const [errors, setErrors] = useState<boolean>(false);
  useEffect(() => {
    if (openDrawer) {
      drawerRef?.current?.show();
    }
  }, [openDrawer]);

  const WANInterfaceDrawerContent = () => {
    const [isFormChange, setIsFormChange] = useState<boolean>(false);
    const [formData, setFormData] = useState(initFormData);
    const [saveDisabled, setSaveDisabled] = useState<boolean>(false);

    const handleDhcp = () => {
      setFormData({ ...formData, dhcpEnabled: !formData.dhcpEnabled });
    };
    const checkValidation = (value: boolean) => {
      setSaveDisabled(value);
    };

    const onMTUDescritionTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
      setErrors(false);
    };

    const onTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
    };

    const onCancel = () => {
      setOpenDrawer(false);
      drawerRef?.current?.hide();
    };

    const onSaveClick = () => {
      formData.mtu === "" ? setErrors(true) : onSave?.(formData);
    };

    useEffect(() => {
      setIsFormChange(
        saveDisabled
          ? false
          : JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData, saveDisabled]);

    drawerFooterConfig.save.onButtonClick = onSaveClick;
    drawerFooterConfig.save.disabled = !isFormChange;
    drawerFooterConfig.cancel.onButtonClick = onCancel;

    return (
      <div className="flex flex-col h-full relative">
        <div className="bg-semantic-content-inverted-base-primary h-[calc(100vh-65px)] overflow-auto">
          <DrawerHeader
            heading={heading}
            onCancel={onCancel}
            dataTestId={getDataTestId(
              "unified-locations-wan-interface-drawer-close-icon",
              "unified-locations",
            )}
          />
          <div className="gap-rem-160 p-rem-160 flex flex-col">
            <div className="flex">
              {IPAddressInfoData?.adminStatus?.options && (
                <div>
                  <RadioGroup
                    key="adminStatus"
                    groupName="adminStatus"
                    groupLabel={t(radioLabel)}
                    onChange={(value: string) => {
                      setFormData({ ...formData, adminStatus: value });
                    }}
                    defaultValue={formData.adminStatus ?? ""}
                    itemProps={IPAddressInfoData?.adminStatus?.options.map(
                      (item) => ({
                        value: item.value,
                        labelText: t(item.label),
                      }),
                    )}
                    orientation="horizontal"
                  />
                </div>
              )}
              <MTUBytes
                header={t(mtuBytes)}
                value={formData.mtu}
                placeholder="Enter"
                id="mtu"
                onChange={onMTUDescritionTextChange}
                isError={errors}
                isWanDrawer={true}
              />
            </div>
            {isWanSubInterfaceDrawer && (
              <FormInput
                header={t(vlanIdLabel)}
                value={formData?.vlanId ?? ""}
                id="vlanId"
                placeholder=""
                onChange={onMTUDescritionTextChange}
              />
            )}
            <FormTextArea
              header={t(descrition)}
              value={formData?.description ?? ""}
              id="description"
              placeholder=""
              onChange={onTextChange}
              required={false}
            />
            <RadioGroup
              key="uplinkModeLabel"
              groupName="uplinkModeLabel"
              groupLabel={t(uplinkModeLabel)}
              onChange={(value: string) => {
                setFormData({ ...formData, upLinkMode: value });
              }}
              defaultValue={formData.upLinkMode}
              itemProps={
                IPAddressInfoData?.upLinkMode?.options.map((item) => ({
                  value: item.value,
                  labelText: t(item.label),
                })) ?? []
              }
              orientation="horizontal"
            />
            <div className="flex flex-col gap-rem-160 border-t border-semantic-border-interactive-primary-disabled">
              <IpAddressInfo
                config={ipAddressConfig}
                IPInfoHeader={t(ipAddressInfoLabel)}
                isDhcpEnabled={!!formData.dhcpEnabled}
                formData={{
                  ipAddress: formData.ipAddress,
                  defaultGateway: formData.defaultGateway ?? "",
                  primaryDNS: formData.primaryDNS,
                  secondaryDNS: formData.secondaryDNS,
                }}
                dhcpData={[
                  {
                    ...IPAddressInfoData?.locationSwitch,
                    handleToggle: () => handleDhcp(),
                    isEnabled: !!formData.dhcpEnabled,
                  },
                ]}
                onChange={onTextChange}
                validateOnChange={checkValidation}
              />
            </div>
          </div>
        </div>
        <DrawerFooter drawerFooterConfig={drawerFooterConfig} />
      </div>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      onClose={() => drawerRef?.current?.show()}
      contentRenderer={() => WANInterfaceDrawerContent()}
      backdrop={true}
      customBackdrop={true}
      width={{
        max: "558px",
        min: "558px",
        default: "fit",
      }}
    />
  );
};

export default WANInterface;
