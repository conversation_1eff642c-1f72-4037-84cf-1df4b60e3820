/* eslint-disable @up/unified-platform/max-file-lines */
import { type Dispatch, type SetStateAction } from "react";
import { type ButtonVariantType } from "@zs-nimbus/core";
import {
  type ToggleSwitch,
  type OptionItem,
  type PaginationResponse,
} from "../Locations/types";
import { type SelectOption } from "./Drawers/DrawerComponents/FormSelect";
import { type SelectProps } from "./components/UpgradeSchedule/Select";
import { type ApplianceListFromServer } from "./components/ApplianceList/ApplianceListTransformer";
import { type ApplianceDetailFromServer } from "./ApplianceDetailTransformer";
import { type TableColumnItemProps } from "@/components/Analytics/DataTable/types";

export type ApplianceNetworkFormProps = {
  dnsCache: string;
  tunnelMode: string;
  distributionMode: string;
};

export type ApplianceNetworkCacheProps = {
  id: string;
  checked: boolean;
  label: string;
};

export type ApplianceNetworkModeProps = {
  heading: string;
  selectedValue: string;
  options: OptionItem[];
};

export type FetchApplianceParams = {
  groupId: string;
  provTemplateName: string;
  serialNumber: string;
};

export type ApplianceNetworkProps = {
  dnsCache: ApplianceNetworkModeProps;
  tunnelMode: ApplianceNetworkModeProps;
  distributionMode: ApplianceNetworkModeProps;
  onSave: (values: ApplianceNetworkFormProps) => void;
  onReset: (values: ApplianceNetworkFormProps) => void;
  isVirtualDevice: boolean;
};

export type ApplianceNetworkProp = {
  heading: string;
  applianceNetworkData: ApplianceNetworkProps;
  apiResponse?: ApplianceDetailFromServer;
  triggerUpdateAppliance?: (
    payload: ApplianceDetailFromServer,
    apiType: string,
  ) => void;
  readOnly: boolean;
};

export type ApplianceTroubleshootingProps = TunnelInfoProps & {
  pageTitle: string;
  pageDesc: string;
  locationSwitchList: ToggleSwitch[];
  onReset: (values: TroubleShootFormProps) => void;
  apiResponse?: ApplianceDetailFromServer;
  triggerUpdateAppliance?: (
    payload: ApplianceDetailFromServer,
    type: string,
  ) => void;
  readOnly: boolean;
};

export type TunnelInfoProps = {
  tunnelInfoTitle: string;
  tunnelInfo: Version[];
};

export type UpgradeScheduleFormProps = {
  day: string;
  hour: string;
  minute: string;
  meridiem: string;
};

export type TroubleShootFormProps = {
  enableTroubleShoot: boolean;
  enableZcalerSupport: boolean;
};

export type Version = {
  label: string;
  value: string;
  timeZone?: string;
};

type UpgradeScheduleSupportedVersionData = {
  title: string;
  data: Version[];
};

export type VersionData = {
  currentVersion: UpgradeScheduleSupportedVersionData;
  nextVersion: UpgradeScheduleSupportedVersionData & {
    upgradeDay: number;
  };
};

export type ScheduleDataProp = {
  day: SelectProps;
  hour: SelectProps;
  minute: SelectProps;
  meridiem: SelectProps;
};

export type UpgradeScheduleProp = {
  pageTitle: string;
  initialScheduledData: UpgradeScheduleFormProps;
  schedule: ScheduleDataProp;
  versionData: VersionData;
  onSave: (values: UpgradeScheduleFormProps) => void;
  onReset: (values: UpgradeScheduleFormProps) => void;
  apiResponse?: ApplianceDetailFromServer;
  triggerUpdateAppliance?: (
    payload: ApplianceDetailFromServer,
    apiType: string,
  ) => void;
  readOnly: boolean;
};

export type FilterOptionsProps = {
  id: string;
  name: string;
  value?: string;
};

export type ApplianceInterfacesDataSetProps = {
  name: string | null;
  meta: string | null;
  value?: number;
  type?: string | null;
  onClickHandler?: (type: string, value: string) => void;
  interfaceId?: string;
  subInterfaceID?: string;
};

export type StatusProps = {
  value: string | null;
  status: string | null;
};

export type ApplianceInterfacesStatus = {
  adminValue: string | null;
  adminStatus: string | null;
  linkValue: string | null;
  linkStatus: string | null;
  type: "link" | "admin";
};

type ApplianceInterfaces = {
  id: number;
  interfaces: ApplianceInterfacesDataSetProps;
  type: string | null;
  ipAddress: string | null;
  dhcp: ApplianceInterfacesDataSetProps;
  details: ApplianceInterfacesDataSetProps;
  interfaceDetail: ApplianceInterfacesDataSetProps;
  adminStatus: StatusProps;
  linkStatus: StatusProps;
  actions?: string | JSX.Element;
  name?: string;
  upLinkMode?: string | null;
  trafficDistributionMode?: string | null;
};

export type ApplianceInterfacesTableRowItemProps = ApplianceInterfaces & {
  subInterfaces?: ApplianceInterfaces[];
};

export type ApplianceInterfacesProps = {
  interfacesOptions: FilterOptionsProps[];
  tableRowData: ApplianceInterfacesTableRowItemProps[];
  columnData: TableColumnItemProps[];
  onSortChange: (value: string) => void;
  onInterfacesChange: (filters: FilterOptionsProps[]) => void;
  apiResponse?: ApplianceDetailFromServer;
  triggerUpdateAppliance?: (
    payload: ApplianceDetailFromServer,
    apiType: string,
  ) => void;
  readOnly: boolean;
};

export type InterfacesListProps = {
  dhcp?: string;
  haVirtualIp?: string;
  loadBalanceIp?: string;
  primaryDns?: string;
  secondaryDns?: string;
  adminStatus?: string | null;
  dhcpEnabled?: boolean;
  defaultGateway?: string | null;
  upLinkMode?: string;
  mtu?: string;
  detailsDescription?: string | null;
  description?: string;
  type?: string;
  dhcpStatus?: string;
  vlanId?: string;
  trafficDistributionMode?: string;
  parentMtu?: string;
  serviceIpAddress2?: string;
  serviceIpAddress3?: string;
};

export type IPAddressInfoData = InterfacesListProps & {
  ipAddress: string;
  primaryDNS: string;
  secondaryDNS: string;
};

export type AdminStatusData = {
  selectedValue: string;
  options: Array<{
    label: string;
    value: string;
  }>;
};

export type IPAddressInfoDataType = {
  ipAddressInfo: IPAddressInfoData;
  adminStatus?: AdminStatusData;
  locationSwitch: ToggleSwitch;
  upLinkMode?: AdminStatusData;
  mtuBytes?: string | undefined;
};

export type HighAvailabilityInfoProps = {
  config: FormInputProps[];
  heading?: string;
  formData: Record<string, string | undefined>;
  onChange: (value: string, id: string) => void;
  validateOnChange?: (value: boolean) => void;
};

export type HighAvailInfoProps = {
  haVirtualIp: string;
  loadBalanceIp: string;
};

export type VMDrawerProps = {
  heading: string;
  radioLabel: string;
  ipAddressInfoLabel: string;
  highAvailabilityLabel: string;
  IPAddressInfoData: IPAddressInfoDataType;
  openDrawer: boolean;
  setOpenDrawer: Dispatch<SetStateAction<boolean>>;
  onSave: (formData: IPAddressInfoData) => void;
  initFormData: IPAddressInfoData;
};

export type FormInputProps = {
  label: string;
  id: string;
  isReadOnly?: boolean;
  metaText?: string;
  placeholder?: string;
  required?: boolean;
  suffix?: string;
};

export type DrawerRef = {
  hide: () => void;
  show: () => void;
};

export type MGTDrawerProps = {
  heading: string;
  radioLabel: string;
  ipAddressInfoLabel: string;
  IPAddressInfoData: IPAddressInfoDataType;
  openDrawer: boolean;
  setOpenDrawer: Dispatch<SetStateAction<boolean>>;
  onSave: (formData: IPAddressInfoData) => void;
  initFormData: IPAddressInfoData;
};

export type ResponseBody = {
  pagination: PaginationResponse;
  data: ApplianceListFromServer;
};

export type WANInterfaceProps = {
  heading: string;
  radioLabel: string;
  isWanSubInterfaceDrawer: boolean;
  ipAddressInfoLabel: string;
  vlanIdLabel: string;
  mtuBytes: string;
  uplinkModeLabel: string;
  descrition: string;
  IPAddressInfoData: IPAddressInfoDataType;
  openDrawer: boolean;
  setOpenDrawer: Dispatch<SetStateAction<boolean>>;
  onSave: (formData: IPAddressInfoData) => void;
  initFormData: IPAddressInfoData;
};

export type ChildHandle = {
  getVlanId: () => string;
};

type LanInterfaceData = {
  mtuBytes: string;
  ipAddress: string;
  description: string;
  vLanId?: string;
};

type ToggleStates = {
  dhcpServer: boolean;
  customDNS: boolean;
  highAvailability: boolean;
  preferred: boolean;
};

type LanSubInterfaceLabels = {
  mtuBytes: FormInputProps;
  ipAddress: FormInputProps;
  description: FormInputProps;
  vLanId: FormInputProps;
};

export type LANSubInterfaceDrawerProps = {
  heading: string;
  radioLabel: string;
  openDrawer: boolean;
  setOpenDrawer: Dispatch<SetStateAction<boolean>>;
  customDNS: ToggleSwitch;
  customDNSData: FormInputProps[];
  customDnsInputValue: CustomDnsValueProps;
  dhcpServer: ToggleSwitch;
  highAvailability: ToggleSwitch;
  lanInterfaceData: LanInterfaceData;
  adminStatusOptions: Version[];
  toggleStates: ToggleStates;
  lanSubInterfaceLabels: LanSubInterfaceLabels;
};

export type CustomDNSProps = {
  customDNS: ToggleSwitch[];
  config: Version[];
};

export type LanInterfaceCustomDNSProps = {
  customDNS: ToggleSwitch[];
  config: FormInputProps[];
  customDNSVal: CustomDnsValueProps;
  setCustomDNSVal: (value: CustomDnsValueProps) => void;
};

type LanInterfaceLabels = {
  mtuBytes: FormInputProps;
  ipAddress: FormInputProps;
  description: FormInputProps;
};

export type DhcpServerDataProps = {
  id: string | number;
  value: string;
  selectedOption: string;
};

export type DhcpServerStaticIpProps = {
  id: string | number;
  addressType: string;
  ip: string;
};

export type DHCPRangeOptions = {
  ipStart: FormInputProps;
  ipEnd: FormInputProps;
};

export type LANInterfaceDrawerProps = {
  heading: string;
  radioLabel: string;
  dhcpOptionsLabel: string;
  staticIpLabel: string;
  openDrawer: boolean;
  setOpenDrawer: Dispatch<SetStateAction<boolean>>;
  customDNS: ToggleSwitch;
  customDNSData: FormInputProps[];
  dhcpServer: ToggleSwitch;
  preferred: ToggleSwitch;
  highAvailability: ToggleSwitch;
  adminStatus: AdminStatusData;
  dhcpServerDropDownOption: SelectOption[];
  lanInterfaceLabels: LanInterfaceLabels;
  onSave: (formData: LANInterfaceFormDataProps) => void;
  initFormData: LANInterfaceFormDataProps;
  peerDHCP: FormInputProps;
  defaultLeaseTimeInput: FormInputProps;
  maxLeaseTimeInput: FormInputProps;
  dhcpLeaseLabel: string;
  dhcpRangeOptions: DHCPRangeOptions;
  isLanSubInterfaceDrawer: boolean;
  vlanIdLabel: string;
};

export type HaConfigProps = {
  id: string;
  passphrase: string;
  preferred: boolean;
  virtualIpAddress: string;
};

export type LANInterfaceFormDataProps = InterfacesListProps & {
  id: string;
  passphrase: string;
  ipAddress: string;
  peerDhcpIp: string;
  dhcpServerData: DhcpServerDataProps[];
  dhcpServerStaticIp: DhcpServerStaticIpProps[];
  dhcpRangeData: DHCPRangeProps[];
  toggles: ToggleStates;
  customDNSInputVal: CustomDnsValueProps;
  defaultLeaseTime: string;
  maxLeaseTime: string;
};

export type CustomDnsValueProps = {
  primaryDNS: string;
  secDNS: string;
};

export type DHCPServerProps = {
  dhcpOptionsLabel: string;
  staticIpLabel: string;
  dhcpServerDropDownOption: SelectOption[];
  dhcpServerVal: DhcpServerDataProps[];
  setDhcpServerVal: (value: DhcpServerDataProps[]) => void;
  dhcpServerStaticIpData: DhcpServerStaticIpProps[];
  setDhcpServerStaticIpData: (value: DhcpServerStaticIpProps[]) => void;
  peerDhcpVal: string;
  setPeerDhcpVal: (value: string) => void;
  dhcpRangeDataVal: DHCPRangeProps[];
  setDhcpRangeDataVal: (value: DHCPRangeProps[]) => void;
  peerDHCP: FormInputProps;
  defaultLeaseTimeInput: FormInputProps;
  maxLeaseTimeInput: FormInputProps;
  defaultLeaseTime: string;
  maxLeaseTime: string;
  dhcpLeaseLabel: string;
  setMaxLeaseTime: (value: string) => void;
  setDefaultLeaseTime: (value: string) => void;
  dhcpRangeOptions: DHCPRangeOptions;
};

export type DHCPRangeProps = {
  id: number;
  ipStart: string;
  ipEnd: string;
};

export type DHCPLeaseProps = {
  length: number;
  id: number;
  macAddress: string;
  ipAddress: string;
};

export type DHCPOptionsProps = {
  options: SelectOption[];
  dhcpServerVal: DhcpServerDataProps;
  setDhcpServerVal: (value: DhcpServerDataProps[]) => void;
  deleteHandler?: (id: string) => void;
  dhcpServerData: DhcpServerDataProps[];
};

export type StaticIpsOptionsProps = {
  dhcpServerStaticIpData: DhcpServerStaticIpProps;
  setDhcpServerStaticIpData: (value: DhcpServerStaticIpProps[]) => void;
  deleteStaticIpDataHandler?: (id: string) => void;
  dhcpServerData: DhcpServerStaticIpProps[];
};

export type EditAppliancesProps = {
  config: FormInputProps[];
  formData: Record<string, string | undefined>;
  onChange: (value: string, id: string) => void;
  id?: string;
};

export type EditAppliancesFormDataProps = {
  name: string;
  description: string;
};

export type ApplianceDetailsProps = {
  label: string;
  value: string;
  isActive?: boolean;
  icon?: string;
};

type FooterButton = {
  text: string;
  type: ButtonVariantType;
  onButtonClick: () => void;
  disabled?: boolean;
};

export type DrawerFooterConfigProps = {
  save: FooterButton;
  cancel: FooterButton;
};

export type AppConnectorFormDataProps = {
  appConnectorGroup: IdLabelProps[];
  deploymentStatus: IdLabelProps[];
  provisionKey: AppConnectorGroupProvKeyProps[];
  ipAddress: string;
  defaultGateway: string;
  primaryDNS: string;
  secondaryDNS: string;
};

export type MenuItem = {
  id: string;
  name?: string;
  iconClass?: string;
  disabled?: boolean;
};

export type AppConnectorGroupResponse = {
  allAppConnectorGroups: Array<{
    name: string;
    externalId: number;
  }>;
};

export type AppConnectorGroupProvKeyResponse = {
  appConnectorGroupProvKey: Array<{
    id: number;
    name: string;
    nonce: string;
  }>;
};

export type AppConnecorGroupLablel = {
  name: string;
  externalId: number;
};

export type AppConnectorGroupProvKeyProps = {
  id: string | number;
  label: string;
  nonce?: string;
};

export type IdLabelProps = {
  id: string | number;
  label: string;
};
