/* eslint-disable @up/unified-platform/max-file-lines */
import {
  calculateNextUpgrade,
  convertMinutesToAMPM,
  parseDateWithYearTimeLabelLongFormat,
} from "../utils/utils";
import {
  FORWARDING,
  listWeek,
  MGT,
  TIMEZONES,
  VM_PRODUCT_MAP,
  VM_SIZE_MAP,
  VM_SIZES,
  DEFAULT_IP_ADDRESS,
} from "../constants";
import { type ConnectorListItemsProps } from "./components/AppConnector/AppConnector";
import { type Items } from "./components/AppliancesDetailsContent/AppliancesDetailsContent";
import {
  type ApplianceInterfacesTableRowItemProps,
  type VersionData,
  type ApplianceNetworkProps,
  type UpgradeScheduleFormProps,
  type IPAddressInfoData,
  type InterfacesListProps,
  type HaConfigProps,
  type DHCPRangeProps,
  type DHCPLeaseProps,
} from "./types";
import { UPGRADE_SCHEDULE_DATA } from "./components/UpgradeSchedule/UpgradeSchedule.data";

export type AppConnectorPropertyList = {
  groupName: {
    id: string | number;
    name: string;
    nonce: string;
  };
  appConnectorGroup: {
    name: string;
    externalId: string | number;
  };
  provisioningKey: string;
  provisioningKeyName: string;
  status: string;
  name: string;
  ipAddress: string;
  defaultGatewayIp: string;
  primaryDnsServerIp: string;
  secondaryDnsServerIp: string;
};

export type ApplianceDetails = {
  applianceGroup: string;
  currentVersion: string;
  model: string;
  name: string;
  nextUpgrade: number;
  nextVersion: string;
  serialNumber: string;
  status: string;
  description: string;
  deploymentStatus: string;
  nextUpgradeDay: number;
  vmSize: string;
  provTemplateName: string;
};

export type Interface = InterfacesListProps & {
  adminStatus: string;
  description: string;
  details: string;
  detailsDescription: string;
  dhcpIp: string;
  dhcpStatus: string;
  id: number;
  ipAddress: string;
  linkStatus: string;
  name: string;
  type: string;
  haConfig: HaConfigProps;
  lanDhcpConfig: LanDhcpConfig;
  upLinkMode: string;
  trafficDistributionMode: string;
};

type LanDhcpConfig = {
  dhcpRange: DHCPRangeProps[];
  defaultLeaseTime: string;
  maxLeaseTime: string;
  dhcpLease: DHCPLeaseProps[];
  peerDhcpIp: string;
  dhcpOptions: {
    defaultGateway: string[];
    domainNameServersList: string[];
    domainSearch: string[];
  };
};

export type ApplianceInterface = Array<
  InterfacesListProps & {
    adminStatus: string;
    description: string;
    details: string;
    detailsDescription: string;
    dhcpIp: string;
    dhcpStatus: string;
    id: number;
    ipAddress: string;
    linkStatus: string;
    name: string;
    type: string;
    upLinkMode: string;
    trafficDistributionMode: string;
    haConfig: HaConfigProps;
    lanDhcpConfig: LanDhcpConfig;
    subInterfaces: Interface[];
  }
>;

export type ApplianceNetworkInfo = {
  dnsCacheEnabled: boolean;
  trafficDistributionMode: string;
  ziaTunnelEnabled: string;
};

export type ApplianceSupportTunnel = {
  allowSupportTunnelAction: boolean;
  enableSupportTunnel: boolean;
  tunnelIp?: string;
  tunnelPort?: string;
};

export type UpgradeItem = {
  upgradeTime: number;
  version: string;
};

export type ApplianceUpgrade = {
  current: UpgradeItem;
  next: UpgradeItem & {
    upgradeDay: number;
  };
};

export type ApplianceDetailFromServer = {
  appConnector: AppConnectorPropertyList;
  applianceDetails: ApplianceDetails;
  applianceInterfaceList: ApplianceInterface;
  applianceNetworkInfo: ApplianceNetworkInfo;
  applianceSupportTunnel: ApplianceSupportTunnel;
  applianceUpgrade: ApplianceUpgrade;
  locationId: number;
  location: string;
  id: number;
  applianceId?: number;
  applianceVirtualInterfaceList: IPAddressInfoData[];
  provId?: number;
  timeZone?: string;
};

export type TransformedApplianceDetails = {
  applianceDetails: Items[];
  applianceConnector: { connectorList: ConnectorListItemsProps[] };
  applianceConnectorInterface: { connectorList: ConnectorListItemsProps[] };
  applianceNetwork: Omit<ApplianceNetworkProps, "onSave" | "onReset">;
  applianceInterface: ApplianceInterfacesTableRowItemProps[];
  applianceUpgrade: VersionData & {
    initialScheduledData: UpgradeScheduleFormProps;
  };
  applianceTroubleshooting: Array<{ id: string; isEnabled: boolean }>;
  apiResponse: ApplianceDetailFromServer;
  applianceStatus: Items[];
  applianceDetailVersion: Items[];
  vmInterface: {
    managementInterface: Items[];
    forwardInterface: Items[];
  };
};

export const networkDistributionMap = {
  BALANCED: "Balanced",
  BEST_LINK: "Best-link",
};

export const networkDistributionDetailsMap = {
  BALANCED: "Balanced",
  BEST_LINK: "Best-link",
  STANDBY: "Standby",
  Active: "Active",
};

export const networkZIATunnelMap = {
  ENCRYPTED_DTLS: "Encrypted",
  PLAIN_UDP: "Unencrypted",
};

const adminStatusMap = {
  up: "success",
  down: "error",
};

export default function responseTransformer(
  data: ApplianceDetailFromServer,
): TransformedApplianceDetails {
  const applianceDetailsFromServer = data?.applianceDetails;
  const physicalDeviceContent = [
    {
      label: "appliances.details.model",
      value: applianceDetailsFromServer?.model,
    },
    {
      label: "appliances.details.serial-number",
      value: applianceDetailsFromServer?.serialNumber,
    },
  ];
  const virtualDeviceContent = [
    {
      label: "appliances.details.vendor",
      value:
        VM_PRODUCT_MAP[
          applianceDetailsFromServer?.model as keyof typeof VM_PRODUCT_MAP
        ],
    },
    {
      label: "appliances.details.vm-size",
      value:
        VM_SIZE_MAP[
          applianceDetailsFromServer?.vmSize as keyof typeof VM_SIZE_MAP
        ],
    },
  ];
  const applianceDetailsCommonContent = [
    {
      label: "appliances.details.name",
      value:
        applianceDetailsFromServer?.name ??
        applianceDetailsFromServer.provTemplateName,
    },
    {
      label: "appliances.details.branch-connector-group",
      value: applianceDetailsFromServer?.applianceGroup,
    },
    {
      label: "LOCATION",
      value: data?.location,
      id: data?.locationId,
      interactive: true,
    },
  ];

  const applianceDetails = data?.applianceDetails?.serialNumber
    ? [...applianceDetailsCommonContent, ...physicalDeviceContent]
    : [...applianceDetailsCommonContent, ...virtualDeviceContent];

  const applianceUpgradeDay = applianceDetailsFromServer?.nextUpgradeDay ?? 1;
  const applianceUpgradeTime = data?.applianceUpgrade?.next?.upgradeTime ?? 1;
  const scheduleApplianceTime = convertMinutesToAMPM(
    Number(applianceUpgradeTime) / 60,
  );
  const applianceHour =
    (typeof scheduleApplianceTime !== "string" &&
      scheduleApplianceTime?.hour) ||
    UPGRADE_SCHEDULE_DATA.initialScheduledData?.hour;
  const applianceMinute =
    (typeof scheduleApplianceTime !== "string" &&
      scheduleApplianceTime?.minute) ||
    UPGRADE_SCHEDULE_DATA?.initialScheduledData?.minute;
  const applianceMeridiem =
    (typeof scheduleApplianceTime !== "string" &&
      scheduleApplianceTime?.meridiem) ||
    UPGRADE_SCHEDULE_DATA?.initialScheduledData?.meridiem;
  const nextUpgradeDate = calculateNextUpgrade(
    applianceUpgradeDay - 1,
    applianceHour,
    applianceMinute,
    applianceMeridiem,
  );
  const applianceDetailVersion = [
    {
      label: "appliances.details.current-version",
      value: applianceDetailsFromServer?.currentVersion,
    },
    {
      label: "appliances.details.next-version",
      value: applianceDetailsFromServer?.nextVersion,
    },
    {
      label: "appliances.details.next-upgrade",
      value:
        data?.applianceDetails?.deploymentStatus === "DEPLOYED"
          ? (nextUpgradeDate ?? "--")
          : "--",
      timeZone:
        data?.applianceDetails?.deploymentStatus === "DEPLOYED"
          ? (TIMEZONES[data?.timeZone as keyof typeof TIMEZONES] ?? "")
          : "",
    },
  ];

  const applianceState = applianceDetailsFromServer?.status;
  const deploymentStatus = applianceDetailsFromServer.deploymentStatus;
  const applianceStatus = [
    {
      label: "OPERATIONAL_STATUS",
      value:
        applianceState === null ? "N/A" : applianceDetailsFromServer.status,
    },
    {
      label: "appliances.details.provisioning-status",
      value:
        deploymentStatus == null
          ? "N/A"
          : applianceDetailsFromServer.deploymentStatus,
    },
    /**
     * TODO: Phase 4 Task
     */
    // {
    //   label: "appliances.details.zt-device-status",
    //   value: applianceDetailsFromServer.status,
    // },
  ];

  const applianceNetwork = {
    dnsCache: {
      heading: "locations.appliance.dnsCache.heading",
      selectedValue: data?.applianceNetworkInfo?.dnsCacheEnabled
        ? "Enabled"
        : "Disabled",
      options: [
        { label: "locations.appliance.dnsCache.label1", value: "Enabled" },
        {
          label: "locations.appliance.dnsCache.label2",
          value: "Disabled",
        },
      ],
    },
    tunnelMode: {
      heading: "locations.appliance.tunnelMode.heading",
      selectedValue:
        networkZIATunnelMap?.[
          data?.applianceNetworkInfo
            ?.ziaTunnelEnabled as keyof typeof networkZIATunnelMap
        ],
      options: [
        { label: "locations.appliance.tunnelMode.label1", value: "Encrypted" },
        {
          label: "locations.appliance.tunnelMode.label2",
          value: "Unencrypted",
        },
      ],
    },
    distributionMode: {
      heading: "locations.appliance.distributionMode.heading",
      selectedValue:
        networkDistributionMap?.[
          data?.applianceNetworkInfo
            ?.trafficDistributionMode as keyof typeof networkDistributionMap
        ],
      options: [
        {
          label: "locations.appliance.distributionMode.label1",
          value: "Balanced",
        },
        {
          label: "locations.appliance.distributionMode.label2",
          value: "Best-link",
        },
      ],
    },
    isVirtualDevice: !applianceDetailsFromServer?.serialNumber,
  };

  const upgradeDay = data?.applianceUpgrade?.next?.upgradeDay ?? 1;
  const upgradeTime = data?.applianceUpgrade?.next?.upgradeTime ?? 1;
  const scheduleUpgradeTime = convertMinutesToAMPM(Number(upgradeTime) / 60);
  const upgradeHour =
    (typeof scheduleUpgradeTime !== "string" && scheduleUpgradeTime?.hour) ||
    UPGRADE_SCHEDULE_DATA?.initialScheduledData?.hour;
  const upgradeMinute =
    (typeof scheduleUpgradeTime !== "string" && scheduleUpgradeTime?.minute) ||
    UPGRADE_SCHEDULE_DATA?.initialScheduledData?.minute;
  const upgradeMeridiem =
    (typeof scheduleUpgradeTime !== "string" &&
      scheduleUpgradeTime?.meridiem) ||
    UPGRADE_SCHEDULE_DATA?.initialScheduledData?.meridiem;

  const applianceUpgrade = {
    initialScheduledData: {
      day:
        listWeek()?.find((x) => x.val === upgradeDay)?.label ??
        UPGRADE_SCHEDULE_DATA?.initialScheduledData?.day,
      hour: upgradeHour,
      minute: upgradeMinute,
      meridiem: upgradeMeridiem,
    },
    currentVersion: {
      title: "appliances.upgradeSchedule.currentVersionTitle",
      data: [
        {
          label: "appliances.upgradeSchedule.version",
          value: data?.applianceUpgrade?.current?.version || "",
        },
        {
          label: "appliances.upgradeSchedule.lastUpgraded",
          value: data?.applianceUpgrade?.current?.upgradeTime
            ? parseDateWithYearTimeLabelLongFormat(
                data.applianceUpgrade?.current?.upgradeTime,
              )
            : "",
          timeZone: TIMEZONES[data?.timeZone as keyof typeof TIMEZONES] ?? "",
        },
      ],
    },
    nextVersion: {
      title: "appliances.upgradeSchedule.nextVersionTitle",
      data: [
        {
          label: "appliances.upgradeSchedule.version",
          value: data?.applianceUpgrade?.next?.version ?? "",
        },
        {
          label: "appliances.upgradeSchedule.scheduled",
          value: nextUpgradeDate || "",
          timeZone: TIMEZONES[data?.timeZone as keyof typeof TIMEZONES] ?? "",
        },
      ],
      upgradeDay: data?.applianceUpgrade?.next?.upgradeDay || 1,
    },
  };

  const applianceTroubleshooting = [
    {
      id: "1",
      isEnabled: data?.applianceSupportTunnel?.enableSupportTunnel,
    },
    {
      id: "2",
      isEnabled: data?.applianceSupportTunnel?.allowSupportTunnelAction,
    },
  ];

  const interfaces: ApplianceInterfacesTableRowItemProps[] = [];

  data?.applianceInterfaceList?.map(
    ({
      id,
      name,
      type,
      adminStatus,
      description,
      details,
      detailsDescription,
      dhcpIp,
      dhcpStatus,
      ipAddress,
      linkStatus,
      subInterfaces,
      upLinkMode,
      trafficDistributionMode,
    }) => {
      interfaces.push({
        id,
        interfaces: {
          name,
          meta: description,
        },
        type,
        ipAddress,
        dhcp: {
          name: dhcpStatus,
          meta: dhcpIp,
        },
        details: {
          name: details,
          meta: detailsDescription,
        },
        interfaceDetail: {
          name: networkDistributionDetailsMap?.[
            upLinkMode as keyof typeof networkDistributionDetailsMap
          ]
            ? networkDistributionDetailsMap?.[
                upLinkMode as keyof typeof networkDistributionDetailsMap
              ]
            : upLinkMode,
          meta: networkDistributionDetailsMap?.[
            trafficDistributionMode as keyof typeof networkDistributionDetailsMap
          ]
            ? networkDistributionDetailsMap?.[
                trafficDistributionMode as keyof typeof networkDistributionDetailsMap
              ]
            : trafficDistributionMode,
        },
        adminStatus: {
          value: adminStatus,
          status: adminStatusMap[adminStatus as keyof typeof adminStatusMap],
        },
        linkStatus: {
          value: linkStatus,
          status: adminStatusMap[linkStatus as keyof typeof adminStatusMap],
        },
        subInterfaces: subInterfaces?.map(
          ({
            id,
            ipAddress,
            adminStatus,
            details,
            detailsDescription,
            dhcpIp,
            dhcpStatus,
            name,
            linkStatus,
            type,
            upLinkMode,
            trafficDistributionMode,
          }) => ({
            id,
            name,
            interfaces: {
              name: name.split(".")[0],
              value: parseInt(name.split(".")[1]),
              meta: description,
            },
            type,
            ipAddress,
            dhcp: {
              name: dhcpStatus,
              meta: dhcpIp,
            },
            details: {
              name: details,
              meta: detailsDescription,
            },
            interfaceDetail: {
              name: networkDistributionDetailsMap?.[
                upLinkMode as keyof typeof networkDistributionDetailsMap
              ]
                ? networkDistributionDetailsMap?.[
                    upLinkMode as keyof typeof networkDistributionDetailsMap
                  ]
                : upLinkMode,
              meta: networkDistributionDetailsMap?.[
                trafficDistributionMode as keyof typeof networkDistributionDetailsMap
              ]
                ? networkDistributionDetailsMap?.[
                    trafficDistributionMode as keyof typeof networkDistributionDetailsMap
                  ]
                : trafficDistributionMode,
            },
            adminStatus: {
              value: adminStatus,
              status:
                adminStatusMap[adminStatus as keyof typeof adminStatusMap],
            },
            linkStatus: {
              value: linkStatus,
              status: adminStatusMap[linkStatus as keyof typeof adminStatusMap],
            },
          }),
        ),
      });
    },
  );

  const applianceConnector = {
    connectorList: [
      {
        label: "appliances.app-connector.group",
        value: data?.appConnector?.appConnectorGroup?.name ?? "--",
      },
      {
        label: "appliances.app-connector.deployment-status",
        value:
          data?.appConnector?.status === "ENABLE"
            ? "appliances.app-connector.status.active-active"
            : data?.appConnector?.status || "--",
      },
      {
        label: "appliances.app-connector.provisioning-key",
        value: data?.appConnector?.provisioningKeyName ?? "--",
        meta: data?.appConnector?.provisioningKey,
      },
    ],
  };

  const managementInterface = data?.applianceVirtualInterfaceList?.find(
    (item) => item?.type === MGT,
  );
  const forwardInterface = data?.applianceVirtualInterfaceList?.find(
    (item) => item?.type === FORWARDING,
  );
  const isSmall = data?.applianceDetails.vmSize === VM_SIZES.SMALL;
  const isMedium = data?.applianceDetails.vmSize === VM_SIZES.MEDIUM;
  const isLarge = data?.applianceDetails.vmSize === VM_SIZES.LARGE;

  const showHADeployementStatus =
    forwardInterface?.haVirtualIp !== DEFAULT_IP_ADDRESS;
  const showLB = data?.applianceDetails?.vmSize != VM_SIZES.SMALL; // Load Balance IP will only show when vmSize is other then the SMALL
  const vmInterface = {
    managementInterface: [
      {
        label: "locations.appliance.interface.dhcp",
        value: managementInterface?.dhcpStatus ?? "",
      },

      {
        label: "appliances.drawer.shared.ipAddress",
        value: managementInterface?.ipAddress ?? "",
        translate:
          managementInterface?.dhcpStatus !== "Disabled"
            ? "appliances.drawer.shared.automatic"
            : "",
      },
      {
        label: "appliances.drawer.shared.gateway",
        value: managementInterface?.defaultGateway ?? "",
        translate:
          managementInterface?.dhcpStatus !== "Disabled"
            ? "appliances.drawer.shared.automatic"
            : "",
      },
      {
        label: "appliances.drawer.shared.dns",
        value: managementInterface?.primaryDns ?? "",
      },
      {
        label: "appliances.drawer.shared.secDNS",
        value: managementInterface?.secondaryDns ?? "",
      },
    ],
    forwardInterface: [
      {
        label: "locations.appliance.interface.dhcp",
        value: forwardInterface?.dhcpStatus ?? "",
      },
      ...(isSmall
        ? [
            {
              label: "appliances.drawer.shared.ipAddress",
              value: forwardInterface?.ipAddress ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
          ]
        : []),
      ...(isMedium
        ? [
            {
              label: "appliances.drawer.shared.serviceIP1",
              value: forwardInterface?.ipAddress ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
            {
              label: "appliances.drawer.shared.serviceIP2",
              value: forwardInterface?.serviceIpAddress2 ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
          ]
        : []),
      ...(isLarge
        ? [
            {
              label: "appliances.drawer.shared.serviceIP1",
              value: forwardInterface?.ipAddress ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
            {
              label: "appliances.drawer.shared.serviceIP2",
              value: forwardInterface?.serviceIpAddress2 ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
            {
              label: "appliances.drawer.shared.serviceIP3",
              value: forwardInterface?.serviceIpAddress3 ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
          ]
        : []),
      {
        label: "appliances.drawer.shared.gateway",
        value: forwardInterface?.defaultGateway ?? "",
        translate:
          forwardInterface?.dhcpStatus !== "Disabled"
            ? "appliances.drawer.shared.automatic"
            : "",
      },
      {
        label: "appliances.drawer.shared.dns",
        value: forwardInterface?.primaryDns ?? "",
      },
      {
        label: "appliances.drawer.shared.secDNS",
        value: forwardInterface?.secondaryDns ?? "",
      },

      ...(showHADeployementStatus
        ? [
            {
              label: "appliances.drawer.shared.haDeploymentStatus",
              value: "Active-Standby",
            },
            {
              label: "appliances.drawer.shared.haVirtualIp",
              value: forwardInterface?.haVirtualIp ?? "",
              translate:
                forwardInterface?.dhcpStatus !== "Disabled"
                  ? "appliances.drawer.shared.automatic"
                  : "",
            },
          ]
        : []),

      ...(showLB
        ? [
            {
              label: "appliances.highAvailabilityConfig.load-balancer",
              value: forwardInterface?.loadBalanceIp ?? "",
              translate: "appliances.drawer.shared.automatic",
            },
          ]
        : []),
    ],
  };

  const applianceConnectorInterface = {
    connectorList: [
      {
        label: "locations.appliance.interface.dhcp",
        value: data?.appConnector?.ipAddress ?? "--",
      },
      {
        label: "appliances.drawer.shared.ipAddress",
        value: data?.appConnector?.defaultGatewayIp ?? "--",
      },
      {
        label: "appliances.drawer.shared.gateway",
        value: data?.appConnector?.primaryDnsServerIp ?? "--",
      },
      {
        label: "appliances.drawer.shared.dns",
        value: data?.appConnector?.secondaryDnsServerIp ?? "--",
      },
    ],
  };

  return {
    applianceDetails,
    applianceNetwork,
    applianceUpgrade,
    applianceTroubleshooting,
    applianceConnector,
    applianceConnectorInterface,
    applianceInterface: interfaces,
    apiResponse: data,
    applianceStatus,
    applianceDetailVersion,
    vmInterface,
  };
}
