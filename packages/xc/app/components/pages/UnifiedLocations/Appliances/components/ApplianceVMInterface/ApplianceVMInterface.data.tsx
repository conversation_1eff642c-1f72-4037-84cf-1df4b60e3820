import { type VMInterfaceProps } from "./ApplianceVMInterface";

export const MGT_INTERFACE_LIST_DATA: Array<{
  label: string;
  value: string;
}> = [
  {
    label: "locations.appliance.interface.dhcp",
    value: "Enabled",
  },
  {
    label: "appliances.drawer.shared.ipAddress",
    value: "Automatic (***********)",
  },
  {
    label: "appliances.drawer.shared.gateway",
    value: "Automatic (*************)",
  },
  {
    label: "appliances.drawer.shared.dns",
    value: "*******",
  },
];

export const FI_INTERFACE_LIST_DATA: Array<{
  label: string;
  value: string;
}> = [
  {
    label: "locations.appliance.interface.dhcp",
    value: "Enabled",
  },
  {
    label: "appliances.drawer.shared.ipAddress",
    value: "Automatic (***********)",
  },
  {
    label: "appliances.drawer.shared.gateway",
    value: "Automatic (*************)",
  },
  {
    label: "appliances.drawer.shared.dns",
    value: "*******",
  },
  {
    label: "appliances.drawer.shared.haVirtualIp",
    value: "Automatic (*********)",
  },
  {
    label: "appliances.highAvailabilityConfig.load-balancer",
    value: "Automatic (***********)",
  },
];

export const VM_INTERFACE_DATA = {
  forwardInterface: FI_INTERFACE_LIST_DATA,
  managementInterface: MGT_INTERFACE_LIST_DATA,
  forwardInterfaceHeading: "appliances.vmInterface.heading.forwardInterface",
  mgtInterfaceHeading: "appliances.vmInterface.heading.mgtInterface",
  readOnly: false,
} as VMInterfaceProps;
