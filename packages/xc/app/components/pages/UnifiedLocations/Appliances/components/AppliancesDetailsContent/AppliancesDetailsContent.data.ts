import { type ApplianceDetailsProps } from "./AppliancesDetailsContent";

export const ApplianceDetailsData: ApplianceDetailsProps = {
  id: 1,
  heading: "appliances.details.appliance",
  applianceName: "ord-gw-1",
  deleteIconText: "DELETE",
  editIconText: "EDIT",
  groups: [
    {
      heading: undefined,
      list: [
        {
          label: "appliances.details.name",
          value: "ord-gw-",
        },
        {
          label: "appliances.details.model",
          value: "ZT-400",
        },
        {
          label: "appliances.details.serial-number",
          value: "KSD234234324",
        },
        {
          label: "appliances.details.appliance-group",
          value: "ord_dg1",
        },
      ],
    },
  ],
  applianceStatus: [
    {
      label: "Operational Status",
      value: "ACTIVE",
    },
    {
      label: "Provisioning Status",
      value: "DEPLOYED",
    },
    {
      label: "ZT Device Status",
      value: "ACTIVE",
    },
  ],
  applianceDetailVersion: [
    {
      label: "appliances.details.current-version",
      value: "1734332952",
    },

    {
      label: "appliances.details.next-version",
      value: "7.8.2+a2cd65bf5682d0f2121",
    },
    {
      label: "appliances.details.next-upgrade",
      value: "September 1, 2024 12:00 AM",
    },
  ],
  handleEditLocation: () => console.log("Edit icon clicked"),
  onDelete: (id: string) => console.log(id, "Appliacne Deleted"),
  readOnly: false,
};
