"use client";

import { ZDataTable } from "@xc/legacy-components";
import { useEffect, useRef, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import useSWRMutation from "swr/mutation";
import { faPlus } from "@fortawesome/pro-regular-svg-icons";
import { t } from "i18next";
import { debounce } from "lodash";
import useSWR from "swr";
import { APPLIANCE_ENDPOINTS } from "../../config/apiUtils";
import { paginationGetReq } from "../../../Locations/config/apiUtils";
import { tablePaginationData } from "../../../Locations/LocationList/LocationTableConfig";
import Tooltip from "../../../Components/Tootip/Tooltip";
import { type ResponseBody, type FilterOptionsProps } from "../../types";
import { ButtonConfig } from "../../../Locations/LocationsList";
import { hasProductRights } from "../../../helper";
import { ADMIN_ACCESS_TYPE, LOCATION_PRODUCTS } from "../../../constants";
import ApplianceButtonGroup, { type ButtonProps } from "./ApplianceButtonGroup";
import ApplianceTableControls from "./ApplianceTableControls";
import { tableColumnFn } from "./ApplianceListConfig";
import responseTransformer from "./ApplianceListTransformer";
import { useFlags } from "@/context/FeatureFlags";
import TableLoader from "@/components/TableLoader/TableLoader";
import { getDataTestId } from "@/utils/utils";
import { API_ENDPOINTS, getReq } from "@/utils/apiHelper";
import { endpointConditionHandler } from "@/app/onboarding/apiHelper";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";

const Appliances = () => {
  const ID = "appliance-list";
  const [search, setSearch] = useState("");
  const { can } = useFlags();
  const router = useRouter();
  const path = usePathname();
  const tableRef = useRef<{ refreshData: () => void }>(null);
  const [hidePagination, setHidePagination] = useState(false);
  const [refreshData, setRefreshData] = useState(false);
  const [callSyncAppliance, setCallSyncAppliance] = useState(false);
  const [checkSyncStatus, setCheckSyncStatus] = useState(false);
  const [userRoleFilters, setUserRoleFilters] = useState<FilterOptionsProps[]>(
    [],
  );
  const [provStatusFilters, setProvStatusFilters] = useState<
    FilterOptionsProps[]
  >([]);

  const [userStatusFilters, setUserStatusFilters] = useState<
    FilterOptionsProps[]
  >([]);
  const productAccessInfo = useProductAccessProvider();
  const hasProducts = hasProductRights(LOCATION_PRODUCTS, productAccessInfo);

  const { featurePermissions } = (productAccessInfo.features?.CLOUD_CONNECTOR ??
    {}) as {
    featurePermissions?: Record<string, string>;
  };

  const readOnly =
    featurePermissions?.EDGE_CONNECTOR_CLOUD_PROVISIONING !==
      ADMIN_ACCESS_TYPE &&
    featurePermissions?.EDGE_CONNECTOR_TEMPLATE !== ADMIN_ACCESS_TYPE;

  if (!hasProducts || !can("showUnifiedLocations")) {
    window.location.replace("/error");
  }

  useSWR(
    endpointConditionHandler(
      callSyncAppliance,
      `${API_ENDPOINTS.ZUXP}/unified-locations/sync-appliances`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setCheckSyncStatus(true);
          void syncApplianceStatus.mutate();
          setCallSyncAppliance(false);
        } else {
          setCheckSyncStatus(false);
          setCallSyncAppliance(false);
          tableRef?.current?.refreshData();
        }
      },
    },
  );

  const syncApplianceStatus = useSWR(
    endpointConditionHandler(
      checkSyncStatus,
      `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/sync-status`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setTimeout(() => {
            void syncApplianceStatus.mutate();
          }, 3000);
        } else {
          setCheckSyncStatus(false);
          tableRef?.current?.refreshData();
        }
      },
    },
  );

  useEffect(() => {
    setRefreshData(true);
    if (refreshData) {
      tableRef?.current?.refreshData();
    }
  }, [
    userRoleFilters,
    search,
    userStatusFilters,
    provStatusFilters,
    refreshData,
  ]);

  const paginationData = useSWRMutation("paginationLocation", paginationGetReq);
  const fetchRows = (
    currentPage: number,
    pageSize: number,
    sortBy: string,
    sortDirection: string,
  ) =>
    paginationData
      .trigger({
        EndpointUrl: APPLIANCE_ENDPOINTS.fetchAppliances({
          limit: pageSize,
          offset: currentPage,
          search: search,
          fieldName: sortBy ?? "",
          orderBy: sortDirection?.toLowerCase() ?? "",
          type: userRoleFilters?.[0]?.id ?? "",
          status: userStatusFilters?.[0]?.value ?? "",
          deploymentStatus: provStatusFilters?.[0]?.value ?? "",
        }),
      })
      .then((response) => {
        const { data, pagination } = response as ResponseBody;
        setHidePagination(!(data.length > 0));

        return {
          totalItems: pagination.totalCount,
          totalPages: pagination.totalPages,
          items: data.length > 0 ? responseTransformer(data) : [],
        };
      })
      .catch((err) => console.log(err));

  const ButtonMap: ButtonProps[] = [
    {
      text: "ADD",
      onClick: () =>
        void router.push(
          `/ec/administration/branch-provisioning-templates?filter=BC&URI="${path.toString()}"`,
        ),
      icon: faPlus,
      type: "primary",
      id: "add",
    },
  ];

  const translatedTableColumnData = tableColumnFn().map((tcd) => {
    if (typeof tcd.name === "string") {
      tcd.name = <Tooltip content={t(tcd.name)} />;
    }

    return tcd;
  });
  //TODO: This is done explicitly as Pagination is not yet done on this page.
  const updatedPaginationData = {
    ...tablePaginationData,
  };

  const handleUserRoleFilter = (options: FilterOptionsProps[]) => {
    setUserRoleFilters(options);
  };

  const handleUserStatusFilter = (options: FilterOptionsProps[]) => {
    setUserStatusFilters(options);
  };

  const handleProvStatusFilter = (options: FilterOptionsProps[]) => {
    setProvStatusFilters(options);
  };

  const handleResetFilter = (options: FilterOptionsProps[]) => {
    setUserRoleFilters(options);
    setUserStatusFilters(options);
    setProvStatusFilters(options);
  };

  const handleApplianceSync = () => {
    setCallSyncAppliance(true);
  };

  return (
    <div
      className="flex flex-col gap-rem-80"
      data-testid={getDataTestId("container", ID)}
    >
      <ApplianceButtonGroup id={ID} buttons={ButtonMap} readOnly={readOnly} />
      <ApplianceTableControls
        id={ID}
        search={{
          onChange: debounce((val: string) => setSearch(val), 300),
          text: search,
        }}
        userRoleFilters={userRoleFilters}
        userStatusFilters={userStatusFilters}
        provStatusFilters={provStatusFilters}
        handleUserRoleFilter={handleUserRoleFilter}
        handleUserStatusFilter={handleUserStatusFilter}
        handleProvStatusFilter={handleProvStatusFilter}
        handleResetFilter={handleResetFilter}
        buttonConfig={{
          ...ButtonConfig,
          text: "appliances.list.sync-appliance",
          onClick: handleApplianceSync,
          loading: checkSyncStatus || callSyncAppliance,
        }}
      />
      <ZDataTable
        id={ID}
        columns={translatedTableColumnData}
        pagination={updatedPaginationData}
        fetchRows={fetchRows}
        noSelection
        sorting={{
          enabled: true,
        }}
        ref={tableRef}
        isHeaderGrey={true}
        shouldRerenderOnResize={true}
        loadingComponent={<TableLoader rows={7} />}
        hidePagination={hidePagination}
      />
    </div>
  );
};

export default Appliances;
