import {
  type FilterOptionsProps,
  type ApplianceInterfacesProps,
} from "../../types";

export const APPLIANCE_INTERFACES_TABLE_DATA = {
  tableRowData: [
    {
      id: 1,
      interfaces: {
        name: "GE1",
        meta: "",
      },
      type: "LAN",
      ipAddress: "",
      dhcp: {
        name: "",
        meta: "",
      },
      details: {
        name: "",
        meta: "",
      },
      interfaceDetail: {
        name: "",
        meta: "",
      },
      adminStatus: {
        value: "",
        status: "",
      },
      linkStatus: {
        value: "",
        status: "",
      },
      subInterfaces: [
        {
          id: 2,
          interfaces: {
            name: "GE1",
            value: 100,
            meta: "",
          },
          type: "LAN",
          ipAddress: "10",
          dhcp: {
            name: "Server",
            meta: "******** / 24",
          },
          details: {
            name: "HA enabled",
            meta: "Best-link",
          },
          interfaceDetail: {
            name: "",
            meta: "",
          },
          adminStatus: {
            value: "Up",
            status: "success",
          },
          linkStatus: {
            value: "Up",
            status: "success",
          },
        },
        {
          id: 3,
          interfaces: {
            name: "GE1",
            value: 200,
            meta: "",
          },
          type: "LAN",
          ipAddress: "10",
          dhcp: {
            name: "Server",
            meta: "******** / 24",
          },
          details: {
            name: "",
            meta: "",
          },
          interfaceDetail: {
            name: "",
            meta: "",
          },
          adminStatus: {
            value: "Up",
            status: "success",
          },
          linkStatus: {
            value: "Up",
            status: "success",
          },
        },
      ],
    },
    {
      id: 4,
      interfaces: {
        name: "GE2",
        meta: "",
      },
      type: "MGT",
      ipAddress: "67",
      dhcp: {
        name: "",
        meta: "",
      },
      details: {
        name: "Enabled",
        meta: "",
      },
      interfaceDetail: {
        name: "",
        meta: "",
      },
      adminStatus: {
        value: "",
        status: "",
      },
      linkStatus: {
        value: "",
        status: "",
      },
    },
    {
      id: 5,
      interfaces: {
        name: "GE5",
        meta: "Comcast",
      },
      type: "WAN",
      ipAddress: "67",
      dhcp: {
        name: "Enabled",
        meta: "",
      },
      details: {
        name: "Active",
        meta: "Best-link",
      },
      interfaceDetail: {
        name: "",
        meta: "",
      },
      adminStatus: {
        value: "Up",
        status: "success",
      },
      linkStatus: {
        value: "Down",
        status: "error",
      },
    },
    {
      id: 6,
      interfaces: {
        name: "GE6",
        meta: "AT&T",
      },
      type: "WAN",
      ipAddress: "67",
      dhcp: {
        name: "Enabled",
        meta: "",
      },
      details: {
        name: "Standby",
        meta: "Best-link",
      },
      interfaceDetail: {
        name: "",
        meta: "",
      },
      adminStatus: {
        value: "Down",
        status: "warning",
      },
      linkStatus: {
        value: "Down",
        status: "warning",
      },
    },
  ],
  tableColumnFn: () => [
    {
      id: "interface",
      name: "locations.appliance.interface.interface",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
    {
      id: "type",
      name: "locations.appliance.interface.type",
      isSortable: true,
      isHidden: false,
      width: "10%",
    },
    {
      id: "ipAddress",
      name: "locations.appliance.interface.ip-address",
      isSortable: false,
      isHidden: false,
      width: "10%",
    },
    {
      id: "dhcp",
      name: "locations.appliance.interface.dhcp",
      isSortable: false,
      isHidden: false,
      width: "10%",
    },
    {
      id: "details",
      name: "locations.appliance.interface.details",
      isSortable: false,
      isHidden: false,
      width: "15%",
    },
    {
      id: "adminStatus",
      name: "locations.appliance.interface.admin-status",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
    {
      id: "linkStatus",
      name: "locations.appliance.interface.link-status",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
    {
      id: "actions",
      name: "",
      isSortable: true,
      isHidden: false,
      width: "5%",
      isPinned: true,
      pinnedDirection: "right",
    },
  ],
};

const interfacesOptions = [
  {
    id: "ALL",
    name: "locations.appliance.interface.filters.all-types",
  },
  {
    id: "LAN",
    name: "locations.appliance.interface.filters.lan-interfaces",
  },
  {
    id: "WAN",
    name: "locations.appliance.interface.filters.wan-interfaces",
  },
];

export const APPLIANCE_INTERFACES_DATA: ApplianceInterfacesProps = {
  interfacesOptions: interfacesOptions,
  tableRowData: APPLIANCE_INTERFACES_TABLE_DATA.tableRowData,
  columnData: APPLIANCE_INTERFACES_TABLE_DATA.tableColumnFn(),
  onSortChange: (value: string) => console.log(value, "on sort change"),
  onInterfacesChange: (options: FilterOptionsProps[]) =>
    console.log("interfaces Filters Values: ", options),
  readOnly: false,
};
