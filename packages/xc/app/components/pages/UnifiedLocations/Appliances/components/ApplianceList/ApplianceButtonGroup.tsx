import { Button, type ButtonVariantType } from "@zs-nimbus/core";
import { type IconProp } from "@fortawesome/fontawesome-svg-core";
import { useTranslation } from "react-i18next";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { getDataTestId } from "@/utils/utils";

export type ButtonProps = {
  text: string;
  onClick: () => void;
  icon?: IconProp;
  type: ButtonVariantType;
  id: string;
  loading?: boolean;
};

type ButtonGroupProps = {
  buttons: ButtonProps[];
  id?: string;
  readOnly: boolean;
};

const ApplianceButtonGroup = ({ buttons, id, readOnly }: ButtonGroupProps) => {
  const { t } = useTranslation();
  const ID = getDataTestId("button-group", id);

  return (
    <div className="flex justify-between">
      <div>
        <div
          className="typography-header3 text-semantic-content-base-primary"
          data-testid={ID}
        >
          {t("appliances.list.title")}
        </div>
      </div>
      <div className="flex gap-rem-80">
        {!readOnly &&
          buttons.map((button) => (
            <Button
              id={getDataTestId(button.id, ID)}
              data-testid={getDataTestId(button.id, ID)}
              key={button.text}
              prefixIcon={
                button?.icon && <FontAwesomeIcon icon={button.icon} />
              }
              onClick={button.onClick}
              variant={button.type}
            >
              {t(button.text)}
            </Button>
          ))}
      </div>
    </div>
  );
};

export default ApplianceButtonGroup;
