import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ZButton } from "@xc/legacy-components";
import classNames from "classnames";
import { Button, RadioGroup } from "@zs-nimbus/core";
import {
  type ApplianceNetworkFormProps,
  type ApplianceNetworkProp,
} from "../../types";
import {
  networkDistributionMap,
  networkZIATunnelMap,
} from "../../ApplianceDetailTransformer";
import CardWrapper from "../../../Components/CardWrapper/CardWrapper";
import { getKeyofObject } from "../../../utils/utils";
import { getDataTestId } from "@/utils/utils";

const ApplianceNetwork = ({
  heading,
  applianceNetworkData,
  triggerUpdateAppliance,
  apiResponse,
  readOnly,
}: ApplianceNetworkProp) => {
  const { t } = useTranslation();
  const isDeviceDeployed =
    apiResponse?.applianceDetails.deploymentStatus === "DEPLOYED";
  const ID = "appliance-network";

  const wanCount =
    apiResponse?.applianceInterfaceList?.reduce((count, iface) => {
      let total = iface.type === "WAN" ? 1 : 0;
      total += (iface.subInterfaces || []).reduce(
        (subCount, sub) => subCount + (sub.type === "WAN" ? 1 : 0),
        0,
      );

      return count + total;
    }, 0) ?? 0;

  const initialValues: ApplianceNetworkFormProps = {
    dnsCache: applianceNetworkData.dnsCache.selectedValue,
    tunnelMode: applianceNetworkData.tunnelMode.selectedValue,
    distributionMode:
      applianceNetworkData.distributionMode.selectedValue ?? "Balanced",
  };

  const { isVirtualDevice } = applianceNetworkData;

  const [formValues, setFormValues] =
    useState<ApplianceNetworkFormProps>(initialValues);
  const [isFormChange, setIsFormChange] = useState<boolean>(false);

  const handleSave = () => {
    const { dnsCache, tunnelMode, distributionMode } = formValues;

    const payload = {
      ...apiResponse!,
      applianceNetworkInfo: {
        ...apiResponse!.applianceNetworkInfo,
        dnsCacheEnabled: dnsCache === "Disabled" ? false : true,
        trafficDistributionMode: getKeyofObject(
          networkDistributionMap,
          distributionMode,
        )?.toString(),
        ziaTunnelEnabled: getKeyofObject(
          networkZIATunnelMap,
          tunnelMode,
        )?.toString(),
      },
    };
    triggerUpdateAppliance?.(payload, "network");
  };

  const handleReset = () => {
    setFormValues(initialValues);
    applianceNetworkData.onReset(formValues);
  };

  const ziaTunnelMode = applianceNetworkData.tunnelMode.options?.map(
    (item) => ({
      label: `${item.label === "locations.appliance.tunnelMode.label1" ? t(item.label) + " (DTLS)" : t(item.label) + " (UDP)"}`,
      value: item.value,
    }),
  );

  useEffect(() => {
    setIsFormChange(
      JSON.stringify(formValues) !== JSON.stringify(initialValues),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formValues]);

  return (
    <CardWrapper className="p-4 flex flex-col gap-6" data-testid={ID}>
      <div className="typography-header5 text-semantic-content-base-primary">
        {t(heading)}
      </div>
      <div className="pl-4 pr-2 flex flex-col gap-4">
        {!isVirtualDevice && (
          <div className="flex flex-col gap-1">
            <RadioGroup
              key={formValues.dnsCache}
              groupName="dnsCache"
              groupLabel={t(applianceNetworkData.dnsCache.heading)}
              onChange={(value: string) => {
                setFormValues({ ...formValues, dnsCache: value });
              }}
              defaultValue={formValues.dnsCache}
              itemProps={applianceNetworkData.dnsCache.options.map((item) => ({
                value: item.value,
                labelText: t(item.label),
              }))}
              disabled={!isDeviceDeployed || readOnly}
              orientation="horizontal"
            />
          </div>
        )}

        <div className="flex flex-col gap-1">
          <RadioGroup
            key={formValues.tunnelMode}
            groupName="tunnelMode"
            disabled={!isDeviceDeployed || readOnly}
            groupLabel={t(applianceNetworkData.tunnelMode.heading)}
            onChange={(value: string) => {
              setFormValues({ ...formValues, tunnelMode: value });
            }}
            defaultValue={formValues.tunnelMode}
            itemProps={ziaTunnelMode.map((item) => ({
              value: item.value,
              labelText: t(item.label),
            }))}
            orientation="horizontal"
          />
        </div>
        {!isVirtualDevice && wanCount > 1 && (
          <div className="flex flex-col gap-1">
            <RadioGroup
              key={formValues.distributionMode}
              disabled={readOnly}
              groupName="distributionMode"
              groupLabel={t(applianceNetworkData.distributionMode.heading)}
              onChange={(value: string) => {
                setFormValues({ ...formValues, distributionMode: value });
              }}
              defaultValue={formValues.distributionMode}
              itemProps={applianceNetworkData.distributionMode.options.map(
                (item) => ({ value: item.value, labelText: t(item.label) }),
              )}
              orientation="horizontal"
            />
          </div>
        )}
      </div>

      <div className="flex flex-col px-2 pt-4">
        <div className="flex flex-row gap-5">
          <Button
            id={getDataTestId("save", ID)}
            data-testid={getDataTestId("save", ID)}
            variant="primary"
            onClick={handleSave}
            disabled={!isFormChange}
          >
            {t("SAVE")}
          </Button>

          <ZButton
            text={t("RESET")}
            id={getDataTestId("reset", ID)}
            type="tertiary"
            iconName="fa-solid fa-rotate-left"
            iconPosition="left"
            onButtonClick={handleReset}
            customClass={classNames({
              "!text-semantic-content-interactive-primary-disabled !pointer-events-none":
                !isFormChange,
            })}
          />
        </div>
      </div>
    </CardWrapper>
  );
};

export default ApplianceNetwork;
