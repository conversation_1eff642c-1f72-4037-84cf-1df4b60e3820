import { type ApplianceDetailFromServer } from "../../ApplianceDetailTransformer";
import { type ApplianceTroubleshootingProps } from "../../types";

export const APPLIANCE_TROUBLESHOOTING_DATA: ApplianceTroubleshootingProps = {
  pageTitle: "locations.troubleshooting.title",
  pageDesc: "locations.troubleshooting.description",
  triggerUpdateAppliance: (payload: ApplianceDetailFromServer, type: string) =>
    console.log("triggerAPI", payload, type),
  onReset: () => console.log("Reset Button Clicked"),
  readOnly: false,
  locationSwitchList: [
    {
      id: "enableTroubleShoot",
      isEnabled: true,
      desc: "locations.troubleshooting.enableSupport",
      handleToggle: () => console.log("Location Toggle"),
    },
    {
      id: "enableZcalerSupport",
      isEnabled: false,
      desc: "locations.troubleshooting.allowSupport",
      handleToggle: () => console.log("Location Toggle"),
    },
  ],
  tunnelInfoTitle: "locations.troubleshooting.tunnelInfo",
  tunnelInfo: [
    {
      label: "IP_ADDRESS",
      value: "--",
    },
    {
      label: "locations.troubleshooting.port",
      value: "--",
    },
  ],
};
