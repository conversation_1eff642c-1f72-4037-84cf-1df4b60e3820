import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import classNames from "classnames";
import { ZButton } from "@xc/legacy-components";
import { Button } from "@zs-nimbus/core";
import { t } from "i18next";
import {
  type TroubleShootFormProps,
  type ApplianceTroubleshootingProps,
  type TunnelInfoProps,
} from "../../types";
import LocationToggleSwitch from "../../../Locations/LocationToggleSwitch/LocationToggleSwitch";
import CardWrapper from "../../../Components/CardWrapper/CardWrapper";
import { getDataTestId } from "@/utils/utils";

const TunnelInfo = ({ tunnelInfoTitle, tunnelInfo }: TunnelInfoProps) => (
  <div className="flex flex-col">
    <div
      className="typography-paragraph1-strong text-semantic-content-base-primary"
      aria-label={t(tunnelInfoTitle)}
    >
      {t(tunnelInfoTitle)}
    </div>
    {tunnelInfo.map(({ label, value }) => (
      <div className="flex" key={label}>
        <div className="typography-paragraph1 text-semantic-content-base-tertiary w-[10rem] break-all">
          {t(label)}
        </div>
        <div className="typography-paragraph1 text-semantic-content-base-primary w-[24rem] break-all">
          {value || "--"}
        </div>
      </div>
    ))}
  </div>
);
const ApplianceTroubleshooting = ({
  locationSwitchList,
  pageTitle,
  pageDesc,
  tunnelInfoTitle,
  tunnelInfo,
  onReset,
  apiResponse,
  triggerUpdateAppliance,
  readOnly,
}: ApplianceTroubleshootingProps) => {
  const { applianceSupportTunnel } = apiResponse ?? {};
  const tunnelIp = applianceSupportTunnel?.tunnelIp ?? "";
  const tunnelPort = applianceSupportTunnel?.tunnelPort ?? "";
  const updateTunnelInfo = tunnelInfo.map((item) => {
    if (item.label === "IP_ADDRESS") {
      return { ...item, value: tunnelIp ?? "--" };
    } else {
      return { ...item, value: tunnelPort ?? "--" };
    }
  });

  const tunnelInfoArr = tunnelIp || tunnelPort ? updateTunnelInfo : [];

  const ID = "appliance-troubleshooting";
  const { t } = useTranslation();
  const initialValues = {
    enableTroubleShoot: locationSwitchList[0].isEnabled,
    enableZcalerSupport: locationSwitchList[1].isEnabled,
  };
  const [troubleShoot, setTroubleShoot] = useState(initialValues);
  const [isFormChange, setIsFormChange] = useState<boolean>(false);
  const showTunnelinfo =
    troubleShoot.enableTroubleShoot && tunnelInfoArr.length > 0;
  const handleReset = () => {
    setTroubleShoot(initialValues);
    onReset(initialValues);
  };

  const onChange = (value: boolean, id: string) => {
    setTroubleShoot((prev) => ({
      enableTroubleShoot:
        id === "enableTroubleShoot" ? value : !value && prev.enableTroubleShoot,
      enableZcalerSupport:
        id === "enableZcalerSupport"
          ? value
          : !value && prev.enableZcalerSupport,
    }));
  };

  useEffect(() => {
    setIsFormChange(
      JSON.stringify(troubleShoot) !== JSON.stringify(initialValues),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [troubleShoot]);

  const onSave = () => {
    const payload = {
      ...apiResponse!,
      applianceSupportTunnel: {
        ...apiResponse!.applianceSupportTunnel,
        enableSupportTunnel: troubleShoot.enableTroubleShoot,
        allowSupportTunnelAction: troubleShoot.enableZcalerSupport,
      },
    };
    triggerUpdateAppliance?.(payload, "support-tunnel");
  };

  return (
    <CardWrapper data-testid={ID} className="flex flex-wrap flex-col p-4 gap-6">
      <div className="flex flex-col gap-1">
        <div className="typography-header5 text-semantic-content-base-primary">
          {t(pageTitle)}
        </div>
        <div className="typography-paragraph2 text-semantic-content-base-primary">
          {t(pageDesc ?? "")}
        </div>
      </div>
      <div className="px-2 flex-col flex">
        <LocationToggleSwitch
          locationSwitch={locationSwitchList.map((item) => ({
            ...item,
            isEnabled: troubleShoot[item.id as keyof TroubleShootFormProps],
            handleToggle: onChange,
          }))}
          disabled={readOnly}
        />
      </div>
      {showTunnelinfo && (
        <div className="px-2 flex-col flex">
          <TunnelInfo
            tunnelInfo={tunnelInfoArr}
            tunnelInfoTitle={tunnelInfoTitle}
          />
        </div>
      )}
      <div className="px-2 inline-flex items-start gap-rem-120 mt-rem-160">
        <Button
          variant="primary"
          onClick={onSave}
          id={getDataTestId("save", ID)}
          data-testid={getDataTestId("save", ID)}
          disabled={!isFormChange}
        >
          {t("SAVE")}
        </Button>
        <ZButton
          id={getDataTestId("reset", ID)}
          text={t("RESET")}
          type="tertiary"
          iconName="fa-solid fa-rotate-left"
          iconPosition="left"
          onButtonClick={handleReset}
          customClass={classNames({
            "!text-semantic-content-interactive-primary-disabled !pointer-events-none":
              !isFormChange,
          })}
        />
      </div>
    </CardWrapper>
  );
};

export default ApplianceTroubleshooting;
