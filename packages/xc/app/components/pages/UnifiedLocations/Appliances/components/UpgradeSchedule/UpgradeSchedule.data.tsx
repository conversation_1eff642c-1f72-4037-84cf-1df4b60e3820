import {
  type UpgradeScheduleFormProps,
  type UpgradeScheduleProp,
} from "../../types";
import { type SelectOption } from "./Select";

const SCHEDULE_DATA = {
  day: {
    onChange: (value: SelectOption) => console.log(value),
    options: [],
    value: "Sunday",
  },
  hour: {
    onChange: (value: SelectOption) => console.log(value),
    options: [
      { id: "00", label: "00" },
      { id: "01", label: "01" },
      { id: "02", label: "02" },
      { id: "03", label: "03" },
      { id: "04", label: "04" },
      { id: "05", label: "05" },
      { id: "06", label: "06" },
      { id: "07", label: "07" },
      { id: "08", label: "08" },
      { id: "09", label: "09" },
      { id: "10", label: "10" },
      { id: "11", label: "11" },
      { id: "12", label: "12" },
    ],
    value: "12",
  },
  minute: {
    onChange: (value: SelectOption) => console.log(value),
    options: [
      { id: "00", label: "00" },
      { id: "01", label: "01" },
      { id: "02", label: "02" },
      { id: "03", label: "03" },
      { id: "04", label: "04" },
      { id: "05", label: "05" },
      { id: "06", label: "06" },
      { id: "07", label: "07" },
      { id: "08", label: "08" },
      { id: "09", label: "09" },
      { id: "10", label: "10" },
      { id: "11", label: "11" },
      { id: "12", label: "12" },
      { id: "13", label: "13" },
      { id: "14", label: "14" },
      { id: "15", label: "15" },
      { id: "16", label: "16" },
      { id: "17", label: "17" },
      { id: "18", label: "18" },
      { id: "19", label: "19" },
      { id: "20", label: "20" },
      { id: "21", label: "21" },
      { id: "22", label: "22" },
      { id: "23", label: "23" },
      { id: "24", label: "24" },
      { id: "25", label: "25" },
      { id: "26", label: "26" },
      { id: "27", label: "27" },
      { id: "28", label: "28" },
      { id: "29", label: "29" },
      { id: "30", label: "30" },
      { id: "31", label: "31" },
      { id: "32", label: "32" },
      { id: "33", label: "33" },
      { id: "34", label: "34" },
      { id: "35", label: "35" },
      { id: "36", label: "36" },
      { id: "37", label: "37" },
      { id: "38", label: "38" },
      { id: "39", label: "39" },
      { id: "40", label: "40" },
      { id: "41", label: "41" },
      { id: "42", label: "42" },
      { id: "43", label: "43" },
      { id: "44", label: "44" },
      { id: "45", label: "45" },
      { id: "46", label: "46" },
      { id: "47", label: "47" },
      { id: "48", label: "48" },
      { id: "49", label: "49" },
      { id: "50", label: "50" },
      { id: "51", label: "51" },
      { id: "52", label: "52" },
      { id: "53", label: "53" },
      { id: "54", label: "54" },
      { id: "55", label: "55" },
      { id: "56", label: "56" },
      { id: "57", label: "57" },
      { id: "58", label: "58" },
      { id: "59", label: "59" },
    ],
    value: "00",
  },
  meridiem: {
    onChange: (value: SelectOption) => console.log(value),
    options: [
      { id: "AM", label: "AM" },
      { id: "PM", label: "PM" },
    ],
    value: "AM",
  },
};

export const UPGRADE_SCHEDULE_DATA: UpgradeScheduleProp = {
  pageTitle: "appliances.upgradeSchedule.title",
  initialScheduledData: {
    day: "Sunday",
    hour: "12",
    minute: "00",
    meridiem: "AM",
  },
  schedule: SCHEDULE_DATA,
  versionData: {
    currentVersion: {
      title: "appliances.upgradeSchedule.currentVersionTitle",
      data: [
        {
          label: "appliances.upgradeSchedule.version",
          value: "0.245.0+bafbb08bc_6.2.2546e4cb7",
        },
        {
          label: "appliances.upgradeSchedule.lastUpgraded",
          value: "May 02, 2024 09:25 AM America/Chicago",
        },
      ],
    },
    nextVersion: {
      title: "appliances.upgradeSchedule.nextVersionTitle",
      data: [
        {
          label: "appliances.upgradeSchedule.version",
          value: "0.245.0+bafbb08bc_6.2.2546e4cb7",
        },
        {
          label: "appliances.upgradeSchedule.lastUpgraded",
          value: "May 02, 2024 09:25 AM America/Chicago",
        },
      ],
      upgradeDay: 1,
    },
  },
  onSave: (values: UpgradeScheduleFormProps) =>
    console.log("on save button click values", values),
  onReset: (values: UpgradeScheduleFormProps) =>
    console.log("on reset button click values", values),
  readOnly: false,
};
