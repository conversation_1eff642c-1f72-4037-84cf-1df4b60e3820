import {
  type ApplianceNetworkFormProps,
  type ApplianceNetworkProp,
} from "../../types";

export const APPLIANCE_NETWORK_DATA: ApplianceNetworkProp = {
  heading: "appliances.menu.network",
  readOnly: false,
  applianceNetworkData: {
    dnsCache: {
      heading: "locations.appliance.dnsCache.heading",
      selectedValue: "Enabled",
      options: [
        { label: "locations.appliance.dnsCache.label1", value: "Enabled" },
        {
          label: "locations.appliance.dnsCache.label2",
          value: "Disabled",
        },
      ],
    },
    tunnelMode: {
      heading: "locations.appliance.tunnelMode.heading",
      selectedValue: "Unencrypted",
      options: [
        { label: "locations.appliance.tunnelMode.label1", value: "Encrypted" },
        {
          label: "locations.appliance.tunnelMode.label2",
          value: "Unencrypted",
        },
      ],
    },
    distributionMode: {
      heading: "locations.appliance.distributionMode.heading",
      selectedValue: "Balanced",
      options: [
        {
          label: "locations.appliance.distributionMode.label1",
          value: "Balanced",
        },
        {
          label: "locations.appliance.distributionMode.label2",
          value: "Best-link",
        },
      ],
    },
    onSave: (values: ApplianceNetworkFormProps) =>
      console.log("on save button click values", values),

    onReset: (values: ApplianceNetworkFormProps) =>
      console.log("on reset button click values", values),
    isVirtualDevice: false,
  },
};
