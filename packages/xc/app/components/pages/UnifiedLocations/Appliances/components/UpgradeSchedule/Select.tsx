import { useTranslation } from "react-i18next";
import { Zselect } from "@xc/legacy-components";

export type SelectOption = {
  id: string;
  label: string;
};

export type SelectProps = {
  options: SelectOption[];
  onChange: (value: SelectOption) => void;
  label?: string;
  value: string;
  customWidth?: number;
  id?: string | number;
  disabled?: boolean;
};

export default function Select({
  options,
  onChange,
  label = "Select",
  value,
  id,
  customWidth = 64,
  disabled = false,
}: SelectProps) {
  const { t } = useTranslation();

  return (
    <div className={`w-[${customWidth}px]`}>
      <div>
        <Zselect
          showSelectedOptions={false}
          placeholder={t(label)}
          idAttr={"id"}
          valueAttr={"label"}
          id={id ?? "select"}
          multiSelect={false}
          enableSelectAll={false}
          flip={false}
          enableCancel={false}
          options={options}
          disabled={disabled}
          enableClearSelection={false}
          showClearSelection={false}
          isPill={false}
          searchOptions={{ enable: false }}
          onSelectionChange={(item: SelectOption[]) => onChange(item[0])}
          preSelectedOptions={options.filter((item) => item.id === value)}
        />
      </div>
    </div>
  );
}
