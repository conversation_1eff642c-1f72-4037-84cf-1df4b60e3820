import { faPen } from "@fortawesome/pro-regular-svg-icons";
import { type AppConnectorProps } from "./AppConnector";

export const APP_CONNECTOR_INTERFACE: Array<{
  label: string;
  value: string;
}> = [
  {
    label: "locations.appliance.interface.dhcp",
    value: "Enabled",
  },
  {
    label: "appliances.drawer.shared.ipAddress",
    value: "Automatic (***********)",
  },
  {
    label: "appliances.drawer.shared.gateway",
    value: "Automatic (*************)",
  },
  {
    label: "appliances.drawer.shared.DNS",
    value: "*******,*******",
  },
];

export const APP_CONNECTOR_DATA: AppConnectorProps = {
  connectors: {
    id: "network",
    handleEditLocation: (id: string | number) => console.log(id),
    heading: "App Connector Properties",
    icon: faPen,
    connectorList: [
      {
        label: "appliances.app-connector.group",
        value: "\u2014",
      },
      {
        label: "appliances.app-connector.deployment-status",
        value: "\u2014",
      },
      {
        label: "appliances.app-connector.provisioning-key",
        value: "\u2014",
      },
    ],
  },
  appConnectorInterface: {
    id: "appConnectorInterface",
    handleEditLocation: (id: string | number) => console.log(id),
    heading: "App Connector Inteface",
    icon: faPen,
    connectorList: APP_CONNECTOR_INTERFACE,
  },
  isPhysicalDevice: false,
  handleEditLocation: (id: string | number) => console.log(id),
  readOnly: false,
};
