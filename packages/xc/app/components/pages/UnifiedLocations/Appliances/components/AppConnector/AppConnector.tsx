/* eslint-disable @up/unified-platform/max-file-lines */
import { useTranslation } from "react-i18next";
import { type ReactElement, useEffect, useState } from "react";
import { Button } from "@zs-nimbus/core";
import { type IconProp } from "@fortawesome/fontawesome-svg-core";
import { ZToggleSwitch, Alert } from "@xc/legacy-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import useSWR from "swr";
import useSWRMutation from "swr/mutation";
import { APP_CONNECTOR_DRAWER_DATA } from "../../Drawers/AppConnectorDrawer/AppConnectorDrawer.data";
import AppConnectorDrawer from "../../Drawers/AppConnectorDrawer/AppConnectorDrawer";
import CardWrapper from "../../../Components/CardWrapper/CardWrapper";
import {
  type IdLabelProps,
  type AppConnectorGroupResponse,
  type AppConnectorGroupProvKeyResponse,
  type AppConnectorGroupProvKeyProps,
  type AppConnectorFormDataProps,
} from "../../types";
import { UnifiedLocationQueries } from "../../../Locations/query";
import { type ApplianceDetailFromServer } from "../../ApplianceDetailTransformer";
import { extractErrorDetails } from "../../../helper";
import { getDataTestId } from "@/utils/utils";
import { API_ENDPOINTS, postReq, putReq } from "@/utils/apiHelper";
import {
  type ErrorType,
  removeAlertMessage,
  showErrorAlert,
  showSuccessAlert,
} from "@/app/onboarding/apiHelper";
import { Overlay } from "@/components/OnboardingLayout/Overlay";

export type ConnectorListItemsProps = {
  label: string;
  value: string;
  meta?: string;
  id?: string;
};

export type ConnectorListProps = {
  id: number | string;
  heading: string;
  icon: IconProp;
  connectorList: ConnectorListItemsProps[];
  appConnectorInterface?: ConnectorListItemsProps[];
  apiResponse?: ApplianceDetailFromServer;
  isPhysicalDevice?: boolean;
  handleEditLocation: (id: string | number) => void;
  readOnly?: boolean;
};

export type AppConnectorProps = {
  connectors: ConnectorListProps;
  appConnectorInterface?: ConnectorListProps;
  apiResponse?: ApplianceDetailFromServer;
  isPhysicalDevice?: boolean;
  handleEditLocation: (id: string | number) => void;
  readOnly: boolean;
};

const ConnectorListItem = ({
  label,
  value,
  meta,
  id,
}: ConnectorListItemsProps) => {
  const { t } = useTranslation();

  return (
    <div className="flex w-[360px] mb-rem-100" data-testid={id}>
      <div className="typography-paragraph2 text-semantic-content-base-tertiary break-words">
        {t(label)}
        <div className="flex flex-col w-[340px] typography-paragraph2 text-semantic-content-base-primary">
          <span>{value ? t(value) : "--"}</span>
          <span className={`text-semantic-content-base-tertiary break-words`}>
            {meta}
          </span>
        </div>
      </div>
    </div>
  );
};

const AppConnectorInterfaceListItem = ({
  label,
  value,
  meta,
  id,
}: ConnectorListItemsProps) => {
  const { t } = useTranslation();

  return (
    <div
      className="flex w-[360px] mb-rem-100"
      data-testid={getDataTestId(id, "appliance-details-item")}
    >
      <div className="typography-paragraph2 text-semantic-content-base-tertiary break-words">
        {t(label)}
        <div className="flex flex-col w-[340px] typography-paragraph2 text-semantic-content-base-primary">
          <span>{value ? t(value) : "--"}</span>
          <span className={`text-semantic-content-base-tertiary break-words`}>
            {meta}
          </span>
        </div>
      </div>
    </div>
  );
};

const ConnectorList = ({
  id,
  heading,
  icon,
  connectorList,
  handleEditLocation,
  apiResponse,
  appConnectorInterface,
  isPhysicalDevice,
  readOnly,
}: ConnectorListProps) => {
  const { t } = useTranslation();
  const ID = "app-connector-network";
  const [openDrawer, setOpenDrawer] = useState(false);
  const handleEditAppConnectorDrawer = () => {
    setOpenDrawer(true);
    handleEditLocation(id);
  };

  const canEditAppliance =
    apiResponse!.applianceDetails.serialNumber !== null && apiResponse?.id;
  // apiResponse?.applianceDetails?.deploymentStatus === "DEPLOYED" &&

  const isEnableConnector = apiResponse?.appConnector?.status === "ENABLE";
  const [enableConnector, setEnableConnectorr] = useState(isEnableConnector);
  const [appConnectorGroupList, setAppConnectorGroupList] = useState<
    IdLabelProps[]
  >([]);
  const [appConnectorProvKeyList, setappConnectorProvKeyList] = useState<
    AppConnectorGroupProvKeyProps[]
  >([]);
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const externalId =
    apiResponse?.appConnector?.appConnectorGroup?.externalId ?? "";
  const applianceID = apiResponse?.applianceId;
  const updateDrawerData = {
    ...APP_CONNECTOR_DRAWER_DATA,
    appConnectorGroup: {
      ...APP_CONNECTOR_DRAWER_DATA.appConnectorGroup,
      options: appConnectorGroupList,
    },
    provisionKey: {
      ...APP_CONNECTOR_DRAWER_DATA.provisionKey,
      options: appConnectorProvKeyList,
    },
  };

  const drawerData = {
    provisionKey: [
      {
        id: apiResponse?.appConnector?.groupName?.id ?? "",
        label: apiResponse?.appConnector?.groupName?.name ?? "",
        nonce: apiResponse?.appConnector?.groupName?.nonce ?? "",
      },
    ],
    appConnectorGroup: [
      {
        id: apiResponse?.appConnector?.appConnectorGroup?.externalId ?? "",
        label: apiResponse?.appConnector?.appConnectorGroup?.name ?? "",
      },
    ],
    deploymentStatus: [{ id: "ENABLE", label: "Active - Active" }],
    ipAddress: apiResponse?.appConnector?.ipAddress ?? "",
    defaultGateway: apiResponse?.appConnector?.defaultGatewayIp ?? "",
    primaryDNS: apiResponse?.appConnector?.primaryDnsServerIp ?? "",
    secondaryDNS: apiResponse?.appConnector?.secondaryDnsServerIp ?? "",
  };

  const [initFormData, setInitFormData] =
    useState<AppConnectorFormDataProps>(drawerData);
  useSWR(
    [
      `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
      "all_app_connector_group",
    ],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_APP_CONNECTOR_GROUP,
      }),
    {
      onSuccess: (data) => {
        const appConnectorData = data as { data: AppConnectorGroupResponse };
        const transformAppConnectorData =
          appConnectorData?.data?.allAppConnectorGroups.map((item) => ({
            id: item.externalId.toString(),
            label: item.name,
          })) satisfies IdLabelProps[];
        setAppConnectorGroupList([
          ...appConnectorGroupList,
          ...transformAppConnectorData,
        ]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );
  const errorHandler = {
    onError: (error: Error) => {
      //temporary to get pages to render if mock server not running
      const errorCause = error.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }
      showErrorAlert(errorCause, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };

  useEffect(() => {
    const GET_ALL_APP_CONNECTOR_GROUP_PROV_KEY = {
      query: `query ($identifier: ID!) { appConnectorGroupProvKey(groupId: $identifier) { id name nonce } }`,
      variables: {
        identifier: externalId,
      },
    };
    void postPref(GET_ALL_APP_CONNECTOR_GROUP_PROV_KEY);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { trigger: postPref } = useSWRMutation(
    `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
    postReq,
    {
      onSuccess: (data) => {
        const appConnectorProvKeyData = data as {
          data: AppConnectorGroupProvKeyResponse;
        };
        const transformAppConnectorProvKeyData =
          appConnectorProvKeyData.data.appConnectorGroupProvKey.map((item) => ({
            id: item.id.toString(),
            label: item.name,
            nonce: item.nonce,
          }));

        setappConnectorProvKeyList(transformAppConnectorProvKeyData);
      },
      ...errorHandler,
    },
  );

  const updateAppConnector = useSWRMutation(
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${applianceID}/app-connector`,
    putReq,
    {
      onSuccess: () => {
        const appConnectorUpdateMsg = t(
          "appliances.app-connector.toast-message.update-appConnector",
        );
        showSuccessAlert(appConnectorUpdateMsg, setAlertType, setAlertMessage);
        removeAlertMessage(setAlertType, setAlertMessage);
      },
      ...errorHandler,
    },
  );
  function onSaveHandler(formData: AppConnectorFormDataProps): void {
    setInitFormData(formData);
    const payload = {
      id: apiResponse?.id,
      provId: apiResponse?.provId,
      appConnector: {
        ...apiResponse!.appConnector,
        groupName: {
          ...apiResponse!.appConnector?.groupName,
          name: formData.provisionKey.map((item) => item.label).toString(),
          nonce: formData.provisionKey.map((item) => item.nonce).toString(),
        },
        status: "ENABLE",
        provisioningKeyName: formData.provisionKey
          .map((item) => item.label)
          .toString(),
        provisioningKey: formData.provisionKey
          .map((item) => item.label)
          .toString(),
        appConnectorGroup: {
          name: formData.appConnectorGroup.map((item) => item.label).toString(),
          externalId: formData.appConnectorGroup
            .map((item) => item.id)
            .toString(),
        },
        ipAddress: formData.ipAddress || apiResponse?.appConnector.ipAddress,
        defaultGatewayIp:
          formData.defaultGateway ||
          apiResponse?.appConnector?.defaultGatewayIp,
        primaryDnsServerIp:
          formData.primaryDNS || apiResponse?.appConnector?.primaryDnsServerIp,
        secondaryDNS:
          formData.secondaryDNS ||
          apiResponse?.appConnector?.secondaryDnsServerIp,
      },
    };
    void updateAppConnector.trigger(payload);
  }
  const updateIdentifierData = (identifier: string | number, name: string) => {
    const GET_ALL_APP_CONNECTOR_GROUP_PROV_KEY = {
      query: `query ($identifier: ID!) { appConnectorGroupProvKey(groupId: $identifier) { id name nonce } }`,
      variables: {
        identifier: identifier,
      },
    };
    void postPref(GET_ALL_APP_CONNECTOR_GROUP_PROV_KEY);

    const drawerData = {
      provisionKey: [
        {
          id: apiResponse?.appConnector?.groupName?.id ?? "",
          label: apiResponse?.appConnector?.groupName?.name ?? "",
          nonce: apiResponse?.appConnector?.groupName?.nonce ?? "",
        },
      ],
      appConnectorGroup: [
        {
          id: identifier ?? "",
          label: name ?? "",
        },
      ],
      deploymentStatus: [{ id: "ENABLE", label: "Active - Active" }],
      ipAddress: apiResponse?.appConnector?.ipAddress ?? "",
      defaultGateway: apiResponse?.appConnector?.defaultGatewayIp ?? "",
      primaryDNS: apiResponse?.appConnector?.primaryDnsServerIp ?? "",
      secondaryDNS: apiResponse?.appConnector?.secondaryDnsServerIp ?? "",
    };
    setInitFormData(drawerData);
  };

  const onDisableHandler = (checked: boolean) => {
    if (!checked) {
      const payload = {
        ...apiResponse!,
        appConnector: {
          ...apiResponse!.appConnector,
          status: "DISABLE",
        },
      };
      void updateAppConnector.trigger(payload);
    }
  };

  return (
    <div className="inline-flex gap-rem-160" data-testid={ID}>
      <CardWrapper className="p-4  max-w-[380px]">
        <div className="flex flex-col w-[360px] gap-rem-120">
          <div
            className="flex gap-rem-80 items-center"
            data-testid={getDataTestId(`layout`, "appliance-network")}
          >
            <ZToggleSwitch
              id={id}
              checked={enableConnector}
              xs
              type="secondary"
              disabled={!canEditAppliance || readOnly}
              onChange={(e: boolean) => {
                onDisableHandler(e);
                setEnableConnectorr(e);
              }}
            />
            <span
              className="typography-header5 mr-rem-380 text-semantic-content-base-primary"
              aria-label={`appliance-detail-heading`}
              data-testid={getDataTestId(`${id}`, "appliance-heading")}
            >
              {t(heading)}
            </span>
            {enableConnector && (
              <div className="pl-px-640">
                {canEditAppliance && (
                  <Button
                    variant="tertiary"
                    id={"edit"}
                    onClick={handleEditAppConnectorDrawer}
                    prefixIcon={<FontAwesomeIcon icon={icon} />}
                    disabled={!canEditAppliance}
                  />
                )}
              </div>
            )}
          </div>
          {enableConnector && (
            <div>
              {connectorList?.map((item, index) => (
                <ConnectorListItem
                  key={item.value}
                  {...item}
                  id={`${id}-${index}`}
                />
              ))}
            </div>
          )}
          <AppConnectorDrawer
            {...updateDrawerData}
            initFormData={initFormData}
            onSave={onSaveHandler}
            openDrawer={openDrawer}
            setOpenDrawer={setOpenDrawer}
            isPhysicalDevice={isPhysicalDevice}
            updateIdentifier={updateIdentifierData}
          />

          {updateAppConnector.isMutating && (
            <div className="[&_div]:z-[999]">
              <Overlay id="unified-locations" />
            </div>
          )}
          {alertMessage && (
            <Alert
              alert={{
                message: alertMessage,
                type: alertType,
              }}
            />
          )}
        </div>
      </CardWrapper>
      {enableConnector && !isPhysicalDevice && (
        <CardWrapper className="p-4  max-w-[380px] h-[272px]">
          <div className="flex flex-col w-[360px] gap-rem-120">
            <div
              className="flex  items-center gap-rem-240"
              data-testid={getDataTestId(`layout`, "appliance-network")}
            >
              <span
                className="typography-header5 mr-rem-380 text-semantic-content-base-primary"
                aria-label={`appliance-detail-heading`}
                data-testid={getDataTestId(`${id}`, "appliance-heading")}
              >
                {t("appliances.app-connector.connector-interface.heading")}
              </span>

              <div className="pl-rem-960">
                {canEditAppliance && (
                  <Button
                    variant="tertiary"
                    id={"edit"}
                    onClick={handleEditAppConnectorDrawer}
                    prefixIcon={<FontAwesomeIcon icon={icon} />}
                    disabled={!canEditAppliance}
                  />
                )}
              </div>
            </div>

            <div>
              {appConnectorInterface?.map((item, index) => (
                <AppConnectorInterfaceListItem
                  key={item.value}
                  {...item}
                  id={`${id}-${index}`}
                />
              ))}
            </div>
          </div>
        </CardWrapper>
      )}
    </div>
  );
};
const AppConnector = ({
  connectors,
  handleEditLocation,
  apiResponse,
  isPhysicalDevice,
  readOnly,
}: AppConnectorProps) => (
  <div className=" flex flex-col gap-rem-160 pl-rem-80">
    <ConnectorList
      key={connectors.id}
      {...connectors}
      handleEditLocation={handleEditLocation}
      apiResponse={apiResponse}
      isPhysicalDevice={isPhysicalDevice}
      readOnly={readOnly}
    />
  </div>
);

export default AppConnector;
