import { useEffect, useState } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { ZButton } from "@xc/legacy-components";
import { Button } from "@zs-nimbus/core";
import {
  type UpgradeScheduleProp,
  type UpgradeScheduleFormProps,
  type Version,
} from "../../types";
import { convertAMPMtoMinutes } from "../../../utils/utils";
import { listWeek } from "../../../constants";
import CardWrapper from "../../../Components/CardWrapper/CardWrapper";
import { UPGRADE_SCHEDULE_DATA } from "./UpgradeSchedule.data";
import Select from "./Select";
import { getDataTestId } from "@/utils/utils";

export type ScheduleType = {
  day: string;
  hour: string;
  minute: string;
  meridiem: string;
};

export function Versions({ label, value, timeZone }: Version) {
  const { t } = useTranslation();

  return (
    <div className="flex gap-rem-240 mb-rem-80">
      <div className="typography-paragraph2 text-semantic-content-base-tertiary w-rem-1280 break-words">
        {t(label)}
      </div>
      <div className="inline-flex">
        <div className="typography-paragraph2 text-semantic-content-base-primary pl-rem-240">
          {value || "--"}
        </div>

        {value && timeZone && (
          <div className="typography-paragraph2 text-semantic-content-base-tertiary pl-rem-40">
            &#x2022; {timeZone}
          </div>
        )}
      </div>
    </div>
  );
}

const UpgradeSchedule = ({
  pageTitle,
  initialScheduledData,
  versionData,
  onSave,
  onReset,
  triggerUpdateAppliance,
  apiResponse,
  readOnly,
}: UpgradeScheduleProp) => {
  const { t } = useTranslation();
  const ID = "upgrade-schedule";
  UPGRADE_SCHEDULE_DATA.schedule.day.options = listWeek();

  const initialValues: UpgradeScheduleFormProps = {
    day: initialScheduledData.day,
    hour: initialScheduledData.hour,
    minute: initialScheduledData.minute,
    meridiem: initialScheduledData.meridiem,
  };
  const [scheduleState, setScheduleState] = useState(initialValues);
  const [isFormChange, setIsFormChange] = useState<boolean>(false);

  useEffect(() => {
    setIsFormChange(
      JSON.stringify(scheduleState) !== JSON.stringify(initialValues),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scheduleState]);

  const handleSave = () => {
    onSave(scheduleState);

    const minutes =
      convertAMPMtoMinutes(
        Number(scheduleState.hour),
        Number(scheduleState.minute),
        scheduleState.meridiem,
      ) * 60;

    const dayIndex = listWeek().find((x) => x.label === scheduleState.day)?.val;

    const payload = {
      ...apiResponse!,
      applianceUpgrade: {
        ...apiResponse!.applianceUpgrade,
        next: {
          ...apiResponse!.applianceUpgrade.next,
          upgradeDay: dayIndex!,
          upgradeTime: minutes,
        },
      },
    };
    triggerUpdateAppliance?.(payload, "upgrade-schedule");
  };

  const handleReset = () => {
    setScheduleState(initialValues);
    onReset(initialValues);
  };

  const onChange = (type: keyof ScheduleType, value: string) => {
    setScheduleState({ ...scheduleState, [type]: value });
  };

  return (
    <CardWrapper className="p-4  max-w-[520px]">
      <div className="flex flex-col gap-rem-120" data-testid={ID}>
        <div
          className="typography-header5 text-semantic-content-base-primary"
          data-testid={getDataTestId("title", ID)}
        >
          {t(pageTitle)}
        </div>
        <div className="flex gap-rem-120 items-center mb-rem-160">
          <Select
            {...UPGRADE_SCHEDULE_DATA?.schedule?.day}
            onChange={(value) => onChange("day", value.id)}
            value={scheduleState.day}
            customWidth={200}
            id={"week-names"}
            disabled={readOnly}
          />
          <span className="text-semantic-content-base-secondary text-paragraph1">
            {t("AT")}
          </span>
          <Select
            {...UPGRADE_SCHEDULE_DATA?.schedule?.hour}
            onChange={(value) => onChange("hour", value.id)}
            value={scheduleState.hour}
            id={"hours"}
            disabled={readOnly}
          />
          <Select
            {...UPGRADE_SCHEDULE_DATA?.schedule?.minute}
            onChange={(value) => onChange("minute", value.id)}
            value={scheduleState.minute}
            id={"minutes"}
            disabled={readOnly}
          />
          <Select
            {...UPGRADE_SCHEDULE_DATA?.schedule?.meridiem}
            onChange={(value) => onChange("meridiem", value.id)}
            value={scheduleState.meridiem}
            id={"meridiem"}
            disabled={readOnly}
          />
        </div>
        <div className="flex gap-rem-120 flex-col">
          <div>
            <p className="typography-paragraph1 text-semantic-content-base-primary">
              {t(versionData?.currentVersion?.title)}
            </p>
            {versionData?.currentVersion?.data?.map(
              ({ label, value, timeZone }: Version) => (
                <Versions
                  key={`${label}-${value}`}
                  label={label}
                  value={value}
                  timeZone={timeZone}
                />
              ),
            )}
          </div>
          <div>
            <p className="typography-paragraph1 text-semantic-content-base-primary">
              {t(versionData?.nextVersion?.title)}
            </p>
            {versionData?.nextVersion?.data?.map(
              ({ label, value, timeZone }: Version) => (
                <Versions
                  key={`${label}-${value}`}
                  label={label}
                  value={value}
                  timeZone={timeZone}
                />
              ),
            )}
          </div>
        </div>

        <div className="inline-flex items-start gap-rem-120 mt-rem-200">
          <Button
            id={getDataTestId("save", ID)}
            data-testid={getDataTestId("save", ID)}
            variant="primary"
            onClick={handleSave}
            disabled={!isFormChange}
          >
            {t("SAVE")}
          </Button>

          <ZButton
            text={t("RESET")}
            id={getDataTestId("reset", ID)}
            type="tertiary"
            iconName="fa-solid fa-rotate-left"
            iconPosition="left"
            onButtonClick={handleReset}
            customClass={classNames({
              "!text-semantic-content-interactive-primary-disabled !pointer-events-none":
                !isFormChange,
            })}
          />
        </div>
      </div>
    </CardWrapper>
  );
};

export default UpgradeSchedule;
