import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "@zs-nimbus/core";
import { faPen } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import MGTDrawer from "../../Drawers/MGTDrawer/MGTDrawer";
import VMDrawer from "../../Drawers/VMDrawer/VMDrawer";
import { MGT_DRAWER_DATA } from "../../Drawers/MGTDrawer/MGTDrawer.data";
import { VM_DRAWER_DATA } from "../../Drawers/VMDrawer/VMDrawer.data";
import { type ApplianceDetailFromServer } from "../../ApplianceDetailTransformer";
import { type IPAddressInfoData } from "../../types";
import { DISABLED, ENABLED, FORWARDING, MGT } from "../../../constants";
import { getDataTestId } from "@/utils/utils";

export type KeyValuePairProps = {
  label: string;
  value: string;
  translate?: string;
};

export type VMInterfaceProps = {
  forwardInterface: KeyValuePairProps[];
  managementInterface: KeyValuePairProps[];
  forwardInterfaceHeading: string;
  mgtInterfaceHeading: string;
  apiResponse?: ApplianceDetailFromServer;
  triggerUpdateAppliance?: (
    payload: ApplianceDetailFromServer,
    apiType: string,
  ) => void;
  readOnly: boolean;
};

export const KeyValuePair = ({
  label,
  value,
  translate,
}: KeyValuePairProps) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col justify-start items-start">
      <div className="typography-paragraph1 text-semantic-content-base-tertiary break-all">
        {t(label)}
      </div>
      <div className="typography-paragraph1 text-semantic-content-base-primary break-all w-[340px]">
        {translate && value ? `${t(translate)} (${value})` : value || "--"}
      </div>
    </div>
  );
};

const VMInterface = ({
  forwardInterface,
  managementInterface,
  forwardInterfaceHeading,
  mgtInterfaceHeading,
  apiResponse,
  triggerUpdateAppliance,
  readOnly,
}: VMInterfaceProps) => {
  const [openDrawer, setOpenDrawer] = useState(false);
  const [openWANDrawer, setOpenWanDrawer] = useState(false);
  const { t } = useTranslation();
  const ID = "unified-locations-ipsec-gre";
  const onClickHandler = () => {
    setOpenDrawer(true);
  };
  const onClickVMHandler = () => {
    setOpenWanDrawer(true);
  };

  useEffect(() => {
    if (apiResponse) {
      setOpenDrawer(false);
      setOpenWanDrawer(false);
    }
  }, [apiResponse]);

  const managementInterfaceData =
    apiResponse?.applianceVirtualInterfaceList?.find(
      (item) => item.type === MGT,
    );

  const mgtDrawerData = {
    ipAddress: managementInterfaceData?.ipAddress ?? "",
    defaultGateway: managementInterfaceData?.defaultGateway ?? "",
    primaryDNS: managementInterfaceData?.primaryDns ?? "",
    secondaryDNS: managementInterfaceData?.secondaryDns ?? "",
    dhcpEnabled: managementInterfaceData?.dhcpStatus === ENABLED ? true : false,
  };

  const [initMGTFormData, setMGTInitFormData] =
    useState<IPAddressInfoData>(mgtDrawerData);

  const forwardInterfaceData = apiResponse?.applianceVirtualInterfaceList?.find(
    (item) => item.type === FORWARDING,
  );

  const vmDrawerData = {
    ipAddress: forwardInterfaceData?.ipAddress ?? "",
    defaultGateway: forwardInterfaceData?.defaultGateway ?? "",
    primaryDNS: forwardInterfaceData?.primaryDns ?? "",
    secondaryDNS: forwardInterfaceData?.secondaryDns ?? "",
    dhcpEnabled: forwardInterfaceData?.dhcpStatus === ENABLED ? true : false,
    haVirtualIp: forwardInterfaceData?.haVirtualIp ?? "",
    loadBalanceIp: forwardInterfaceData?.loadBalanceIp ?? "",
  };

  const [initVMFormData, setVMInitFormData] =
    useState<IPAddressInfoData>(vmDrawerData);

  const onSaveMGThandler = (formData: IPAddressInfoData) => {
    setMGTInitFormData(formData);
    const updatedInterface = apiResponse!.applianceVirtualInterfaceList?.map(
      (item) =>
        item.type === MGT
          ? {
              ...item,
              dhcpStatus: formData.dhcpEnabled ? ENABLED : DISABLED,
              ...(formData?.ipAddress && { ipAddress: formData.ipAddress }),
              ...(formData?.defaultGateway && {
                defaultGateway: formData.defaultGateway,
              }),
              ...(formData?.primaryDNS && { primaryDns: formData.primaryDNS }),
              ...(formData?.secondaryDNS && {
                secondaryDns: formData.secondaryDNS,
              }),
              ...(formData?.adminStatus && {
                adminStatus: formData.adminStatus,
              }),
            }
          : item,
    );
    const payload = {
      ...apiResponse!,
      applianceVirtualInterfaceList: [...updatedInterface],
    };

    triggerUpdateAppliance?.(payload, "update-interface");
  };

  const onSaveVMhandler = (formData: IPAddressInfoData) => {
    setVMInitFormData(formData);
    const updatedInterface = apiResponse!.applianceVirtualInterfaceList?.map(
      (item) =>
        item.type === FORWARDING
          ? {
              ...item,
              dhcpStatus: formData.dhcpEnabled ? ENABLED : DISABLED,
              ...(formData?.ipAddress && { ipAddress: formData.ipAddress }),
              ...(formData?.defaultGateway && {
                defaultGateway: formData.defaultGateway,
              }),
              ...(formData?.primaryDNS && { primaryDns: formData.primaryDNS }),
              ...(formData?.secondaryDNS && {
                secondaryDns: formData.secondaryDNS,
              }),
              ...(formData?.adminStatus && {
                adminStatus: formData.adminStatus,
              }),
              ...(formData?.haVirtualIp && {
                haVirtualIp: formData.haVirtualIp,
              }),
              ...(formData?.loadBalanceIp && {
                loadBalanceIp: formData.loadBalanceIp,
              }),
            }
          : item,
    );

    const payload = {
      ...apiResponse!,
      applianceVirtualInterfaceList: [...updatedInterface],
    };

    triggerUpdateAppliance?.(payload, "update-interface");
  };
  const canEditAppliance =
    apiResponse?.applianceDetails?.deploymentStatus === "DEPLOYED" &&
    apiResponse.applianceDetails.serialNumber === null;

  return (
    <div
      className="inline-flex justify-start items-start gap-4"
      data-testid={ID}
    >
      <Card.Root>
        <Card.Body className="flex flex-col gap-6">
          <Card.Header className="inline-flex justify-start items-start gap-2">
            <div className="flex-1 flex flex-col">
              <div
                className="typography-header5 text-semantic-content-base-primary"
                aria-label={t(mgtInterfaceHeading)}
              >
                {t(mgtInterfaceHeading)}
              </div>
            </div>
            {!canEditAppliance && (
              <div className="flex justify-start">
                <Button
                  variant="tertiary"
                  disabled={canEditAppliance || readOnly}
                  onClick={onClickHandler}
                  prefixIcon={<FontAwesomeIcon icon={faPen} />}
                  className="!p-1"
                  id={getDataTestId("mgt-edit-btn", ID)}
                  data-testid={getDataTestId("mgt-edit-btn", ID)}
                />
              </div>
            )}
          </Card.Header>
          <div className="flex flex-col justify-start items-start gap-3">
            {managementInterface.map(
              ({ label, value, translate }, itemIndex) => (
                <KeyValuePair
                  label={label}
                  value={value}
                  translate={translate}
                  key={itemIndex}
                />
              ),
            )}
          </div>
        </Card.Body>
      </Card.Root>

      <Card.Root>
        <Card.Body className="flex flex-col gap-6">
          <Card.Header className="inline-flex justify-start items-start gap-2">
            <div className="flex-1 flex flex-col">
              <div
                className="typography-header5 text-semantic-content-base-primary"
                aria-label={t(forwardInterfaceHeading)}
              >
                {t(forwardInterfaceHeading)}
              </div>
            </div>
            {!canEditAppliance && (
              <div className="flex justify-start">
                <Button
                  variant="tertiary"
                  onClick={onClickVMHandler}
                  disabled={canEditAppliance || readOnly}
                  prefixIcon={<FontAwesomeIcon icon={faPen} />}
                  className="!p-1"
                  id={getDataTestId("fi-edit-btn", ID)}
                  data-testid={getDataTestId("fi-edit-btn", ID)}
                />
              </div>
            )}
          </Card.Header>
          <div className="flex flex-col justify-start items-start gap-3">
            {forwardInterface.map(({ label, value, translate }, itemIndex) => (
              <KeyValuePair
                label={label}
                value={value}
                translate={translate}
                key={itemIndex}
              />
            ))}
          </div>
        </Card.Body>
      </Card.Root>
      <MGTDrawer
        {...MGT_DRAWER_DATA}
        heading={mgtInterfaceHeading}
        openDrawer={openDrawer}
        setOpenDrawer={setOpenDrawer}
        onSave={onSaveMGThandler}
        initFormData={initMGTFormData}
      />
      <VMDrawer
        {...VM_DRAWER_DATA}
        heading={forwardInterfaceHeading}
        openDrawer={openWANDrawer}
        setOpenDrawer={setOpenWanDrawer}
        onSave={onSaveVMhandler}
        initFormData={initVMFormData}
      />
    </div>
  );
};

export default VMInterface;
