import type { <PERSON>a, StoryObj } from "@storybook/react";
import { t } from "i18next";
import component from "./NoDataCard";

const meta = {
  title: "pages/UnifiedLocations/components",
  component,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/QVUFwkI7nsoxelC275In8w/AGN%2FBranch-Merged-UI?node-id=1532-44402&t=U5EmZSphdZAhcKcO-4",
    },
  },
} satisfies Meta<typeof component>;

export default meta;

export const NoDataCardData = {
  icon: (
    <i
      aria-label={t("OPERATION_ICON", { label: "No Appliances" })}
      className="text-semantic-content-base-tertiary typography-header3 fa fa-router"
    />
  ),
  name: "No Appliances",
  NoDataCardButton: () => console.log("Button Clicked"),
  description:
    "Secure your location with Edge Connector appliances that forward traffic to Zscaler services.",
  buttonText: "Add Appliances",
  readOnly: false,
};

export const NoDataCard: StoryObj<typeof meta> = {
  args: { ...NoDataCardData },
};
