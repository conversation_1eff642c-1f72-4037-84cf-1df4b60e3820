import { useTranslation } from "react-i18next";
import { Button } from "@zs-nimbus/core";
import { type NoDataCardProps } from "../types";
import { getDataTestId } from "@/utils/utils";

const NoDataCard = ({
  id = "",
  name,
  NoDataCardButton,
  description,
  buttonText,
  readOnly,
}: NoDataCardProps) => {
  const { t } = useTranslation();
  const ID = getDataTestId("no-data-card", id);

  return (
    <div
      className="flex items-center justify-center min-h-full"
      key={id}
      data-testid={ID}
    >
      <div className=" flex flex-col py-rem-160 px-rem-80 items-center w-[400px] ">
        <div className="text-semantic-content-base-primary typography-header4 mb-rem-80">
          {t(name)}
        </div>
        <div
          className="text-semantic-content-base-secondary typography-paragraph1 text-center mb-rem-240"
          data-testid={getDataTestId("description", ID)}
        >
          {t(description)}
        </div>
        {!readOnly && (
          <Button
            variant="primary"
            id={getDataTestId("button", ID)}
            data-testid={getDataTestId("button", ID)}
            onClick={NoDataCardButton}
          >
            {t(buttonText)}
          </Button>
        )}
      </div>
    </div>
  );
};

export default NoDataCard;
