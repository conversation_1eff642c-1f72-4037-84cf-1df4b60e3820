import { useTranslation } from "react-i18next";
import { Button } from "@zs-nimbus/core";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { type LocationHeaderProps } from "./types";
import { getDataTestId } from "@/utils/utils";

const LocationHeader = ({
  pageTitle,
  btnHandleClick,
  btnText,
  hasButton,
  iconName,
  hasDescription,
  pageDesc,
  id,
  readOnly,
}: LocationHeaderProps) => {
  const { t } = useTranslation();

  return (
    <div className="inline-flex justify-start items-start gap-2 gap-rem-240 mb-rem-120">
      <div className="grow flex flex-col justify-start items-start gap-1">
        <div
          className="typography-header5 text-semantic-content-base-primary max-w-[382px] break-all "
          aria-label={t(pageTitle)}
        >
          {t(pageTitle)}
          {hasDescription && (
            <p
              className="typography-paragraph3 text-semantic-content-base-tertiary mb-rem-0 mt-rem-20"
              aria-label={t(pageDesc ?? "")}
            >
              {t(pageDesc ?? "")}
            </p>
          )}
        </div>
      </div>
      <div className="flex justify-start items-center gap-2">
        <div className="w-5 h-5 justify-center items-center flex">
          {hasButton && (
            <Button
              variant="tertiary"
              onClick={btnHandleClick}
              prefixIcon={<FontAwesomeIcon icon={iconName!} />}
              id={getDataTestId(id)}
              data-testid={getDataTestId(id)}
              disabled={readOnly}
            >
              {t(btnText ?? "")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LocationHeader;
