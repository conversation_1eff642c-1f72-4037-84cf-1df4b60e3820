/* eslint-disable @up/unified-platform/max-file-lines */
"use client";

import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { type ReactElement, useContext, useEffect, useState } from "react";
import useS<PERSON> from "swr";
import useSWRMutation from "swr/mutation";
import { Alert } from "@xc/legacy-components";
import { LoadingSpinner } from "@zs-nimbus/core";
import { useTranslation } from "react-i18next";
import { Breadcrumbs, type BreadcrumbsItemsArrayProps } from "@zs-nimbus/core";
import { WorldMap } from "../Appliances/components/WorldMap/WorldMap";
import { mapData } from "../Appliances/components/WorldMap/mapConfig";
import LADetailLayout from "../LADetailLayout/LADetailLayout";
import { extractErrorDetails, hasProductRights } from "../helper";
import { LOCATION_PRODUCTS, ADMIN_ACCESS_TYPE } from "../constants";
import LocationOverview from "./LocationOverview";
import { LOCATION_OVERVIEW_DATA } from "./LocationOverview.data";
import Appliances from "./Appliances";
import { APPLIANCES_DATA } from "./Appliances.data";
import LocationConnectionOptions from "./LocationConnectionOptions";
import { LOCATION_CONNECTION_OPTIONS_DATA } from "./LocationConnectionOptions.data";
import SubLocation from "./SubLocation/SubLocation";
import { SUBLOCATION_DATA } from "./SubLocation/SubLocation.data";
import LocationIPsec from "./LocationIPsec";
import { LOCATION_IPSEC_DATA } from "./LocationIPsec.data";
import responseTransformer, {
  type LocationDetailFromServer,
  type TransformedLocationDetails,
} from "./LocationDetailTransformer";
import { type TabConfig } from "@/components/VerticalTabs/VerticalTabs";
import { useFlags } from "@/context/FeatureFlags";
import { WithStates } from "@/hoc/WithStates";
import { API_ENDPOINTS, getReq, putReq } from "@/utils/apiHelper";
import { Overlay } from "@/components/OnboardingLayout/Overlay";
import {
  type ErrorType,
  removeAlertMessage,
  showErrorAlert,
} from "@/app/onboarding/apiHelper";
import { CcNavUtils } from "@/utils/GlobalNavigationUtils";
import {
  ProductAccessContext,
  useProductAccessProvider,
} from "@/context/ProductAccessProvider";
import { type ProductAccessInfoWithDevFlags } from "@/configs/navigation/types";

export type LocationDetailProps = {
  locationId?: string;
};

const LocationDetail = ({ locationId = undefined }: LocationDetailProps) => {
  const { t } = useTranslation();
  const params = useParams();
  if (!locationId) {
    locationId = params.id as string;
  }

  const queryParams = useSearchParams();
  const router = useRouter();
  const pathName = usePathname();
  const tab = queryParams?.get("tab");
  const productAccessContext = useContext(ProductAccessContext);
  const _applianceTabHidden = CcNavUtils(
    productAccessContext as ProductAccessInfoWithDevFlags,
    ["EDGE_CONNECTOR_CLOUD_PROVISIONING", "EDGE_CONNECTOR_TEMPLATE"],
  );
  const [locationName, setLocationName] = useState("");
  const [locationType, setLocationType] = useState("");
  const [transformed, setTransformed] = useState<TransformedLocationDetails>();
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const [alertType, setAlertType] = useState("");
  const [tabs, setTabs] = useState<TabConfig[]>();
  const [didInit, setDidInit] = useState(false);
  const [sublocationCount, setSublocationCount] = useState<number>();
  const [locationDetails, setLocationDetails] =
    useState<LocationDetailFromServer>();

  const tabIndex = tab
    ? tabs?.findIndex((detailsTab) => detailsTab.value === tab)
    : -1;

  const defaultTab = tabIndex !== -1 && tabIndex ? tabs?.[tabIndex] : tabs?.[0];

  const [selectedTab, setSelectedTabs] = useState<TabConfig | undefined>(
    defaultTab,
  );
  const productAccessInfo = useProductAccessProvider();

  const { featurePermissions } = (productAccessInfo.roles?.ZIA ?? {}) as {
    featurePermissions?: Record<string, string>;
  };

  const readOnly = featurePermissions?.LOCATIONS !== ADMIN_ACCESS_TYPE;

  const { featurePermissions: ztwFeaturePermissions } = (productAccessInfo
    .features?.CLOUD_CONNECTOR ?? {}) as {
    featurePermissions?: Record<string, string>;
  };

  const ztwSuperAdmin =
    ztwFeaturePermissions?.EDGE_CONNECTOR_CLOUD_PROVISIONING ===
      ADMIN_ACCESS_TYPE &&
    ztwFeaturePermissions?.EDGE_CONNECTOR_TEMPLATE === ADMIN_ACCESS_TYPE;

  useEffect(() => {
    setSelectedTabs(defaultTab);
  }, [defaultTab]);
  const { can } = useFlags();

  useEffect(() => {
    if (locationDetails) {
      successHandlerUpdateSubLocationCount(locationDetails);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sublocationCount, locationDetails]);

  const getTabs = (
    data: TransformedLocationDetails,
    locationDetailFromServer: LocationDetailFromServer,
  ): TabConfig[] => [
    {
      title: "locations.menu.overview",
      value: "overview",
      content: () => (
        <LocationOverview
          {...LOCATION_OVERVIEW_DATA}
          locationName={data.locationName}
          locationInfo={data.locationDetails}
          locationDetailedData={data.locationDetailedData}
          onSave={submitHandler}
          locationDetailFromServer={locationDetailFromServer}
          readOnly={readOnly}
        />
      ),
      icon: "fa-solid fa-circle-info",
    },
    {
      title:
        locationDetailFromServer.type === "CLOUD"
          ? "locations.menu.vm-appliances"
          : "locations.menu.appliances",
      value: "appliances",
      content: () => (
        <Appliances
          {...APPLIANCES_DATA}
          onAddOpen={() =>
            void router.push(
              `/ec/administration/branch-provisioning-templates?filter=BC&location-id=${locationId}&location-name=${data?.locationName}&URI="/appliances"`,
            )
          }
          applianceCount={data?.applianceCount}
          locationId={locationDetailFromServer.id}
          readOnly={readOnly}
          ztwSuperAdmin={ztwSuperAdmin}
        />
      ),
      icon: "fa-solid fa-server",
      count: data?.applianceCount,
    },
    {
      title: "locations.menu.ipsec-gre",
      value: "ipsec-gre",
      content: () => (
        <LocationIPsec
          {...LOCATION_IPSEC_DATA}
          IPsecList={data.ipSecGre}
          apiData={data.ipSecGreData}
          onSave={submitHandler}
          locationDetailFromServer={locationDetailFromServer}
          readOnly={readOnly}
        />
      ),
      icon: "fa-regular fa-pipe-circle-check",
    },
    {
      title: "locations.menu.connection-options",
      value: "connection-options",
      content: () => (
        <LocationConnectionOptions
          {...LOCATION_CONNECTION_OPTIONS_DATA}
          optionList={data.connectionOptions}
          connectionOptionData={data.connectionOptionData}
          onSave={submitHandler}
          locationDetailFromServer={locationDetailFromServer}
          readOnly={readOnly}
        />
      ),
      icon: "fa-solid fa-arrow-up-arrow-down",
    },
    {
      title: "locations.menu.sublocations",
      value: "sublocations",
      content: () => (
        <SubLocation
          {...SUBLOCATION_DATA}
          updateSubLocationCount={updateSubLocationCount}
          subLocationCount={locationDetailFromServer.subLocationCount === 0}
          locationId={locationDetailFromServer.id}
          apiData={locationDetailFromServer}
          readOnly={readOnly}
        />
      ),
      icon: "fa-regular fa-network-wired",
      count: sublocationCount ?? data.subLocationCount,
    },
  ];

  const handleTabClick = (tab: TabConfig) => {
    if (locationType === "CLOUD" && tab.value === "appliances") {
      router.push("/ec/administration/provisioning-templates?filter=CC");
    } else {
      setSelectedTabs(tab);
      const params = new URLSearchParams(queryParams.toString());
      params.set("tab", tab.value);
      router.push(`${pathName}?${params.toString()}`);
    }
  };

  const crumbOpts: BreadcrumbsItemsArrayProps[] = [
    {
      href: "/locations",
      label: t("locations.menu.locations"),
      id: "1",
    },
    {
      href: "",
      label: locationName ?? "None",
      id: "2",
    },
  ];

  const hasProducts = hasProductRights(LOCATION_PRODUCTS, productAccessInfo);

  if (!hasProducts || !can("showUnifiedLocations")) {
    window.location.replace("/error");
  }

  const successHandler = (data: LocationDetailFromServer) => {
    const transformed: TransformedLocationDetails = responseTransformer(
      data,
      productAccessInfo,
    );
    const tabs = getTabs(transformed, data);
    const _updatedTab = tabs.filter((_tab, index) => index !== 1);
    setLocationName(data?.name);
    setLocationType(data?.type);
    setTabs(tabs);
    setTransformed(transformed);
    setSelectedTabs(tabs[0]);
  };

  const successHandlerUpdateSubLocationCount = (
    data: LocationDetailFromServer,
  ) => {
    const transformed: TransformedLocationDetails = responseTransformer(
      data,
      productAccessInfo,
    );
    const tabs = getTabs(transformed, data);
    setLocationType(data?.type);
    const _updatedTab = tabs.filter((_tab, index) => index !== 1);
    setTabs(tabs);
  };
  const errorHandler = {
    onError: (error: Error) => {
      const errorCause = error.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }

      showErrorAlert(errorCause, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };

  const updateSubLocationCount = (count: number) => {
    setSublocationCount(count);
  };

  const submitHandler = (
    locationDetail: Partial<
      Omit<
        LocationDetailFromServer,
        "locationInfo" | "ipSecGre" | "connectionOptions"
      >
    >,
  ) => {
    void updateLocation.trigger(locationDetail);
  };

  const updateLocation = useSWRMutation(
    `${API_ENDPOINTS.ZUXP}/unified-locations/locations/${locationId}`,
    putReq,
    {
      onSuccess: (data) => {
        if (data) {
          successHandler(data as LocationDetailFromServer);
          setLocationDetails(data as LocationDetailFromServer);
        }
      },
      ...errorHandler,
    },
  );

  const locationDetailsFromServer = useSWR(
    !didInit
      ? `${API_ENDPOINTS.ZUXP}/unified-locations/locations/${locationId}`
      : null,
    getReq,
    {
      onSuccess: (data: LocationDetailFromServer) => {
        setDidInit(true);
        successHandler(data);
        setLocationDetails(data);
      },
      ...errorHandler,
    },
  );

  return (
    <>
      <div
        className="pb-rem-160 flex flex-col space-y-rem-40 h-full -mt-rem-160"
        data-testid="unified-locations-list"
      >
        <WithStates
          loading={locationDetailsFromServer.isLoading}
          loadingComponent={
            <div
              className={
                "overflow-y-auto overflow-x-hidden h-full pt-rem-240 flex justify-center items-center"
              }
            >
              <LoadingSpinner ariaLabel={t("LOADER_ICON")} />
            </div>
          }
        >
          {updateLocation?.isMutating && (
            <div className="z-[999]">
              <Overlay id="unified-locations" />
            </div>
          )}
          {alertMessage && (
            <Alert
              alert={{
                message: alertMessage,
                type: alertType,
              }}
            />
          )}
          <div className="border-b border-semantic-border-interactive-primary-disabled p-rem-80">
            <Breadcrumbs items={crumbOpts} />
          </div>
          {selectedTab && (
            <LADetailLayout
              navTopper={() => (
                <div className="rounded-l">
                  {
                    <WorldMap
                      {...{
                        ...mapData,
                        longitude: transformed?.locationDetails[4].value
                          ? parseInt(transformed?.locationDetails[4].value)
                          : 0,
                        latitude: transformed?.locationDetails[3].value
                          ? parseInt(transformed?.locationDetails[3].value)
                          : 0,
                      }}
                    />
                  }
                </div>
              )}
              verticalTabsProps={{
                tabs: tabs!,
                onTabClick: handleTabClick,
                selectedTab: selectedTab.value,
              }}
              content={selectedTab.content}
            />
          )}
        </WithStates>
      </div>
    </>
  );
};

export default LocationDetail;
