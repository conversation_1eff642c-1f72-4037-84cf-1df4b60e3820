/* eslint-disable @up/unified-platform/max-file-lines */
"use client";
import { Alert, ZDataTable, ZMenu } from "@xc/legacy-components";
import { useTranslation } from "react-i18next";
import { type ReactElement, useEffect, useRef, useState } from "react";
import useSWRMutation from "swr/mutation";
import { debounce } from "lodash";
import useSWR from "swr";
import DeleteModal from "../Components/DeleteModal/DeleteModal";
import Tooltip from "../Components/Tootip/Tooltip";
import {
  COUNTRIES,
  LOCATION_PRODUCTS,
  ADMIN_ACCESS_TYPE,
  TIMEZONES,
  trafficTypeOptions,
} from "../constants";
import { extractErrorDetails, hasProductRights } from "../helper";
import { getKeyofObject } from "../utils/utils";
import { type ButtonProps } from "../Appliances/components/ApplianceList/ApplianceButtonGroup";
import LocationTableControls from "./LocationList/LocationTableControls";
import {
  tableColumnFn,
  tablePaginationData,
  type LocationRowProps,
} from "./LocationList/LocationTableConfig";
import responseTransformer from "./LocationList/LocationListTransformer";
import { LOCATIONS_ENDPOINTS, paginationGetReq } from "./config/apiUtils";
import {
  type ManagedByResponse,
  type ManualGroupResponse,
  type IdLabelProps,
  type ResponseBody,
  type TableRenderItem,
  type AllVirtualZenClustersResponse,
  type AllVirtualZensResponse,
  type AllIpAddressesResponse,
  type IPGreTunnelResponse,
  type ALLVpnCredentialResponse,
  type ProxyPortResponse,
  type VPNCredentalProps,
  type AdminAccessTypes,
} from "./types";
import { UnifiedLocationQueries } from "./query";
import LocationDrawer from "./Drawers/LocationDrawer/LocationDrawer";
import {
  ADD_LOCATION_CONNECTION_OPTIONS_DATA,
  ADD_LOCATION_DRAWER_DATA,
} from "./Drawers/LocationDrawer/LocationDrawer.data";
import { type LocationDrawerFormDataProps } from "./Drawers/types";
import { useFlags } from "@/context/FeatureFlags";
import TableLoader from "@/components/TableLoader/TableLoader";
import {
  API_ENDPOINTS,
  deleteByIdReq,
  getReq,
  postReq,
} from "@/utils/apiHelper";
import {
  endpointConditionHandler,
  type ErrorType,
  removeAlertMessage,
  showErrorAlert,
  showSuccessAlert,
} from "@/app/onboarding/apiHelper";
import { Overlay } from "@/components/OnboardingLayout/Overlay";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";

const segmentConfig = [
  {
    label: "locations.switcher.all-types",
    value: "",
  },
  {
    icon: "fa-buildings fa-regular",
    label: "locations.switcher.edge",
    value: "EDGE",
  },
  {
    icon: "fa-cloud fa-regular",
    label: "locations.switcher.cloud",
    value: "CLOUD",
  },
];

export const ButtonConfig: ButtonProps = {
  text: "locations.list.sync-locations",
  onClick: () => console.log("Sync Location triggered..."),
  type: "tertiary",
  id: "add",
  loading: false,
};

const Locations = () => {
  const [selectedSegement, setSelectedSegment] = useState(
    segmentConfig[0].value,
  );
  const [search, setSearch] = useState("");
  const [deleteLocation, setDeleteLocation] = useState<LocationRowProps | null>(
    null,
  );
  const tableRef = useRef<{ refreshData: () => void }>(null);
  const searchRef = useRef<string>("");
  const [openDrawer, setOpenDrawer] = useState(false);
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const [manualGroupList, setManualGroupList] = useState<IdLabelProps[]>([]);
  const [managedByList, setManagedByList] = useState<IdLabelProps[]>([]);
  const [hidePagination, setHidePagination] = useState(false);
  const [proxyPorts, setProxyPorts] = useState<IdLabelProps[]>([]);
  const [_ipGreTunnel, setIpGreTunned] = useState<IdLabelProps[]>([]);
  const [ipAddress, setIpAddress] = useState<IdLabelProps[]>([]);
  const [allvpnCred, setVpnCred] = useState<VPNCredentalProps[]>([]);
  const [allVirtualZens, setAllVirtualZens] = useState<IdLabelProps[]>([]);
  const [allVirtualZenClusters, setAllVirtualZenClusters] = useState<
    IdLabelProps[]
  >([]);
  const [callSyncLocation, setCallSyncLocation] = useState(false);
  const [checkSyncStatus, setCheckSyncStatus] = useState(false);
  const [readWritePermission, setReadWritePermission] =
    useState<AdminAccessTypes>({
      ziaSuperAdmin: true,
      ztwSuperAdmin: true,
    });
  const handleAddLocationDrawerDrawer = () => {
    setInitFormData(ADD_LOCATION_DRAWER_DATA.initFormData);
    setOpenDrawer(true);
  };
  const { t } = useTranslation();
  const ID = "locations";
  const ID_title = "locations-title";
  const productAccessInfo = useProductAccessProvider();

  const { can } = useFlags();
  const handleFilteredResult = debounce((value: string) => {
    setSearch(value);
    searchRef.current = value;
  }, 300);

  const timedErrorHandler = {
    onError: (err: Error) => {
      const errorCause = err.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }

      showErrorAlert(errorCause, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };
  const deleteLocationHandler = {
    onSuccess: () => {
      const locationRemovedMsg = t("locations.toast-message.delete-location");
      showSuccessAlert(locationRemovedMsg, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
      tableRef.current?.refreshData();
    },
    ...timedErrorHandler,
  };

  const deleteLocationbyId = useSWRMutation(
    LOCATIONS_ENDPOINTS.deleteLocation,
    deleteByIdReq,
    deleteLocationHandler,
  );
  const { featurePermissions } = (productAccessInfo.roles?.ZIA ?? {}) as {
    featurePermissions?: Record<string, string>;
  };
  const { featurePermissions: ztwFeaturePermissions } = (productAccessInfo
    .features?.CLOUD_CONNECTOR ?? {}) as {
    featurePermissions?: Record<string, string>;
  };

  useEffect(() => {
    if (productAccessInfo) {
      const newPermissions = {
        ziaSuperAdmin: featurePermissions?.LOCATIONS === ADMIN_ACCESS_TYPE,
        ztwSuperAdmin:
          ztwFeaturePermissions?.EDGE_CONNECTOR_CLOUD_PROVISIONING ===
            ADMIN_ACCESS_TYPE &&
          ztwFeaturePermissions?.EDGE_CONNECTOR_TEMPLATE === ADMIN_ACCESS_TYPE,
      };
      setReadWritePermission((prevPermissions) => {
        if (
          JSON.stringify(prevPermissions) === JSON.stringify(newPermissions)
        ) {
          return prevPermissions;
        }

        return newPermissions;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productAccessInfo]);

  const paginationData = useSWRMutation("paginationLoction", paginationGetReq);
  const fetchRows = (
    currentPage: number,
    pageSize: number,
    sortBy: string,
    sortDirection: string,
  ) =>
    paginationData
      .trigger({
        EndpointUrl: LOCATIONS_ENDPOINTS.fetchLocations({
          limit: pageSize,
          offset: currentPage,
          search: search,
          type: selectedSegement ?? "",
          fieldName: sortBy ?? "",
          orderBy: sortDirection?.toLowerCase() ?? "",
        }),
      })
      .then((response) => {
        const { data, pagination } = response as ResponseBody;
        setHidePagination(!(data.length > 0));

        return {
          totalItems: pagination.totalCount,
          totalPages: pagination.totalPages,
          items: data ? responseTransformer(data) : [],
        };
      })
      .catch((err) => console.log(err));

  const columnData = tableColumnFn();
  const actionIndex: number = columnData?.findIndex(
    (item) => item.id === "actions",
  );
  if (actionIndex !== -1 && columnData[actionIndex]) {
    columnData[actionIndex].renderItem = (row: TableRenderItem) => (
      <>
        {!row.item.ecLocation && (
          <div
            className={`flex gap-default ${row.item.ecLocation ? "pointer-events-none" : ""}`}
          >
            <ZMenu
              items={[
                {
                  id: "delete-action",
                  name: "DELETE",
                  iconClass: `fa-regular fa-trash ${row.item.ecLocation ? "text-semantic-content-interactive-primary-disabled" : "text-semantic-brand-default"} `,
                  ariaLabel: `DELETE-${row.item.location}`,
                },
              ]}
              onItemSelect={() => {
                setDeleteLocation(row.item);
              }}
              id={row.rowIndex}
            />
          </div>
        )}
      </>
    );
  }

  const hasProducts = hasProductRights(LOCATION_PRODUCTS, productAccessInfo);

  if (!hasProducts || !can("showUnifiedLocations")) {
    window.location.replace("/error");
  }

  const translatedTableColumnData = columnData.map((tcd) => {
    if (typeof tcd.name === "string") {
      tcd.name = <Tooltip content={t(tcd.name)} />;
    }

    return tcd;
  });

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "1"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_MANUAL_LOCATION_GROUPS,
      }),
    {
      onSuccess: (data) => {
        const manualGroupData = data as { data: ManualGroupResponse };
        const updateManualGroupList =
          manualGroupData.data.allManualLocationGroups.map(({ id, name }) => ({
            id: id.toString(),
            label: name,
          })) satisfies IdLabelProps[];

        setManualGroupList(updateManualGroupList);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "2"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_MANAGED_BY,
      }),
    {
      onSuccess: (data) => {
        const manualGroupData = data as { data: ManagedByResponse };
        const updateManagedByList = manualGroupData.data.allManagedBy.map(
          ({ id, name }) => ({
            id: id.toString(),
            label: name,
          }),
        ) satisfies IdLabelProps[];
        setManagedByList([...managedByList, ...updateManagedByList]);
      },
    },
  );

  const onDelete = (id: string) => {
    void deleteLocationbyId.trigger({ id: id.toString() });
    setDeleteLocation(null);
  };

  const updatedTrafficTypeOptions = trafficTypeOptions().slice(0, 4);

  const addLocationDrawerData = {
    ...ADD_LOCATION_DRAWER_DATA,
    locationGroups: {
      ...ADD_LOCATION_DRAWER_DATA.locationGroups,
      manualLocation: {
        ...ADD_LOCATION_DRAWER_DATA.locationGroups.manualLocation,
        options: manualGroupList,
      },
    },
    internetAccess: {
      ...ADD_LOCATION_DRAWER_DATA.internetAccess,
      managedby: {
        ...ADD_LOCATION_DRAWER_DATA.internetAccess.managedby,
        options: [
          ...ADD_LOCATION_DRAWER_DATA.internetAccess.managedby.options,
          ...managedByList,
        ],
      },
      trafficType: {
        ...ADD_LOCATION_DRAWER_DATA.internetAccess.trafficType,
        options: updatedTrafficTypeOptions,
      },
    },
    IPSec: {
      ...ADD_LOCATION_DRAWER_DATA.IPSec,
      proxyPorts: {
        ...ADD_LOCATION_DRAWER_DATA.IPSec.proxyPorts,
        options: proxyPorts,
      },
      staticIPAddresses: {
        ...ADD_LOCATION_DRAWER_DATA.IPSec.staticIPAddresses,
        options: Array.from(
          new Map([...ipAddress].map((item) => [item.label, item])).values(),
        ),
      },
      credentialsVPN: {
        ...ADD_LOCATION_DRAWER_DATA.IPSec.credentialsVPN,
        options: allvpnCred,
      },
      virtualZENs: {
        ...ADD_LOCATION_DRAWER_DATA.IPSec.virtualZENs,
        options: allVirtualZens,
      },
      virtualZENClusters: {
        ...ADD_LOCATION_DRAWER_DATA.IPSec.virtualZENClusters,
        options: allVirtualZenClusters,
      },
    },
  };

  const [initFormData, setInitFormData] = useState<LocationDrawerFormDataProps>(
    ADD_LOCATION_DRAWER_DATA.initFormData,
  );

  const handleAddLocation = (formData: LocationDrawerFormDataProps) => {
    setInitFormData(formData);
    const newFormData = {
      name: formData.name,
      locationInfo: {
        profile: formData.trafficType?.map((item) => item.id)?.toString(),
        description: formData?.description ?? "",
        parentId: 0,
        country: formData.country
          ? getKeyofObject(COUNTRIES, formData.country)
          : null,
        timeZone: formData?.timezone
          ? getKeyofObject(TIMEZONES, formData?.timezone)
          : null,
        geoOverride: false,
        longitude: Number(formData.longitude),
        latitude: Number(formData.latitude),
        excludeFromDynamicGroups: formData.excludeDynamicLocation,
        excludeFromManualGroups: formData.excludeManualLocation,
        ecLocation: false,
        otherSubLocation: false,
        other6SubLocation: false,
        ...(formData?.managedby?.some((item) => item.label !== "Self") && {
          managedBy: {
            id: Number(formData.managedby?.[0]?.id),
            name: formData.managedby?.[0]?.label,
          },
        }),
        state: formData.cityState,
        manualLocationGroups: Array.isArray(formData.manualLocation)
          ? formData.manualLocation?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
        dynamicLocationGroups: Array.isArray(formData.dynamicLocation)
          ? formData.dynamicLocation?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
      },

      ipSecGre: {
        ipAddresses: Array.isArray(formData.staticIPAddresses)
          ? formData.staticIPAddresses?.map((item) => item?.label)
          : [],
        ports: Array.isArray(formData.proxyPorts)
          ? formData.proxyPorts?.map((item) => Number(item?.label))
          : [],
        vpnCredentials: Array.isArray(formData.credentialsVPN)
          ? formData.credentialsVPN?.map((item) => ({
              id: item.id,
              type: item?.type ?? "",
              ...(item?.fqdn
                ? { fqdn: item?.fqdn }
                : { ipAddress: item?.ipAddress }),
            }))
          : [],
        virtualZens: Array.isArray(formData.virtualZENs)
          ? formData.virtualZENs?.map((item) => ({
              id: Number(item.id),
              name: item.label,
            }))
          : [],
        virtualZenClusters: Array.isArray(formData.virtualZENClusters)
          ? formData.virtualZENClusters?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
      },
      connectionOptions: {
        ...ADD_LOCATION_CONNECTION_OPTIONS_DATA,
        authRequired: Array.isArray(formData.proxyPorts) ? true : false,
      },
    };
    void addLocation(newFormData);
  };

  useSWR(
    endpointConditionHandler(
      callSyncLocation,
      `${API_ENDPOINTS.ZUXP}/unified-locations/sync-locations`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setCheckSyncStatus(true);
          void syncLocationStatus.mutate();
          setCallSyncLocation(false);
        } else {
          setCheckSyncStatus(false);
          setCallSyncLocation(false);
          tableRef?.current?.refreshData();
        }
      },
    },
  );

  const syncLocationStatus = useSWR(
    endpointConditionHandler(
      checkSyncStatus,
      `${API_ENDPOINTS.ZUXP}/unified-locations/locations/sync-status`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setTimeout(() => {
            void syncLocationStatus.mutate();
          }, 3000);
        } else {
          setCheckSyncStatus(false);
          tableRef?.current?.refreshData();
        }
      },
    },
  );

  const { trigger: addLocation, isMutating: addLocationMutating } =
    useSWRMutation(
      `${API_ENDPOINTS.ZUXP}/unified-locations/locations`,
      postReq,
      {
        onSuccess: () => {
          tableRef.current?.refreshData();
          setOpenDrawer(false);
          const locationRemovedMsg = t("locations.toast-message.add-location");
          showSuccessAlert(locationRemovedMsg, setAlertType, setAlertMessage);
          removeAlertMessage(setAlertType, setAlertMessage);
        },
        ...timedErrorHandler,
      },
    );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "proxy-port-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_PROVISIONING_PORTS,
      }),
    {
      onSuccess: (data) => {
        const proxyPortData = data as { data: ProxyPortResponse };
        const transformProxyPortData =
          proxyPortData.data.allProvisioningPorts.map((port, index) => ({
            id: index.toString(),
            label: port,
          }));
        setProxyPorts([...proxyPorts, ...transformProxyPortData]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "vpn-credential-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_VPN_CREDENTIALS,
      }),
    {
      onSuccess: (data) => {
        const vpnCredsData = data as { data: ALLVpnCredentialResponse };
        const updateVpnCredsData = vpnCredsData.data.allVpnCredentials
          .filter((item) => item?.name || item?.fqdn || item?.ipAddress)
          .map((item) => ({
            id: item?.id ?? "",
            name: item?.name ?? "",
            label: item.name
              ? item.name
              : item.type == "IP"
                ? item.ipAddress
                : item.fqdn,
            type: item?.type ?? "",
            fqdn: item?.fqdn ?? "",
            ipAddress: item?.ipAddress ?? "",
          }));
        setVpnCred([...allvpnCred, ...updateVpnCredsData]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "ip-gre-tunnel-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_IP_GRE_TUNNEL,
      }),
    {
      onSuccess: (data) => {
        const greTunnel = data as { data: IPGreTunnelResponse };
        const updateGreTunnel = greTunnel.data.allIpGreTunnels.map(
          (gretunnels) => ({
            id: gretunnels.ipAddress,
            label: gretunnels.ipAddress,
          }),
        );
        setIpGreTunned(updateGreTunnel);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "all-ip-addresses-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_IP_ADDRESSES,
      }),
    {
      onSuccess: (data) => {
        const allIpAddresses = data as { data: AllIpAddressesResponse };
        const updateIpAddressData = allIpAddresses.data.allIpAddresses.map(
          (ip, index) => ({
            id: index.toString(),
            label: ip,
          }),
        );
        setIpAddress((prevIpAddresses) => {
          const mergeData = [...prevIpAddresses, ...updateIpAddressData];
          const uniqData = Array.from(
            new Map(mergeData.map((item) => [item.label, item])).values(),
          );

          return uniqData;
        });
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );
  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "all-virtual-zens-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_VIRTUAL_ZENS,
      }),
    {
      onSuccess: (data) => {
        const virtualZensData = data as { data: AllVirtualZensResponse };
        const virtualZens = virtualZensData.data.allVirtualZens;
        const updateVirtualZenData =
          virtualZens.length > 0
            ? virtualZens?.map(({ id, name }) => ({
                id: id.toString(),
                label: name,
              }))
            : [];
        setAllVirtualZens([...allVirtualZens, ...updateVirtualZenData]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [
      `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
      "all-virtual-zen-cluster-api",
    ],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_VIRTUAL_ZEN_CLUSTER,
      }),
    {
      onSuccess: (data) => {
        const virtualZenClustersData = data as {
          data: AllVirtualZenClustersResponse;
        };
        const updateallVirtualZenClustersData =
          virtualZenClustersData.data.allVirtualZenClusters.map((item) => ({
            id: item.id.toString(),
            label: item.name,
          }));
        setAllVirtualZenClusters([
          ...allVirtualZenClusters,
          ...updateallVirtualZenClustersData,
        ]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );
  const handleSegmentControls = (val: string) => {
    setSelectedSegment(val);
  };

  const handleLocationSync = () => {
    setCallSyncLocation(true);
  };

  useEffect(() => {
    tableRef.current?.refreshData();
  }, [search, selectedSegement]);

  const isLoading = addLocationMutating;

  return (
    <div className="p-rem-160 flex flex-col gap-rem-80" data-testid={ID}>
      {alertMessage && (
        <Alert
          alert={{
            message: alertMessage,
            type: alertType,
          }}
        />
      )}
      {isLoading && (
        <div className="z-[999]">
          <Overlay id="appliance-overlay" />
        </div>
      )}
      <div
        className="typography-header3 text-semantic-content-base-primary"
        data-testid={ID_title}
      >
        {t("locations.list.title")}
      </div>
      <LocationTableControls
        handleOn={handleAddLocationDrawerDrawer}
        segmentControls={{
          controls: segmentConfig,
          value: selectedSegement,
          onChange: handleSegmentControls,
        }}
        search={{
          onChange: (val) => handleFilteredResult(val),
          text: search,
        }}
        buttonConfig={{
          ...ButtonConfig,
          onClick: handleLocationSync,
          loading: checkSyncStatus || callSyncLocation,
        }}
        adminPermission={readWritePermission}
      />
      <div className="flex max-h-[calc(100vh-200px)] overflow-hidden">
        <ZDataTable
          id={"location-list"}
          pagination={tablePaginationData}
          columns={translatedTableColumnData}
          noSelection
          fetchRows={fetchRows}
          isHeaderGrey={true}
          ref={tableRef}
          shouldRerenderOnResize={true}
          sorting={{
            enabled: true,
          }}
          loadingComponent={<TableLoader rows={50} />}
          hidePagination={hidePagination}
        />
      </div>
      <DeleteModal
        text="appliances.modal.location-text"
        subText="appliances.modal.location-subtext"
        isSubInterface={true}
        onClose={() => setDeleteLocation(null)}
        title="appliances.modal.confirm-delete"
        show={!!deleteLocation}
        onDelete={onDelete}
        id={deleteLocation?.id.toString()}
        warningMessageText="appliances.modal.undo-warning"
      />
      <LocationDrawer
        {...addLocationDrawerData}
        initFormData={initFormData}
        openDrawer={openDrawer}
        setOpenDrawer={setOpenDrawer}
        onSave={handleAddLocation}
      />
    </div>
  );
};

export default Locations;
