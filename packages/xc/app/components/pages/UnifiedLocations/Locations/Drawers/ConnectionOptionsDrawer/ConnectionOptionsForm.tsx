import {
  type ConnectionOptionsDrawerFormDataProps,
  type ConnectionOptionsProps,
} from "../types";
import ConnectionOptions from "../DrawerComponents/ConnectionOptions";

type Props = {
  connectionOptions: ConnectionOptionsProps;
  formData: ConnectionOptionsDrawerFormDataProps;
  setFormData: (formData: ConnectionOptionsDrawerFormDataProps) => void;
  validateOnChange?: (value: boolean) => void;
};

const ConnectionOptionsForm = ({
  connectionOptions,
  formData,
  setFormData,
  validateOnChange,
}: Props) => (
  <div
    className="flex flex-col gap-rem-80 pt-rem-160 px-rem-160 pb-rem-640"
    data-testid={`connection-options-form`}
  >
    <ConnectionOptions
      connectionOptions={connectionOptions}
      formData={formData}
      setFormData={setFormData}
      showFirewallControl={true}
      showXffForwarding={true}
      validateOnChange={validateOnChange}
    />
  </div>
);

export default ConnectionOptionsForm;
