/* eslint-disable @up/unified-platform/max-file-lines */
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import useSWR from "swr";
import {
  type ConnectionOptionsFormDataProps,
  type ConnectionOptionsProps,
} from "../types";
import FormCheckbox from "../../../Appliances/Drawers/DrawerComponents/FormCheckbox";
import FormInput from "../../../Appliances/Drawers/DrawerComponents/FormInput";
import FormSelect from "../../../Appliances/Drawers/DrawerComponents/FormSelect";
import { formatNumber, getKeyofObject } from "../../../utils/utils";
import { TIMEMAPPING, TIMEUNITMAPPING } from "../../../constants";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";
import { API_ENDPOINTS, getReq } from "@/utils/apiHelper";
import { WithStates } from "@/hoc/WithStates";
import { Loader } from "@/components/Analytics/Loader/Loader";

type Props = {
  connectionOptions: ConnectionOptionsProps;
  formData: ConnectionOptionsFormDataProps;
  setFormData: (formData: ConnectionOptionsFormDataProps) => void;
  showFirewallControl: boolean;
  showXffForwarding: boolean;
  validateOnChange?: (value: boolean) => void;
};

const ConnectionOptions = ({
  connectionOptions,
  formData,
  setFormData,
  showFirewallControl,
  showXffForwarding,
  validateOnChange,
}: Props) => {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { features, subscriptions, entitlements } = useProductAccessProvider();

  const hasIotSubscription =
    // eslint-disable-next-line @typescript-eslint/consistent-indexed-object-style
    (subscriptions.ZIA as unknown as Array<{ [key in string]: unknown }>).find(
      (ziaSub) => ziaSub.id === "ZT_IOT_VIS",
    )?.subscribed as boolean;

  const hasIotFeature =
    features.ZIA?.enableIotConfig &&
    (features.ZIA?.cloudNSSInfo as { webEnabled: boolean }).webEnabled;

  const showIotToggle = (hasIotFeature && hasIotSubscription) as boolean;

  const hasBasicAuth = features.ZIA?.basicAuthEnabled as boolean;
  const hasDigestAuth = features.ZIA?.digestAuthEnabled as boolean;

  const hasAnyError = (obj: Record<string, string>) => {
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === "object") {
        if (hasAnyError(obj[key])) return true;
      } else if (obj[key]) {
        return true;
      }
    }

    return false;
  };

  useEffect(() => {
    validateOnChange?.(hasAnyError(errors));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors]);
  const validateInput = (id: string, value: number) => {
    const limits: Record<string, { min: number; max: number }> = {
      download: {
        min: 0.1,
        max: 99999,
      },
      upload: {
        min: 0.1,
        max: 99999,
      },
    };

    const errorMessage =
      limits[id] && (value > limits[id].max || value < limits[id].min)
        ? t("locations.connectionOptions.max-limit-speed", { name: id })
        : "";

    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  const validateBandwidth = (value: boolean) => {
    const errorMessage = value
      ? t("locations.connectionOptions.max-limit-speed", { name: "download" })
      : "";
    setErrors((prev) => ({ ...prev, ["download"]: errorMessage }));
    const uploadErrorMessage = value
      ? t("locations.connectionOptions.max-limit-speed", { name: "upload" })
      : "";
    setErrors((prev) => ({ ...prev, ["upload"]: uploadErrorMessage }));
  };
  const validateAUP = (id: string, value: number) => {
    const limits: Record<string, number> = {
      min: 1,
      max: 180,
    };

    const errorMessage =
      value > limits.max || value < limits.min
        ? t("locations.connectionOptions.max-limit-frequency", {
            max: limits.max,
            min: limits.min,
          })
        : "";
    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  const validateInputTime = (id: string, value: number) => {
    const unmapTime = formData?.unmapTime;
    const revalidationTime = formData?.revalidationTime;

    const limits: Record<string, number> = {
      unmapUsers: TIMEMAPPING[unmapTime],
      revalidation: TIMEMAPPING[revalidationTime],
    };

    const errorMessage =
      limits[id] && (value > limits[id] || value < 1)
        ? t("locations.connectionOptions.max-limit-time", {
            time: formatNumber(limits[id]),
            name: `${getKeyofObject(TIMEMAPPING, limits[id])
              .toString()
              .toLowerCase()}s`,
          })
        : "";

    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  const { data: authSettings, isLoading } = useSWR<{
    kerberosEnabled: boolean;
  }>(`${API_ENDPOINTS.ZIA}/authSettings/lite`, getReq);
  const hasKerberosAuth = authSettings?.kerberosEnabled;

  return (
    <div
      className="bg-semantic-surface-inverted-base-primary pb-4"
      data-testid="connection-options-drawer-form"
    >
      <WithStates
        loading={isLoading}
        loadingComponent={<Loader customClass="h-rem-320 w-[16rem]" />}
      >
        <div className="flex flex-col">
          <FormCheckbox
            label={connectionOptions.authentication.label}
            id={connectionOptions.authentication.id}
            selected={formData.authentication}
            isToggle={true}
            onChange={(e) => {
              const isAuthChecked = e ? true : false;
              setFormData({
                ...formData,
                authentication: isAuthChecked,
                basicAuthentication: !isAuthChecked
                  ? false
                  : formData.basicAuthentication,
                digestAuthentication: !isAuthChecked
                  ? false
                  : formData.digestAuthentication,
                kerberosAuthentication: !isAuthChecked
                  ? false
                  : formData.kerberosAuthentication,
                ipSurrogate: !isAuthChecked ? false : formData.ipSurrogate,
                cookiesAndProxy: !isAuthChecked
                  ? "Cookie"
                  : formData.cookiesAndProxy,
                cautionWarning: !isAuthChecked
                  ? formData.cautionWarning
                  : false,
                aupWarning: !isAuthChecked ? formData.aupWarning : false,
                internetAccess: !isAuthChecked
                  ? formData.internetAccess
                  : false,
                sslInspection: !isAuthChecked ? formData.sslInspection : false,
              });
            }}
          />
          {!formData.authentication ? (
            <div className="flex flex-col pl-rem-240 mb-rem-80">
              <FormCheckbox
                label={connectionOptions.cautionWarning.label}
                id={connectionOptions.cautionWarning.id}
                selected={formData.cautionWarning}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    cautionWarning: e.target.checked,
                  });
                }}
              />
              <FormCheckbox
                label={connectionOptions.aupWarning.label}
                id={connectionOptions.aupWarning.id}
                selected={formData.aupWarning}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    aupWarning: e.target.checked,
                  });
                }}
              />
              {formData.aupWarning && (
                <div className="flex flex-col pl-rem-240 py-rem-80">
                  <div className="px-rem-120 w-2/4">
                    <FormInput
                      header={connectionOptions.aupFrequency.label}
                      value={formData.aupFrequency ?? ""}
                      placeholder=""
                      errorMessage={errors.aupFrequency}
                      formValidation={!!errors.aupFrequency}
                      id={connectionOptions.aupFrequency.id}
                      onChange={(value: string) => {
                        setFormData({ ...formData, aupFrequency: value });
                        validateAUP("aupFrequency", Number(value));
                      }}
                      type="number"
                      errorClassName="w-[400px]"
                    />
                  </div>

                  <div className="text-semantic-content-base-tertiary typography-paragraph2-strong truncate px-rem-120 pt-rem-80 mb-rem-40">
                    {t(connectionOptions.aupBehavior.heading)}
                  </div>
                  <div className="flex flex-col">
                    <FormCheckbox
                      label={connectionOptions.aupBehavior.internetAccess.label}
                      id={connectionOptions.aupBehavior.internetAccess.id}
                      selected={formData.internetAccess}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          internetAccess: e.target.checked,
                        });
                      }}
                    />
                    <FormCheckbox
                      label={connectionOptions.aupBehavior.sslInspection.label}
                      id={connectionOptions.aupBehavior.sslInspection.id}
                      selected={formData.sslInspection}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          sslInspection: e.target.checked,
                        });
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col pl-rem-240 gap-rem-80 pb-rem-80">
              <div className="flex flex-col">
                {hasBasicAuth && (
                  <FormCheckbox
                    label={connectionOptions.basicAuthentication.label}
                    id={connectionOptions.basicAuthentication.id}
                    selected={
                      formData.authentication
                        ? formData.basicAuthentication
                        : false
                    }
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        basicAuthentication: formData.authentication
                          ? e.target.checked
                          : false,
                      });
                    }}
                  />
                )}
                {hasDigestAuth && (
                  <FormCheckbox
                    label={connectionOptions.digestAuthentication.label}
                    id={connectionOptions.digestAuthentication.id}
                    selected={formData.digestAuthentication}
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        digestAuthentication: e.target.checked,
                      });
                    }}
                  />
                )}
                {!isLoading && hasKerberosAuth && (
                  <FormCheckbox
                    label={connectionOptions.kerberosAuthentication.label}
                    id={connectionOptions.kerberosAuthentication.id}
                    selected={
                      formData.authentication
                        ? formData.kerberosAuthentication
                        : false
                    }
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        kerberosAuthentication: formData.authentication
                          ? e.target.checked
                          : false,
                      });
                    }}
                  />
                )}
                {/* <FormCheckbox
                label={connectionOptions.jwtAuthentication.label}
                id={connectionOptions.jwtAuthentication.id}
                selected={formData.jwtAuthentication}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    jwtAuthentication: e.target.checked,
                  });
                }}
              /> */}
                <div className="flex flex-col gap-rem-80">
                  <FormCheckbox
                    label={connectionOptions.ipSurrogate.label}
                    id={connectionOptions.ipSurrogate.id}
                    selected={formData.ipSurrogate}
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        ipSurrogate: e.target.checked,

                        useIpSurrogate: !e.target.checked
                          ? false
                          : formData.useIpSurrogate,
                        cookiesAndProxy: !e.target.checked
                          ? "Cookie"
                          : formData.cookiesAndProxy,
                      });
                    }}
                  />
                  {formData.ipSurrogate && (
                    <div className="flex flex-col pl-rem-240 mb-rem-80">
                      <div className="flex flex-col px-rem-80">
                        <div className="text-semantic-content-base-tertiary typography-paragraph2-strong truncate mb-rem-40">
                          {t(connectionOptions.unmapUsers.label)}
                        </div>
                        <div className="flex flex-row gap-rem-80 mb-rem-80">
                          <div className="w-[27%]">
                            <FormInput
                              key={connectionOptions.unmapUsers.id}
                              header={""}
                              value={formatNumber(formData.unmapUsers) ?? ""}
                              placeholder=""
                              id={connectionOptions.unmapUsers.id}
                              errorMessage={errors.unmapUsers}
                              formValidation={!!errors.unmapUsers}
                              onChange={(value: string) => {
                                setFormData({
                                  ...formData,
                                  unmapUsers: value,
                                });
                                validateInputTime("unmapUsers", Number(value));
                              }}
                              errorClassName="w-[400px]"
                            />
                          </div>
                          <div className="w-[24%]">
                            <FormSelect
                              key={connectionOptions.unmapTime.id}
                              {...connectionOptions.unmapTime}
                              id={connectionOptions.unmapTime.id}
                              options={connectionOptions.unmapTime.options}
                              header={""}
                              onChange={(value) =>
                                setFormData({
                                  ...formData,
                                  unmapTime: getKeyofObject(
                                    TIMEUNITMAPPING,
                                    value.label,
                                  )?.toString(),
                                })
                              }
                              value={TIMEUNITMAPPING[formData.unmapTime] ?? ""}
                              label={t("NONE")}
                              customClass="w-full h-px-100"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-rem-80">
                        <FormCheckbox
                          label={connectionOptions.useIpSurrogate.label}
                          id={connectionOptions.useIpSurrogate.id}
                          selected={formData.useIpSurrogate}
                          onChange={(e) => {
                            setFormData({
                              ...formData,
                              useIpSurrogate: e.target.checked,
                              cookiesAndProxy: !e.target.checked
                                ? "Cookie"
                                : formData.cookiesAndProxy,
                            });
                          }}
                        />
                        {formData.useIpSurrogate && (
                          <div className="flex flex-col pl-rem-240">
                            <div className="flex flex-col px-rem-80">
                              <div className="text-semantic-content-base-tertiary typography-paragraph2-strong truncate mb-rem-40">
                                {t(connectionOptions.revalidation.label)}
                              </div>
                              <div className="flex flex-row gap-rem-80 mb-rem-80">
                                <div className="w-[27%]">
                                  <FormInput
                                    header={""}
                                    value={
                                      formatNumber(formData.revalidation) ?? ""
                                    }
                                    placeholder=""
                                    id={connectionOptions.revalidation.id}
                                    errorMessage={errors.revalidation}
                                    formValidation={!!errors.revalidation}
                                    onChange={(value: string) => {
                                      setFormData({
                                        ...formData,
                                        revalidation: value,
                                      });
                                      validateInputTime(
                                        "revalidation",
                                        Number(value),
                                      );
                                    }}
                                    errorClassName="w-[400px]"
                                  />
                                </div>
                                <div className="w-[24%]">
                                  <FormSelect
                                    {...connectionOptions.revalidationTime}
                                    id={connectionOptions.revalidationTime.id}
                                    options={
                                      connectionOptions.revalidationTime.options
                                    }
                                    header={""}
                                    onChange={(value) =>
                                      setFormData({
                                        ...formData,
                                        revalidationTime: getKeyofObject(
                                          TIMEUNITMAPPING,
                                          value.label,
                                        )?.toString(),
                                      })
                                    }
                                    value={
                                      TIMEUNITMAPPING[
                                        formData.revalidationTime
                                      ] ?? ""
                                    }
                                    label={t("NONE")}
                                    customClass="w-full h-px-100"
                                  />
                                </div>
                              </div>
                              <div className="flex flex-col pb-rem-240">
                                <div className="text-semantic-content-base-tertiary typography-paragraph2-strong truncate mb-rem-40">
                                  {t(connectionOptions.cookiesAndProxy.label)}
                                </div>
                                <div className="w-[38%]">
                                  <FormSelect
                                    {...connectionOptions.cookiesAndProxy}
                                    id={connectionOptions.cookiesAndProxy.id}
                                    options={
                                      connectionOptions.cookiesAndProxy.options
                                    }
                                    header={""}
                                    onChange={(value) =>
                                      setFormData({
                                        ...formData,
                                        cookiesAndProxy: value.label,
                                      })
                                    }
                                    value={formData.cookiesAndProxy ?? ""}
                                    label={t("NONE")}
                                    customClass="w-full h-px-100"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </WithStates>
      {showIotToggle && (
        <div className="flex flex-col">
          <FormCheckbox
            label={connectionOptions.iotDiscovery.label}
            id={connectionOptions.iotDiscovery.id}
            selected={formData.iotDiscovery}
            isToggle={true}
            onChange={(e) => {
              const isIotChecked = e ? true : false;
              setFormData({
                ...formData,
                iotDiscovery: isIotChecked,
                iotEnforcePolicySet: false,
              });
            }}
          />
          {formData.iotDiscovery && entitlements.iot && (
            <div className="flex flex-col pl-rem-240 mb-rem-80">
              <FormCheckbox
                label={connectionOptions.iotEnforcePolicySet.label}
                id={connectionOptions.iotEnforcePolicySet.id}
                selected={formData.iotEnforcePolicySet}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    iotEnforcePolicySet: e.target.checked,
                  });
                }}
              />
            </div>
          )}
        </div>
      )}
      {showFirewallControl && (
        <div className="flex flex-col">
          <FormCheckbox
            label={connectionOptions.firewallControl.label}
            id={connectionOptions.firewallControl.id}
            selected={formData.firewallControl}
            isToggle={true}
            onChange={(e) => {
              const isFirewallChecked = e ? true : false;
              setFormData({
                ...formData,
                firewallControl: isFirewallChecked,
                ipsControl: false,
              });
            }}
          />
          {formData.firewallControl && (
            <div className="pl-rem-240 mb-rem-80">
              <FormCheckbox
                label={connectionOptions.ipsControl.label}
                id={connectionOptions.ipsControl.id}
                selected={formData.ipsControl}
                onChange={(e) => {
                  setFormData({ ...formData, ipsControl: e.target.checked });
                }}
              />
            </div>
          )}
        </div>
      )}

      {showXffForwarding && (
        <div className="flex flex-col">
          <FormCheckbox
            label={connectionOptions.xffForwarding.label}
            id={connectionOptions.xffForwarding.id}
            selected={formData.xffForwarding}
            isToggle={true}
            onChange={(e) => {
              const isXffChecked = e ? true : false;
              setFormData({ ...formData, xffForwarding: isXffChecked });
            }}
            disabled={connectionOptions.xffForwarding.disabled}
          />
        </div>
      )}

      <div className="flex flex-col">
        <FormCheckbox
          label={connectionOptions.bandwidthControl.label}
          id={connectionOptions.bandwidthControl.id}
          selected={
            formData.bandwidthControl || formData.uploadSpeed ? true : false
          }
          isToggle={true}
          onChange={(e) => {
            const isBandwidthChecked = e ? true : false;
            setFormData({
              ...formData,
              bandwidthControl: isBandwidthChecked,
              downloadSpeed: "",
              uploadSpeed: "",
            });
            validateBandwidth(isBandwidthChecked);
          }}
        />
        {formData.bandwidthControl || formData.uploadSpeed ? (
          <div className="pl-rem-240 w-2/4 pt-rem-80">
            <div className="flex flex-col gap-rem-80 px-rem-80">
              <FormInput
                key={connectionOptions.downloadSpeed.id}
                header={connectionOptions.downloadSpeed.label}
                value={formatNumber(formData?.downloadSpeed) ?? ""}
                placeholder=""
                id={connectionOptions.downloadSpeed.id}
                errorMessage={errors.download}
                formValidation={!!errors.download}
                onChange={(value: string) => {
                  setFormData({
                    ...formData,
                    downloadSpeed: value,
                  });
                  validateInput("download", Number(value));
                }}
                errorClassName="w-[400px]"
              />
              <FormInput
                key={connectionOptions.uploadSpeed.id}
                header={connectionOptions.uploadSpeed.label}
                value={formatNumber(formData?.uploadSpeed) ?? ""}
                placeholder=""
                id={connectionOptions.uploadSpeed.id}
                errorMessage={errors.upload}
                formValidation={!!errors.upload}
                onChange={(value: string) => {
                  setFormData({
                    ...formData,
                    uploadSpeed: value,
                  });
                  validateInput("upload", Number(value));
                }}
                errorClassName="w-[400px]"
              />
            </div>
          </div>
        ) : (
          ""
        )}
      </div>
    </div>
  );
};

export default ConnectionOptions;
