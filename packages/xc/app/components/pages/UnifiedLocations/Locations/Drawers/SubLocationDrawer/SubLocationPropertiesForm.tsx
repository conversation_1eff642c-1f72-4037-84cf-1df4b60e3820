import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { type PropertiesProps } from "../types";
import InternetAccess from "../DrawerComponents/InternetAccess";
import LocationGroups from "../DrawerComponents/LocationGroups";
import FormInput from "../../../Appliances/Drawers/DrawerComponents/FormInput";
import DrawerFooter from "../../../Appliances/Drawers/DrawerComponents/DrawerFooter";
import FormTextArea from "../../../Appliances/Drawers/DrawerComponents/FormTextArea";
import InputList, {
  type ItemProp,
} from "@/components/pages/SecureTrafficStep2/InputList";
import { IPV4, IPV6, URL } from "@/configs/constants";

const SubLocationPropertiesForm = ({
  name,
  description,
  locationGroups,
  internetAccess,
  addressing,
  drawerFooterConfig,
  initFormData,
  onSave,
  onUpdate,
  onCancelClick,
  isFormChangeOnTab = false,
}: PropertiesProps) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({ ...initFormData });
  const [isFormChange, setIsFormChange] = useState<boolean>(isFormChangeOnTab);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [rawVpns, setRawVpns] = useState<ItemProp[]>(
    formData.internalIPAddress.map((ip) => ({
      label: ip,
      error: "",
      type: "ipV4",
    })),
  );

  const validateInput = (id: string, value: string) => {
    const limits: Record<string, number> = {
      name: 127,
      description: 1024,
    };
    const errorMessage =
      limits[id] && value.length > limits[id]
        ? t("MAX_CHARACTER_LIMIT", { max_len: limits[id] })
        : "";

    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  const handleAddVPN = (items: ItemProp[]) => {
    const ipV4: string[] = [];
    const ipV6: string[] = [];
    const url: string[] = [];
    const noErrors =
      items.length > 0 && items.every((item) => item.error === undefined);

    if (noErrors) {
      items.forEach((item) => {
        if (item.type === IPV4) {
          ipV4.push(item.label);
        }
        if (item.type === IPV6) {
          ipV6.push(item.label);
        }
        if (item.type === URL) {
          url.push(item.label);
        }
      });
    }
    setRawVpns([...items]);

    onSave?.({
      ...formData,
      internalIPAddress: items.map((vpn) => vpn.label),
    });
    setFormData({
      ...formData,
      internalIPAddress: items.map((vpn) => vpn.label),
    });
  };

  useEffect(() => {
    if (
      !isFormChangeOnTab ||
      JSON.stringify(formData) !== JSON.stringify(initFormData)
    ) {
      setIsFormChange(
        JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData]);
  if (onUpdate) {
    drawerFooterConfig.save.onButtonClick = onUpdate;
  }

  drawerFooterConfig.save.disabled = !isFormChange;
  drawerFooterConfig.cancel.onButtonClick = onCancelClick;

  const [locationGroupsFormData, setLocationGroupsFormData] = useState({
    excludeManualLocation: formData.excludeManualLocation,
    manualLocation: formData.manualLocation,
    excludeDynamicLocation: formData.excludeDynamicLocation,
    dynamicLocation: formData.dynamicLocation,
  });

  const [internetAccessFormData, setInternetAccessFormData] = useState({
    trafficType: formData.trafficType,
    managedby: formData.managedby,
  });

  const upliftState = () => {
    onSave?.({
      ...formData,
      ...locationGroupsFormData,
      ...internetAccessFormData,
    });
    setFormData({
      ...formData,
      ...locationGroupsFormData,
      ...internetAccessFormData,
    });
  };

  const handleChange = (value: string, fieldName: string) => {
    validateInput(fieldName, value);
    setFormData({ ...formData, [fieldName]: value });
    onSave?.({ ...formData, [fieldName]: value });
  };

  useEffect(() => {
    upliftState();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locationGroupsFormData, internetAccessFormData]);

  return (
    <div
      className="gap-rem-80 bg-semantic-surface-inverted-base-primary pt-rem-160 pb-rem-560"
      data-testid={`sub-location-drawer-form`}
    >
      <div className="flex flex-col gap-rem-160 pb-rem-280 px-rem-160">
        <FormInput
          errorMessage={errors.name}
          formValidation={!!errors.name}
          key={name.id}
          header={name.label}
          value={formData.name ?? ""}
          id={name.id}
          placeholder=""
          onChange={(value: string) => handleChange(value, "name")}
        />

        <FormTextArea
          errorMessage={errors.description}
          formValidation={!!errors.description}
          header={t(description.label)}
          value={formData.description ?? ""}
          id={description.id}
          placeholder=""
          onChange={(value: string) => handleChange(value, "description")}
        />
      </div>
      <LocationGroups
        locationGroups={locationGroups}
        formData={locationGroupsFormData}
        setFormData={setLocationGroupsFormData}
      />

      <InternetAccess
        internetAccess={internetAccess}
        formData={internetAccessFormData}
        setFormData={setInternetAccessFormData}
        isSubLocation
      />
      {!addressing.isDefaultSublocation && (
        <div className="flex flex-col">
          <span className="text-semantic-content-base-primary typography-header5 bg-semantic-surface-base-secondary py-rem-80 px-rem-160">
            {t(addressing.heading)}
          </span>

          <div className="flex flex-col gap-rem-80 p-rem-160">
            <div className="text-semantic-content-base-tertiary typography-paragraph2-strong truncate">
              {t(addressing.internalIPAddress.label)}
            </div>
            <InputList
              onAdd={handleAddVPN}
              vpns={rawVpns}
              placeHolder="ADD_ITEMS"
              className="!w-[393px] !h-[35px]"
              listClasses="w-[390px] !max-w-[393px]"
              id="sub-location-ip"
            />
          </div>
        </div>
      )}
      <DrawerFooter drawerFooterConfig={drawerFooterConfig} id="sub-location" />
    </div>
  );
};

export default SubLocationPropertiesForm;
