import { useEffect, useState } from "react";
import { type SubLocationConnectionOptionsProps } from "../types";
import ConnectionOptions from "../DrawerComponents/ConnectionOptions";
import DrawerFooter from "../../../Appliances/Drawers/DrawerComponents/DrawerFooter";
import FormCheckbox from "../../../Appliances/Drawers/DrawerComponents/FormCheckbox";

const SubLocationConnectionOptionsForm = ({
  connectionOptions,
  locationBandwidth,
  initFormData,
  drawerFooterConfig,
  onSave,
  onCancelClick,
  onUpdate,
  isFormChangeOnTab = false,
}: SubLocationConnectionOptionsProps) => {
  const [formData, setFormData] = useState(initFormData);
  const [isFormChange, setIsFormChange] = useState<boolean>(isFormChangeOnTab);
  const [saveDisabled, setSaveDisabled] = useState<boolean>(false);

  const checkValidation = (value: boolean) => {
    setSaveDisabled(value);
  };

  useEffect(() => {
    if (
      !isFormChangeOnTab ||
      JSON.stringify(formData) !== JSON.stringify(initFormData)
    ) {
      setIsFormChange(
        saveDisabled
          ? false
          : JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData, isFormChangeOnTab, saveDisabled]);
  if (onUpdate) {
    drawerFooterConfig.save.onButtonClick = onUpdate;
  }
  drawerFooterConfig.save.disabled = !isFormChange;
  drawerFooterConfig.cancel.onButtonClick = onCancelClick;

  const [connectionOptionsFormData, setConnectionOptionsFormData] = useState({
    firewallControl: formData.firewallControl,
    ipsControl: formData.ipsControl,
    authentication: formData.authentication,
    basicAuthentication: formData.basicAuthentication,
    digestAuthentication: formData.digestAuthentication,
    kerberosAuthentication: formData.kerberosAuthentication,
    ipSurrogate: formData.ipSurrogate,
    unmapUsers: formData.unmapUsers,
    unmapTime: formData.unmapTime,
    useIpSurrogate: formData.useIpSurrogate,
    revalidation: formData.revalidation,
    revalidationTime: formData.revalidationTime,
    cookiesAndProxy: formData.cookiesAndProxy,
    cautionWarning: formData.cautionWarning,
    aupWarning: formData.aupWarning,
    aupFrequency: formData.aupFrequency,
    internetAccess: formData.internetAccess,
    sslInspection: formData.sslInspection,
    iotDiscovery: formData.iotDiscovery,
    iotEnforcePolicySet: formData.iotEnforcePolicySet,
    xffForwarding: formData.xffForwarding,
    bandwidthControl: formData.bandwidthControl,
    downloadSpeed: formData.downloadSpeed,
    uploadSpeed: formData.uploadSpeed,
    jwtAuthentication: formData.jwtAuthentication,
  });

  const upliftState = () => {
    setFormData({
      ...formData,
      ...connectionOptionsFormData,
    });
    onSave({
      ...formData,
      ...connectionOptionsFormData,
    });
  };

  const handleLocationBandwidth = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      locationBandwidth: e.target.checked,
    });
  };

  useEffect(() => {
    upliftState();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connectionOptionsFormData]);

  return (
    <div
      className="gap-rem-80 bg-semantic-surface-inverted-base-primary"
      data-testid="sub-location-connection-options-drawer-form"
    >
      <div className="p-rem-160">
        <ConnectionOptions
          connectionOptions={connectionOptions}
          formData={connectionOptionsFormData}
          setFormData={setConnectionOptionsFormData}
          showFirewallControl={true}
          showXffForwarding={false}
          validateOnChange={checkValidation}
        />

        {formData.bandwidthControl && (
          <div className="flex flex-col pl-rem-140 gap-rem-80 pb-rem-640">
            <div className="flex flex-col gap-rem-80 px-rem-120">
              <FormCheckbox
                label={locationBandwidth.label}
                id={locationBandwidth.id}
                selected={formData.locationBandwidth}
                onChange={handleLocationBandwidth}
                prefix={locationBandwidth.prefix}
              />
            </div>
          </div>
        )}
      </div>

      <DrawerFooter
        drawerFooterConfig={drawerFooterConfig}
        id="sub-location-connection-options-form"
      />
    </div>
  );
};

export default SubLocationConnectionOptionsForm;
