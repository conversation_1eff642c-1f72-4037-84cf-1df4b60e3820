import { Drawer } from "@xc/legacy-components";
import {
  useEffect,
  useRef,
  useState,
  type Dispatch,
  type SetStateAction,
} from "react";
import {
  type ConnectionOptionsProps,
  type ConnectionOptionsDrawerFormDataProps,
  type DrawerFooterConfigProps,
} from "../types";
import DrawerFooter from "../../../Appliances/Drawers/DrawerComponents/DrawerFooter";
import DrawerHeader from "../../../Appliances/Drawers/DrawerComponents/DrawerHeader";
import ConnectionOptionsForm from "./ConnectionOptionsForm";

type Props = {
  openDrawer: boolean;
  setOpenDrawer: Dispatch<SetStateAction<boolean>>;
  heading: string;
  connectionOptions: ConnectionOptionsProps;
  drawerFooterConfig: DrawerFooterConfigProps;
  initFormData: ConnectionOptionsDrawerFormDataProps;
  onSave: (formData: ConnectionOptionsDrawerFormDataProps) => void;
};

const ConnectionOptionsDrawer = ({
  openDrawer,
  setOpenDrawer,
  heading,
  connectionOptions,
  drawerFooterConfig,
  initFormData,
  onSave,
}: Props) => {
  const drawerRef = useRef<{ hide: () => void; show: () => void }>();
  useEffect(() => {
    if (openDrawer) {
      drawerRef?.current?.show();
    }
  }, [openDrawer]);

  const ConnectionOptionsDrawerContent = () => {
    const [formData, setFormData] = useState(initFormData);
    const [isFormChange, setIsFormChange] = useState<boolean>(false);
    const [saveDisabled, setSaveDisabled] = useState<boolean>(false);

    const checkValidation = (value: boolean) => {
      setSaveDisabled(value);
    };

    const onSaveClick = () => {
      onSave(formData);
    };

    const onCancelClick = () => {
      drawerRef?.current?.hide();
      setOpenDrawer(false);
    };

    useEffect(() => {
      setIsFormChange(
        saveDisabled
          ? false
          : JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData, saveDisabled]);

    drawerFooterConfig.save.onButtonClick = onSaveClick;
    drawerFooterConfig.save.disabled = !isFormChange;
    drawerFooterConfig.cancel.onButtonClick = onCancelClick;

    return (
      <div
        className="flex flex-col h-full relative mb-[11rem]"
        data-testid={`connection-options-drawer`}
      >
        <DrawerHeader
          heading={heading}
          onCancel={onCancelClick}
          dataTestId="unified-locations-connection-options-drawer-close-icon"
        />

        <ConnectionOptionsForm
          connectionOptions={connectionOptions}
          formData={formData}
          setFormData={setFormData}
          validateOnChange={checkValidation}
        />

        <DrawerFooter
          drawerFooterConfig={drawerFooterConfig}
          id="connection-options"
        />
      </div>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      onClose={() => drawerRef?.current?.show()}
      contentRenderer={() => ConnectionOptionsDrawerContent()}
      backdrop={true}
      customBackdrop={true}
      width={{
        max: "558px",
        min: "558px",
        default: "fit",
      }}
    />
  );
};

export default ConnectionOptionsDrawer;
