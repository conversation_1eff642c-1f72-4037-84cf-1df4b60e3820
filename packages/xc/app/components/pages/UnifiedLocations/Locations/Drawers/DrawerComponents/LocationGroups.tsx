import { useTranslation } from "react-i18next";
import {
  type LocationGroupsFormDataProps,
  type LocationGroupProps,
} from "../types";
import FormCheckbox from "../../../Appliances/Drawers/DrawerComponents/FormCheckbox";
import FormMultiSelect from "../../../Appliances/Drawers/DrawerComponents/FormMultiSelect";

type Props = {
  locationGroups: LocationGroupProps;
  formData: LocationGroupsFormDataProps;
  setFormData: (formData: LocationGroupsFormDataProps) => void;
};

const LocationGroups = ({ locationGroups, formData, setFormData }: Props) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col" data-testid={`location-groups-form`}>
      <span className="text-semantic-content-base-primary typography-header5 bg-semantic-surface-base-secondary py-rem-80 px-rem-160">
        {t(locationGroups.heading)}
      </span>

      <div className="gap-rem-80 bg-semantic-surface-inverted-base-primary">
        <div className="flex flex-col p-rem-160">
          <div className="max-w-[300px]">
            <FormCheckbox
              label={locationGroups.excludeManualLocation.label}
              id={locationGroups.excludeManualLocation.id}
              selected={formData.excludeManualLocation}
              onChange={(e) => {
                setFormData({
                  ...formData,
                  excludeManualLocation: e.target.checked,
                  manualLocation: [],
                });
              }}
            />
          </div>
          {!formData.excludeManualLocation && (
            <div className="pl-rem-280 mb-rem-160">
              <FormMultiSelect
                {...locationGroups.manualLocation}
                id={locationGroups.manualLocation.id}
                options={locationGroups.manualLocation.options}
                header={locationGroups.manualLocation.label}
                onChange={(value) =>
                  setFormData({ ...formData, manualLocation: value })
                }
                value={formData.manualLocation ?? []}
                label={t("NONE")}
              />
            </div>
          )}
          <div className="max-w-[300px]">
            <FormCheckbox
              label={locationGroups.excludeDynamicLocation.label}
              id={locationGroups.excludeDynamicLocation.id}
              selected={formData.excludeDynamicLocation}
              onChange={(e) => {
                setFormData({
                  ...formData,
                  excludeDynamicLocation: e.target.checked,
                });
              }}
            />
          </div>

          {!formData.excludeDynamicLocation && (
            <div className="pl-rem-280">
              <div className="text-semantic-content-base-tertiary typography-paragraph2-strong">
                {t(locationGroups.dynamicLocation.label)}
              </div>
              <div className="text-semantic-content-base-tertiary typography-paragraph2-strong mt-rem-40">
                {Array.isArray(formData.dynamicLocation)
                  ? formData.dynamicLocation
                      ?.map((item) => item.label)
                      ?.join(", ")
                  : "--"}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LocationGroups;
