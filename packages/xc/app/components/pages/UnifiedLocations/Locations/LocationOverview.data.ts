import { type LocationDetailsProps, type LocationInfoProps } from "./types";

//TODO: value from LOCATION_INFO is not yet translated as it will come from API.
const LOCATION_INFO: LocationInfoProps[] = [
  {
    label: "locations.overview.cityState",
    value: "Chicago, IL",
  },
  {
    label: "locations.overview.country",
    value: "United States",
  },
  {
    label: "locations.overview.timezone",
    value: "Central -06:00",
  },
  {
    label: "locations.overview.latitude",
    value: "39.9828343",
  },
  {
    label: "locations.overview.longitude",
    value: "-83.155978",
  },
  {
    label: "locations.overview.manual",
    value: "None",
  },
  {
    label: "locations.overview.dynamic",
    value: "Excluded",
  },
  {
    label: "locations.overview.traffictype",
    value: "Workload Traffic Type",
  },
  {
    label: "locations.overview.managedby",
    value: "Self",
  },
];

const detailData = {
  description: "",
  profile: "",
  parentId: 1,
  country: "",
  state: "",
  city: {
    id: 1,
    name: "CA",
  },
  province: "",
  timeZone: "",
  geoOverride: false,
  longitude: 0,
  latitude: 0,
  traffictype: "",
  excludeFromDynamicGroups: false,
  excludeFromManualGroups: false,
  manualLocationGroups: [],
  dynamicLocationGroups: [],
  managedBy: null,
  ecLocation: false,
};

export const LOCATION_OVERVIEW_DATA: LocationDetailsProps = {
  locationName: "Atlanta",
  templateName: "Dummy Template",
  locationInfo: LOCATION_INFO,
  handleEditLocation: () => console.log("Edit location clicked"),
  handleRemoveLocation: () => console.log("Location removed"),
  locationDetailedData: detailData,
  readOnly: false,
};
