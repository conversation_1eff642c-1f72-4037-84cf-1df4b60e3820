import { type ReactNode } from "react";
import {
  type ConnectionOptionsDrawerFormDataProps,
  type LocationDrawerFormDataProps,
} from "../Drawers/types";
import { type LocationDetailFromServer } from "../LocationDetailTransformer";
import { type TableColumnItemProps } from "@/components/Analytics/DataTable/types";

export type SubLocationProps = {
  tableRowData?: TableRowItemProps[];
  columnData: SubLocationTableColumnItemProps[];
  onUserSearch?: (value: string) => void;
  isSearchExpanded?: boolean;
  userSearchText?: string;
  addButtonText: string;
  subLocationCount: boolean;
  apiData?: LocationDetailFromServer;
  onAddOpen: () => void;
  locationId: number;
  onSortChange: (value: string) => void;
  updateSubLocationCount: (count: number) => void;
  readOnly: boolean;
};

export type TableRowItemProps = {
  id: number;
  name: string;
  meta?: string | null;
  details: string[];
  authentication: string;
  firewall: string;
  xff: string;
  bandwidth: string;
  authenticationMeta: string[];
  firewallMeta: string[];
  bandWidthMeta: string[];
  otherLocation: boolean;
};

export type OutputData = {
  id: number;
  details: JSX.Element;
  sublocation: JSX.Element;
  actions?: JSX.Element;
  isRowExapdable: boolean;
  authentication: JSX.Element;
  firewall: JSX.Element;
  xff: JSX.Element;
  bandwidth: JSX.Element;
};

export type DetailMappingProps = {
  id?: string;
  items: string[];
};

export type SubLocationTableRenderItem = {
  columnIndex: number;
  rowIndex: number;
  item: TableRowItemProps;
  column: TableColumnItemProps;
};

export type SubLocationTableColumnItemProps = {
  id: string;
  name: string | ReactNode;
  isSortable: boolean;
  isHidden: boolean;
  renderItem?: (item: SubLocationTableRenderItem) => void;
  width: string;
  isPinned?: boolean;
  pinnedDirection?: string;
};

export type FormDataProps = LocationDrawerFormDataProps &
  ConnectionOptionsDrawerFormDataProps & {
    internalIPAddress: string[];
  };
