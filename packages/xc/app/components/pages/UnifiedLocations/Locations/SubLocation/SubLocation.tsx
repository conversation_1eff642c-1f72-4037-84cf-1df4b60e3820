/* eslint-disable @up/unified-platform/max-file-lines */

import { ZDataTable, ZMenu, Alert } from "@xc/legacy-components";
import debounce from "lodash/debounce";
import { useState, type ReactElement, useRef, useEffect } from "react";
import useSWRMutation from "swr/mutation";
import { usePathname } from "next/navigation";
import { t } from "i18next";
import { Button } from "@zs-nimbus/core";
import { faPlus } from "@fortawesome/pro-solid-svg-icons";
import useSWR from "swr";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import SubLocationDrawer from "../Drawers/SubLocationDrawer/SubLocationDrawer";
import {
  EDIT_SUB_LOCATION_DRAWER_DATA,
  SUB_LOCATION_DRAWER_DATA,
} from "../Drawers/SubLocationDrawer/SubLocationDrawer.data";
import { LOCATIONS_ENDPOINTS, paginationGetReq } from "../config/apiUtils";
import { tablePaginationData } from "../LocationList/LocationTableConfig";
import {
  type SubLocations,
  type SubLocationResponseBody,
  type IdLabelProps,
  type ManualGroupResponse,
  type ManagedByResponse,
  type UnitProps,
} from "../types";
import NoDataCard from "../NoDataCard/NoDataCard";
import DeleteModal from "../../Components/DeleteModal/DeleteModal";
import Tooltip from "../../Components/Tootip/Tooltip";
import {
  type SubLocationConnectionOptionsDrawerFormDataProps,
  type PropertiesFormDataProps,
} from "../Drawers/types";
import {
  trafficTypeOptions,
  trafficTypesMapping,
  UNMAP_USERS_TIME_DEFAULT,
  REVALIDATION_TIME_DEFAULT,
} from "../../constants";
import { extractErrorDetails } from "../../helper";
import { convertFromMinutes, convertToMinutes } from "../../utils/utils";
import { ButtonConfig } from "../LocationsList";
import { PROPERTIES_DATA } from "../Drawers/SubLocationDrawer/SubLocationProperties.data";
import { SUB_LOCATION_CONNECTION_OPTIONS_DATA } from "../Drawers/SubLocationDrawer/SubLocationConnectionOptions.data";
import {
  type TableRowItemProps,
  type SubLocationProps,
  type OutputData,
  type DetailMappingProps,
  type SubLocationTableRenderItem,
} from "./type";
import responseTransformer from "./SubLocationTransformer";
import { NO_SUBLOCATION_CARD_DATA } from "./SubLocation.data";
import SearchBar from "@/components/SearchBar/SearchBar";
import { getDataTestId } from "@/utils/utils";
import TableLoader from "@/components/TableLoader/TableLoader";
import { Link } from "@/components/Link/Link";
import {
  API_ENDPOINTS,
  deleteByIdReq,
  getReq,
  postReq,
  putReq,
} from "@/utils/apiHelper";
import { Overlay } from "@/components/OnboardingLayout/Overlay";
import {
  endpointConditionHandler,
  type ErrorType,
  removeAlertMessage,
  showErrorAlert,
  showSuccessAlert,
} from "@/app/onboarding/apiHelper";
import { Spinner } from "@/components/Spinner/Spinner";

const SubLocation = ({
  columnData,
  isSearchExpanded,
  addButtonText,
  onAddOpen,
  onSortChange,
  updateSubLocationCount,
  subLocationCount,
  locationId,
  apiData,
  readOnly,
}: SubLocationProps) => {
  const ID = "sub-location";
  const pathName = usePathname();
  const [search, setSearch] = useState("");
  const tableRef = useRef<{ refreshData: () => void }>(null);
  const [checkCountFromLocation, setCheckCountFromLocation] = useState(true);
  const [hidePagination, setHidePagination] = useState(false);
  const [totalSubLocationCount, setTotalSubLocationCount] = useState(0);
  const searchRef = useRef<string>("");

  const match = /locations\/(\d+)/.exec(pathName);
  const subLocationId = match?.[1];
  const [openDrawer, setOpenDrawer] = useState(false);
  const [openEditDrawer, setOpenEditDrawer] = useState(false);
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const [selectedRowData, setSelectedRowData] = useState<SubLocations[]>([]);
  const [selectedId, setSelectedId] = useState<number | null>(null);
  // const [drawerData, setDrawerData] = useState(null);
  const [deleteLocation, setDeleteLocation] =
    useState<TableRowItemProps | null>(null);
  const onClickEditHandler = (id: number | null) => {
    setSelectedId(id);
    setOpenEditDrawer(true);
  };
  const [manualGroupList, setManualGroupList] = useState<IdLabelProps[]>([]);
  const [managedByList, setManagedByList] = useState<IdLabelProps[]>([]);
  const [callSyncSubLocation, setCallSyncSubLocation] = useState(false);
  const [checkSyncStatus, setCheckSyncStatus] = useState(false);
  const result = selectedRowData?.find((item) => item.id === selectedId);
  const selectedDrawerConnectionData = result?.connectionOptions;
  const selectedPropertyData = result?.subLocationInfo;
  const { name, ipAddresses } = result ?? {};
  const locationTrafficType = apiData?.locationInfo?.profile;
  const locationConnectionOptions = apiData?.connectionOptions;
  const locationManagedBy = apiData?.locationInfo?.managedBy;

  const {
    description,
    excludeFromManualGroups,
    excludeFromDynamicGroups,
    manualLocationGroups,
    managedBy,
    dynamicLocationGroups,
    profile,
    ecLocation,
    otherSubLocation,
  } = selectedPropertyData ?? {};
  const updatedTrafficTypeOptions =
    ecLocation || locationTrafficType === "WORKLOAD"
      ? trafficTypesMapping().filter((_, index) => index !== 4)
      : trafficTypeOptions().slice(0, 4);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const editSubLocationDrawerData = {
    name: name ?? "",
    description: description ?? "",
    excludeManualLocation: excludeFromManualGroups ?? false,
    excludeDynamicLocation: excludeFromDynamicGroups ?? false,
    manualLocation: Array.isArray(manualLocationGroups)
      ? manualLocationGroups?.map((item) => ({
          id: item.id.toString(),
          label: item.name,
        }))
      : [],
    dynamicLocation: Array.isArray(dynamicLocationGroups)
      ? dynamicLocationGroups?.map((item) => ({
          id: item.id.toString(),
          label: item.name,
        }))
      : [],
    trafficType: profile
      ? updatedTrafficTypeOptions?.filter((item) => item?.id === profile)
      : [],
    managedby: managedBy
      ? [{ id: managedBy?.id, label: managedBy?.name }]
      : EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess.managedby
          .options,
    internalIPAddress: ipAddresses ?? [],
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const addSubLocationDrawerData = {
    ...PROPERTIES_DATA.initFormData,
    trafficType: locationTrafficType
      ? updatedTrafficTypeOptions?.filter(
          (item) => item?.id === locationTrafficType,
        )
      : [],
    managedby: locationManagedBy
      ? [{ id: locationManagedBy?.id, label: locationManagedBy?.name }]
      : [{ id: 1, label: "Self" }],
  };

  const propertyRef = useRef<PropertiesFormDataProps>(addSubLocationDrawerData);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const editConnectionOptionData = {
    authentication: selectedDrawerConnectionData?.authRequired ?? false,
    basicAuthentication:
      selectedDrawerConnectionData?.basicAuthEnabled ?? false,
    digestAuthentication:
      selectedDrawerConnectionData?.digestAuthEnabled ?? false,
    kerberosAuthentication: selectedDrawerConnectionData?.kerberosAuth ?? false,
    jwtAuthentication: selectedDrawerConnectionData?.jwtAuthentication ?? false,
    ipSurrogate: selectedDrawerConnectionData?.surrogateIp ?? false,
    unmapUsers: selectedDrawerConnectionData?.idleTimeInMinutes
      ? convertFromMinutes(
          Number(selectedDrawerConnectionData?.idleTimeInMinutes),
          selectedDrawerConnectionData?.idleTimeDisplayUnit?.toLowerCase() as UnitProps,
        )?.toString()
      : UNMAP_USERS_TIME_DEFAULT,
    unmapTime: selectedDrawerConnectionData?.idleTimeDisplayUnit
      ? selectedDrawerConnectionData?.idleTimeDisplayUnit
      : "",
    useIpSurrogate:
      selectedDrawerConnectionData?.enableSurrogateForKnownBrowsers ?? false,
    revalidation: selectedDrawerConnectionData?.refreshTimeSurrogacy
      ? convertFromMinutes(
          Number(selectedDrawerConnectionData.refreshTimeSurrogacy),
          selectedDrawerConnectionData?.refreshTimeSurrogacyDisplayUnit?.toLowerCase() as UnitProps,
        )?.toString()
      : REVALIDATION_TIME_DEFAULT,
    revalidationTime:
      selectedDrawerConnectionData?.refreshTimeSurrogacyDisplayUnit
        ? selectedDrawerConnectionData?.refreshTimeSurrogacyDisplayUnit
        : "",
    cookiesAndProxy: selectedDrawerConnectionData?.cookiesAndProxy
      ? "Cookie and Proxy"
      : "Cookie",
    iotDiscovery: selectedDrawerConnectionData?.iotDiscoveryEnabled ?? false,
    iotEnforcePolicySet:
      selectedDrawerConnectionData?.iotEnforcePolicySet ?? false,
    firewallControl: selectedDrawerConnectionData?.enableFirewall ?? false,
    ipsControl: selectedDrawerConnectionData?.enableIps ?? false,
    xffForwarding: selectedDrawerConnectionData?.xffEnabled ?? false,
    bandwidthControl: selectedDrawerConnectionData?.bandwidthControl ?? false,
    downloadSpeed: selectedDrawerConnectionData?.downloadBandwidth
      ? (
          Number(selectedDrawerConnectionData?.downloadBandwidth) / 1000
        )?.toString()
      : "",
    uploadSpeed: selectedDrawerConnectionData?.uploadBandwidth
      ? (
          Number(selectedDrawerConnectionData?.uploadBandwidth) / 1000
        )?.toString()
      : "",
    cautionWarning: selectedDrawerConnectionData?.cautionEnabled ?? false,
    aupWarning: selectedDrawerConnectionData?.aupEnabled ?? false,
    aupFrequency: selectedDrawerConnectionData?.aupEnabled
      ? (selectedDrawerConnectionData?.aupTimeoutInDays?.toString() ?? "30")
      : "30",
    internetAccess: selectedDrawerConnectionData?.aupBlockInternet ?? false,
    sslInspection: selectedDrawerConnectionData?.aupForceSslInspection ?? false,
    locationBandwidth: selectedDrawerConnectionData?.locationBandwidth ?? false,
  };

  // eslint-disable-next-line
  const addConnectionOptionData = {
    ...SUB_LOCATION_CONNECTION_OPTIONS_DATA.initFormData,
    authentication: locationConnectionOptions?.authRequired ?? false,
    basicAuthentication: locationConnectionOptions?.basicAuthEnabled ?? false,
    digestAuthentication: locationConnectionOptions?.digestAuthEnabled ?? false,
    kerberosAuthentication: locationConnectionOptions?.kerberosAuth ?? false,
    jwtAuthentication: locationConnectionOptions?.jwtAuthentication ?? false,
    ipSurrogate: locationConnectionOptions?.surrogateIp ?? false,
    unmapUsers: locationConnectionOptions?.idleTimeInMinutes
      ? convertFromMinutes(
          Number(locationConnectionOptions?.idleTimeInMinutes),
          locationConnectionOptions?.idleTimeDisplayUnit?.toLowerCase() as UnitProps,
        )?.toString()
      : UNMAP_USERS_TIME_DEFAULT,
    unmapTime: locationConnectionOptions?.idleTimeDisplayUnit
      ? locationConnectionOptions?.idleTimeDisplayUnit
      : "",
    useIpSurrogate:
      locationConnectionOptions?.enableSurrogateForKnownBrowsers ?? false,
    revalidation: locationConnectionOptions?.refreshTimeSurrogacy
      ? convertFromMinutes(
          Number(locationConnectionOptions.refreshTimeSurrogacy),
          locationConnectionOptions?.refreshTimeSurrogacyDisplayUnit?.toLowerCase() as UnitProps,
        )?.toString()
      : REVALIDATION_TIME_DEFAULT,
    revalidationTime:
      locationConnectionOptions?.refreshTimeSurrogacyDisplayUnit ?? "",
    cookiesAndProxy: locationConnectionOptions?.cookiesAndProxy
      ? "Cookie and Proxy"
      : "Cookie",
    iotDiscovery: locationConnectionOptions?.iotDiscoveryEnabled ?? false,
    iotEnforcePolicySet:
      locationConnectionOptions?.iotEnforcePolicySet ?? false,
    firewallControl: locationConnectionOptions?.enableFirewall ?? false,
    ipsControl: locationConnectionOptions?.enableIps ?? false,
    xffForwarding: locationConnectionOptions?.xffEnabled ?? false,
    bandwidthControl: locationConnectionOptions?.bandwidthControl ?? false,
    downloadSpeed: locationConnectionOptions?.downloadBandwidth
      ? (
          Number(locationConnectionOptions?.downloadBandwidth) / 1000
        )?.toString()
      : "",
    uploadSpeed: locationConnectionOptions?.uploadBandwidth
      ? (Number(locationConnectionOptions?.uploadBandwidth) / 1000)?.toString()
      : "",
    cautionWarning: locationConnectionOptions?.cautionEnabled ?? false,
    aupWarning: locationConnectionOptions?.aupEnabled ?? false,
    aupFrequency: locationConnectionOptions?.aupEnabled
      ? (locationConnectionOptions?.aupTimeoutInDays?.toString() ?? "30")
      : "30",
    internetAccess: locationConnectionOptions?.aupBlockInternet ?? false,
    sslInspection: locationConnectionOptions?.aupForceSslInspection ?? false,
  };
  const connectionRef = useRef<SubLocationConnectionOptionsDrawerFormDataProps>(
    addConnectionOptionData,
  );
  const editpropertyRef = useRef<PropertiesFormDataProps>(
    editSubLocationDrawerData,
  );
  const editconnectionRef =
    useRef<SubLocationConnectionOptionsDrawerFormDataProps>(
      editConnectionOptionData,
    );

  useEffect(() => {
    editpropertyRef.current = editSubLocationDrawerData;
  }, [editSubLocationDrawerData]);
  useEffect(() => {
    editconnectionRef.current = editConnectionOptionData;
  }, [editConnectionOptionData]);

  useEffect(() => {
    propertyRef.current = addSubLocationDrawerData;
  }, [addSubLocationDrawerData]);
  useEffect(() => {
    connectionRef.current = addConnectionOptionData;
  }, [addConnectionOptionData]);
  // for zsExternalPartner showing full options else not showing last extranet option.
  // Update zsExternalPartner when get from backend
  // for ecLocation(edge connector location) we need to show readonly traffic type dropdown so added workload in trafficTypesMapping for read only

  const timedErrorHandler = {
    onError: (err: Error) => {
      const errorCause = err.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }
      showErrorAlert(err?.cause as ErrorType, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };
  const deleteSubLocationHandler = {
    onSuccess: () => {
      const subLocationRemovedMsg = t(
        "locations.toast-message.delete-sublocation",
      );
      showSuccessAlert(subLocationRemovedMsg, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
      tableRef?.current?.refreshData();
      setDeleteLocation(null);
      totalSubLocationCount === 2
        ? updateSubLocationCount(0)
        : updateSubLocationCount(totalSubLocationCount - 1);
    },
    ...timedErrorHandler,
  };

  const deleteSubLocationbyId = useSWRMutation(
    LOCATIONS_ENDPOINTS.deleteLocation,
    deleteByIdReq,
    deleteSubLocationHandler,
  );

  useSWR(
    endpointConditionHandler(
      callSyncSubLocation,
      `${API_ENDPOINTS.ZUXP}/unified-locations/sync-sublocations/${locationId}`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setCallSyncSubLocation(false);
          setCheckSyncStatus(true);
          void syncSubLocationStatus.mutate();
        } else {
          setCallSyncSubLocation(false);
          setCheckSyncStatus(false);
          tableRef?.current?.refreshData();
        }
      },
    },
  );

  const syncSubLocationStatus = useSWR(
    endpointConditionHandler(
      checkSyncStatus,
      `${API_ENDPOINTS.ZUXP}/unified-locations/sublocations/${locationId}/sync-status`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setTimeout(() => {
            void syncSubLocationStatus.mutate();
          }, 3000);
        } else {
          setCheckSyncStatus(false);
          tableRef?.current?.refreshData();
        }
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "1"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: {
          query: `query {
            allManualLocationGroups{
              id,
              name
            }
        }`,
        },
      }),
    {
      onSuccess: (data) => {
        const manualGroupData = data as { data: ManualGroupResponse };
        const updateManualGroupList =
          manualGroupData.data.allManualLocationGroups.map(({ id, name }) => ({
            id: id.toString(),
            label: name,
          })) satisfies IdLabelProps[];

        setManualGroupList(updateManualGroupList);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "2"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: {
          query: `query {
            allManagedBy{
              id,
              name,
              type,
              disabled
            }
          }`,
        },
      }),
    {
      onSuccess: (data) => {
        const manualGroupData = data as { data: ManagedByResponse };
        const updateManualGroupList = manualGroupData.data.allManagedBy.map(
          ({ id, name }) => ({
            id: id.toString(),
            label: name,
          }),
        ) satisfies IdLabelProps[];
        setManagedByList([...managedByList, ...updateManualGroupList]);
      },
    },
  );

  const editPropertyDrawerData = {
    ...EDIT_SUB_LOCATION_DRAWER_DATA,
    propertiesData: {
      ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData,
      locationGroups: {
        ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.locationGroups,
        manualLocation: {
          ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.locationGroups
            .manualLocation,
          options: manualGroupList,
        },
      },
      internetAccess: {
        ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess,
        managedby: {
          ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess
            .managedby,
          options: [
            ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess
              .managedby.options,
            ...managedByList,
          ],
        },
        trafficType: {
          ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess
            .trafficType,
          options: updatedTrafficTypeOptions,
          readonly: false,
        },
      },
      addressing: {
        ...EDIT_SUB_LOCATION_DRAWER_DATA.propertiesData.addressing,
        isDefaultSublocation: otherSubLocation,
      },
      initFormData: editpropertyRef.current,
    },
    connectionOptionsData: {
      ...EDIT_SUB_LOCATION_DRAWER_DATA.connectionOptionsData,
      initFormData: editconnectionRef.current,
    },
  };

  const errorHandler = {
    onError: (error: Error) => {
      //temporary to get pages to render if mock server not running
      const errorCause = error.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }
      showErrorAlert(errorCause, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };

  const updateSubLocation = useSWRMutation(
    `${API_ENDPOINTS.ZUXP}/unified-locations/locations/${selectedId}`,
    putReq,
    {
      onSuccess: (data) => {
        if (data) {
          void (async () => {
            await fetchRows(
              tablePaginationData.currentPage,
              tablePaginationData.pageSize,
            );
          })();
          tableRef?.current?.refreshData();
          setOpenEditDrawer(false);
        }
      },
      ...errorHandler,
    },
  );

  const onUpdateSubLocation = () => {
    const {
      name,
      description,
      trafficType,
      managedby,
      internalIPAddress,
      excludeDynamicLocation,
      excludeManualLocation,
      manualLocation,
      dynamicLocation,
    } = editpropertyRef.current;
    const {
      authentication,
      basicAuthentication,
      digestAuthentication,
      kerberosAuthentication,
      jwtAuthentication,
      ipSurrogate,
      unmapUsers,
      unmapTime,
      useIpSurrogate,
      revalidation,
      revalidationTime,
      cautionWarning,
      cookiesAndProxy,
      internetAccess,
      iotDiscovery,
      iotEnforcePolicySet,
      ipsControl,
      sslInspection,
      uploadSpeed,
      firewallControl,
      aupFrequency,
      aupWarning,
      xffForwarding,
      downloadSpeed,
    } = editconnectionRef.current;

    const locationData = {
      ...result,
      id: result?.id,
      name: name || result?.name,
      locationInfo: {
        ...result?.subLocationInfo,
        description: description,
        excludeFromDynamicGroups: excludeDynamicLocation,
        excludeFromManualGroups: excludeManualLocation,
        manualLocationGroups: Array.isArray(manualLocation)
          ? manualLocation?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
        dynamicLocationGroups: Array.isArray(dynamicLocation)
          ? dynamicLocation?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
        managedBy: managedby?.some((item) => item.label !== "Self")
          ? {
              id: Number(managedby?.[0]?.id),
              name: managedby?.[0]?.label,
            }
          : undefined,
        profile: trafficType?.map((item) => item.id)?.toString(),
      },
      ipSecGre: {
        ipAddresses: internalIPAddress || result?.ipAddresses,
      },
      connectionOptions: {
        ...result?.connectionOptions,
        authRequired: authentication,
        basicAuthEnabled: basicAuthentication,
        digestAuthEnabled: digestAuthentication,
        kerberosAuth: kerberosAuthentication,
        jwtAuthentication: jwtAuthentication,
        surrogateIp: ipSurrogate,
        idleTimeInMinutes: unmapUsers
          ? convertToMinutes(
              Number(unmapUsers),
              unmapTime?.toLowerCase() as UnitProps,
            )
          : 0,
        idleTimeDisplayUnit: unmapTime ?? "",
        enableSurrogateForKnownBrowsers: useIpSurrogate,
        refreshTimeSurrogacy: revalidation
          ? convertToMinutes(
              Number(revalidation),
              revalidationTime?.toLowerCase() as UnitProps,
            )
          : 0,
        refreshTimeSurrogacyDisplayUnit: revalidationTime ?? "",
        cookiesAndProxy: cookiesAndProxy === "Cookie" ? false : true,
        iotDiscoveryEnabled: iotDiscovery,
        iotEnforcePolicySet: iotEnforcePolicySet,
        enableFirewall: firewallControl,
        enableIps: ipsControl,
        xffEnabled: xffForwarding,
        downloadBandwidth: Number(downloadSpeed) * 1000,
        uploadBandwidth: Number(uploadSpeed) * 1000,
        cautionEnabled: cautionWarning,
        aupEnabled: aupWarning,
        aupTimeoutInDays: Number(aupFrequency),
        aupBlockInternet: internetAccess,
        aupForceSslInspection: sslInspection,
      },
      ipAddresses: internalIPAddress || result?.ipAddresses,
    };
    delete locationData?.subLocationInfo;
    void updateSubLocation.trigger(locationData);
  };

  const createSubLocationHandler = () => {
    const {
      name,
      description,
      trafficType,
      managedby,
      internalIPAddress,
      excludeDynamicLocation,
      excludeManualLocation,
      manualLocation,
      dynamicLocation,
    } = propertyRef.current;

    const {
      authentication,
      basicAuthentication,
      digestAuthentication,
      kerberosAuthentication,
      jwtAuthentication,
      ipSurrogate,
      unmapUsers,
      unmapTime,
      useIpSurrogate,
      revalidation,
      revalidationTime,
      cautionWarning,
      cookiesAndProxy,
      internetAccess,
      iotDiscovery,
      iotEnforcePolicySet,
      ipsControl,
      sslInspection,
      uploadSpeed,
      firewallControl,
      aupFrequency,
      aupWarning,
      xffForwarding,
      downloadSpeed,
    } = connectionRef.current;
    const payload = {
      name: name,
      locationInfo: {
        profile: trafficType?.map((item) => item.id)?.toString(),
        description: description,
        parentId: locationId,
        country: apiData?.locationInfo?.country,
        timeZone: apiData?.locationInfo?.timeZone,
        geoOverride: apiData?.locationInfo?.geoOverride,
        longitude: apiData?.locationInfo?.longitude,
        latitude: apiData?.locationInfo?.latitude,
        excludeFromDynamicGroups: excludeDynamicLocation,
        excludeFromManualGroups: excludeManualLocation,
        ecLocation: apiData?.locationInfo?.ecLocation,
        otherSubLocation: false,
        other6SubLocation: false,
        manualLocationGroups: Array.isArray(manualLocation)
          ? manualLocation?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
        dynamicLocationGroups: Array.isArray(dynamicLocation)
          ? dynamicLocation?.map((item) => ({
              id: item.id,
              name: item.label,
            }))
          : [],
        managedBy: managedby?.some((item) => item.label !== "Self")
          ? {
              id: Number(managedby?.[0]?.id),
              name: managedby?.[0]?.label,
            }
          : undefined,
      },

      ipSecGre: {
        ipAddresses: internalIPAddress,
      },
      connectionOptions: {
        ...result?.connectionOptions,
        authRequired: authentication,
        basicAuthEnabled: basicAuthentication,
        digestAuthEnabled: digestAuthentication,
        kerberosAuth: kerberosAuthentication,
        jwtAuthentication: jwtAuthentication,
        surrogateIp: ipSurrogate,
        idleTimeInMinutes: unmapUsers
          ? convertToMinutes(
              Number(unmapUsers),
              unmapTime?.toLowerCase() as UnitProps,
            )
          : 0,
        idleTimeDisplayUnit: unmapTime ?? "",
        enableSurrogateForKnownBrowsers: useIpSurrogate,
        refreshTimeSurrogacy: revalidation
          ? convertToMinutes(
              Number(revalidation),
              revalidationTime?.toLowerCase() as UnitProps,
            )
          : 0,
        refreshTimeSurrogacyDisplayUnit: revalidationTime ?? "",
        cookiesAndProxy:
          cookiesAndProxy !== "Cookie" && ipSurrogate ? true : false,
        iotDiscoveryEnabled: iotDiscovery,
        iotEnforcePolicySet: iotEnforcePolicySet,
        enableFirewall: firewallControl,
        enableIps: ipsControl,
        xffEnabled: xffForwarding,
        downloadBandwidth: Number(downloadSpeed) * 1000,
        uploadBandwidth: Number(uploadSpeed) * 1000,
        cautionEnabled: cautionWarning,
        aupEnabled: aupWarning,
        aupTimeoutInDays: Number(aupFrequency),
        aupBlockInternet: internetAccess,
        aupForceSslInspection: sslInspection,
      },
    };
    void createSubLocation.trigger(payload);
  };

  const createSubLocation = useSWRMutation(
    `${API_ENDPOINTS.ZUXP}/unified-locations/locations`,
    postReq,
    {
      onSuccess: () => {
        setTotalSubLocationCount(totalSubLocationCount + 1);
        void (async () => {
          await fetchRows(
            tablePaginationData.currentPage,
            tablePaginationData.pageSize,
          );
        })();
        tableRef?.current?.refreshData();
        setOpenDrawer(false);
      },
      ...errorHandler,
    },
  );

  const addPropertyDrawerData = {
    ...SUB_LOCATION_DRAWER_DATA,
    propertiesData: {
      ...SUB_LOCATION_DRAWER_DATA.propertiesData,
      locationGroups: {
        ...SUB_LOCATION_DRAWER_DATA.propertiesData.locationGroups,
        manualLocation: {
          ...SUB_LOCATION_DRAWER_DATA.propertiesData.locationGroups
            .manualLocation,
          options: manualGroupList,
        },
      },
      internetAccess: {
        ...SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess,
        managedby: {
          ...SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess.managedby,
          options: [
            ...SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess.managedby
              .options,
            ...managedByList,
          ],
        },
        trafficType: {
          ...SUB_LOCATION_DRAWER_DATA.propertiesData.internetAccess.trafficType,
          options: updatedTrafficTypeOptions,
        },
      },
      initFormData: propertyRef.current,
    },
    connectionOptionsData: {
      ...SUB_LOCATION_DRAWER_DATA.connectionOptionsData,
      initFormData: connectionRef.current,
    },
  };
  const translatedColumnData = columnData;
  const translatedTableColumnData = translatedColumnData.map((tcd) => {
    if (typeof tcd.name === "string") {
      tcd.name = <Tooltip content={t(tcd.name)} />;
    }

    return tcd;
  });

  const TitleMetaMapping = ({
    title,
    meta,
    id,
  }: {
    title: string;
    meta: string | undefined | null;
    id: number;
  }) => (
    <div
      className="flex flex-col w-full"
      data-testid={getDataTestId("title-mapping" + id, ID)}
    >
      {readOnly ? (
        <Tooltip content={title} />
      ) : (
        <Link
          data-unique-id="sub-location-drawer"
          className="text-semantic-content-interactive-primary-default cursor-pointer"
          onClick={() => onClickEditHandler(id)}
          tabIndex={0}
          data-testid={getDataTestId("drawer-link" + id, ID)}
        >
          <Tooltip content={title} />
        </Link>
      )}

      {meta && (
        <span className="text-[10px] text-semantic-content-base-tertiary">
          <Tooltip content={meta} />
        </span>
      )}
    </div>
  );

  const DetailsMapping = ({ items, id }: DetailMappingProps) => (
    <div
      className="flex flex-col typography-paragraph2 text-semantic-content-base-primary w-full"
      data-testid={getDataTestId(id, "details-mapping")}
    >
      <Tooltip content={items?.toString()} tooltipCustomClass="max-w-[800px]" />
    </div>
  );

  const ContentMapping = ({ title, id }: { title: string; id?: string }) => (
    <div
      className="flex flex-col typography-paragraph2 text-semantic-content-base-primary w-full"
      data-testid={getDataTestId(id, "content-mapping")}
    >
      <span>{title ? <Tooltip content={title} /> : "--"}</span>
    </div>
  );

  const transformTableData = (tableData: TableRowItemProps[]): OutputData[] =>
    tableData?.map(
      ({
        id,
        name,
        meta,
        details,
        authentication,
        firewall,
        xff,
        bandwidth,
        otherLocation,
      }) => ({
        id,
        details: <DetailsMapping id={`${id}`} items={details} />,
        sublocation: <TitleMetaMapping title={name} meta={meta} id={id} />,
        authentication: <ContentMapping id={`${id}`} title={authentication} />,
        firewall: <ContentMapping id={`${id}`} title={firewall} />,
        xff: (
          <div className="typography-paragraph2 text-semantic-content-base-primary">
            {xff}
          </div>
        ),
        bandwidth: <ContentMapping id={`${id}`} title={bandwidth} />,
        isRowExapdable: false,
        otherLocation: otherLocation,
      }),
    );

  const actionIndex: number = columnData?.findIndex(
    (item) => item.id === "actions",
  );
  if (actionIndex !== -1 && columnData[actionIndex]) {
    columnData[actionIndex].renderItem = (row: SubLocationTableRenderItem) => (
      <>
        {!row.item.otherLocation && (
          <div
            className={`flex gap-default ${row.item.otherLocation || readOnly ? "pointer-events-none" : ""}`}
          >
            <ZMenu
              items={[
                {
                  id: "delete-action",
                  name: "DELETE",
                  iconClass: `fa-regular fa-trash  ${row.item.otherLocation || readOnly ? "text-semantic-content-interactive-primary-disabled" : "text-semantic-brand-default"} `,
                  ariaLabel: `DELETE-${row.item.name}`,
                },
              ]}
              onItemSelect={() => {
                setDeleteLocation(row.item);
              }}
            />
          </div>
        )}
      </>
    );
  }

  const handleFilteredResult = debounce((value: string) => {
    setSearch(value);
    searchRef.current = value;
    tableRef?.current?.refreshData();
  }, 300);
  const AddClickHandler = () => {
    setOpenDrawer(true);
    onAddOpen();
  };
  const paginationData = useSWRMutation(
    "pagination Sublocation",
    paginationGetReq,
  );

  const updatedNoDataCardData = {
    ...NO_SUBLOCATION_CARD_DATA,
    readOnly,
    NoDataCardButton: () => setOpenDrawer(true),
  };

  const fetchRows = (currentPage: number, pageSize: number) =>
    paginationData
      .trigger({
        EndpointUrl: searchRef.current
          ? LOCATIONS_ENDPOINTS.sublocationListSearch(subLocationId!, {
              limit: pageSize,
              offset: currentPage,
              search: searchRef.current,
            })
          : LOCATIONS_ENDPOINTS.sublocationList(subLocationId!, {
              limit: pageSize,
              offset: currentPage,
            }),
      })
      .then((response) => {
        setCheckCountFromLocation(false);

        const { data, pagination } = response as SubLocationResponseBody;

        if ("subLocations" in data) {
          const totalSublocation = data?.subLocations?.length
            ? pagination.totalCount
            : 0;
          setHidePagination(totalSublocation === 0);
          !searchRef.current && setTotalSubLocationCount(totalSublocation);
          !searchRef.current && updateSubLocationCount(totalSublocation);
          setSelectedRowData(
            data?.subLocations?.length ? data.subLocations : [],
          );
        } else {
          const totalSublocation = data?.length ? pagination.totalCount : 0;
          setHidePagination(totalSublocation === 0);
          !searchRef.current && setTotalSubLocationCount(totalSublocation);
          !searchRef.current && updateSubLocationCount(totalSublocation);
          setSelectedRowData(data?.length ? data : []);
        }
        const transformedData = responseTransformer(
          searchRef.current === "" && "subLocations" in data
            ? data.subLocations
            : (data as SubLocations[]),
        ) as TableRowItemProps[];

        return {
          totalItems: pagination.totalCount,
          totalPages: pagination.totalPages,
          // if data is not there mock data will be shown.
          nestedItems: transformTableData(transformedData),
        };
      })
      .catch((err) => console.log(err));

  const onDelete = (id: string) => {
    void deleteSubLocationbyId.trigger({ id: id.toString() });
  };
  const showEmptySubloaction = checkCountFromLocation
    ? subLocationCount
    : totalSubLocationCount === 0;
  if (showEmptySubloaction) {
    return (
      <div
        className={`overflow-y-auto overflow-x-hidden h-full min-h-full pt-m`}
      >
        {alertMessage && (
          <Alert
            alert={{
              message: alertMessage,
              type: alertType,
            }}
          />
        )}
        {(updateSubLocation.isMutating || createSubLocation.isMutating) && (
          <div className="z-[999]">
            <Overlay id="sublocation-overlay" />
          </div>
        )}
        <NoDataCard {...updatedNoDataCardData} />
        <SubLocationDrawer
          {...addPropertyDrawerData}
          openDrawer={openDrawer}
          propertyRef={propertyRef}
          connectionRef={connectionRef}
          onSave={createSubLocationHandler}
          setOpenDrawer={setOpenDrawer}
          canEdit={false}
        />
      </div>
    );
  }
  const handleSublocationSync = () => {
    setCallSyncSubLocation(true);
  };

  return (
    <>
      {(updateSubLocation.isMutating || createSubLocation.isMutating) && (
        <div className="z-[999]">
          <Overlay id="appliance-overlay" />
        </div>
      )}
      {alertMessage && (
        <Alert
          alert={{
            message: alertMessage,
            type: alertType,
          }}
        />
      )}
      <div className="px-rem-80" data-testid={ID}>
        <div className="flex justify-between items-center mb-rem-80">
          <div>
            <div className="typography-header5 font-medium break-words text-semantic-content-base-primary">
              {t("locations.menu.sublocations")}
            </div>
          </div>
          <div className="flex gap-rem-160">
            <Button
              id={getDataTestId("sync-refresh", ID)}
              data-testid={getDataTestId("sync-refresh", ID)}
              key={ButtonConfig.id}
              onClick={handleSublocationSync}
              variant={"tertiary"}
              disabled={checkSyncStatus || callSyncSubLocation}
              suffixIcon={
                (checkSyncStatus || callSyncSubLocation) && (
                  <Spinner
                    size="lg"
                    id={ID}
                    iconClass="text-semantic-content-interactive-primary-disabled py-[11px]"
                  />
                )
              }
            >
              {t("locations.sublocation.sync-sublocation")}
            </Button>
            <SearchBar
              onChange={handleFilteredResult}
              isOnlyExpanded={isSearchExpanded}
              userSearchText={search}
            />
            {!readOnly && (
              <Button
                id={getDataTestId("add", ID)}
                data-testid={getDataTestId("add", ID)}
                variant="primary"
                prefixIcon={<FontAwesomeIcon icon={faPlus} />}
                onClick={AddClickHandler}
              >
                {t(addButtonText)}
              </Button>
            )}
          </div>
        </div>
        <div className="flex justify-end mb-rem-80" />
        <div className="flex max-h-[calc(100vh-200px)] overflow-hidden">
          <ZDataTable
            pagination={{ ...tablePaginationData }}
            columns={translatedTableColumnData}
            id={"subLocation-list"}
            sorting={{
              enabled: true,
            }}
            noSelection
            onSortChange={onSortChange}
            fetchRows={fetchRows}
            ref={tableRef}
            rowHeightClass={"!h-[68px]"}
            isHeaderGrey={true}
            shouldRerenderOnResize={true}
            loadingComponent={<TableLoader rows={7} />}
            hidePagination={hidePagination}
          />
        </div>

        <DeleteModal
          text="appliances.modal.sublocation-text"
          subText="appliances.modal.sublocation-subtext"
          isSubInterface={true}
          onClose={() => setDeleteLocation(null)}
          title="appliances.modal.confirm-delete"
          show={!!deleteLocation}
          id={deleteLocation?.id.toString()}
          onDelete={onDelete}
          warningMessageText="appliances.modal.undo-warning"
        />
        <SubLocationDrawer
          {...addPropertyDrawerData}
          openDrawer={openDrawer}
          propertyRef={propertyRef}
          connectionRef={connectionRef}
          onSave={createSubLocationHandler}
          setOpenDrawer={setOpenDrawer}
          canEdit={false}
        />
        <SubLocationDrawer
          {...editPropertyDrawerData}
          openDrawer={openEditDrawer}
          propertyRef={editpropertyRef}
          connectionRef={editconnectionRef}
          onSave={onUpdateSubLocation}
          setOpenDrawer={setOpenEditDrawer}
          canEdit={true}
        />
        {updateSubLocation.isMutating && (
          <div className="[&_div]:z-[999]">
            <Overlay id="unified-locations" />
          </div>
        )}
      </div>
    </>
  );
};

export default SubLocation;
