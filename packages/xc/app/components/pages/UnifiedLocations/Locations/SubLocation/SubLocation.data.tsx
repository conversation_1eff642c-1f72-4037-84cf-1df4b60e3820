import { t } from "i18next";
import {
  type SubLocationTableColumnItemProps,
  type SubLocationProps,
} from "./type";
import { getDataTestId } from "@/utils/utils";

export const SUBLOCATION_TABLE_DATA = {
  tableRowData: [
    {
      id: 1,
      name: "chicago-corp",
      meta: "Corporate VLAN",
      details: ["******** / 16"],
      authentication: "Enforced",
      authenticationMeta: [
        "Basic Auth",
        "Digest",
        "Kerberos Enabled IP Surrogate",
      ],
      firewall: "Enforced",
      firewallMeta: ["Eanble IPS"],
      xff: "Enabled",
      bandwidth: "",
      bandWidthMeta: [],
      otherLocation: true,
    },
    {
      id: 2,
      name: "chicago-wifi",
      meta: "Corporate wifi VLAN",
      details: ["********** / 28", "************", "************"],
      authentication: "Enforced",
      authenticationMeta: [
        "Basic Auth",
        "Digest",
        "Kerberos Enabled IP Surrogate",
      ],
      firewall: "Enforced",
      firewallMeta: [],
      xff: "Enabled",
      bandwidth: "",
      bandWidthMeta: [],
      otherLocation: true,
    },
    {
      id: 2,
      name: "chicago-guest",
      meta: "Guest wifi vlan",
      details: ["*********** / 24"],
      authentication: "Enforced",
      authenticationMeta: ["Caution Warning AUP Warning"],
      firewall: "Enforced",
      firewallMeta: [],
      xff: "\u2014",
      bandwidth: "Enforced",
      bandWidthMeta: ["3k up", "10k down"],
      otherLocation: true,
    },
  ],
  tableColumnFn: (): SubLocationTableColumnItemProps[] => [
    {
      id: "sublocation",
      name: "NAME",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
    {
      id: "details",
      name: "locations.subLocation.internal-ips",
      isSortable: false,
      isHidden: false,
      width: "15%",
    },
    {
      id: "authentication",
      name: "locations.subLocation.authentication",
      isSortable: false,
      isHidden: false,
      width: "15%",
    },
    {
      id: "firewall",
      name: "locations.subLocation.firewall",
      isSortable: false,
      isHidden: false,
      width: "10%",
    },
    {
      id: "xff",
      name: "locations.subLocation.xff",
      isSortable: false,
      isHidden: false,
      width: "15%",
    },
    {
      id: "bandwidth",
      name: "locations.subLocation.bandwidth",
      isSortable: false,
      isHidden: false,
      width: "15%",
    },
    {
      id: "actions",
      name: (
        <i
          aria-label={t("ICON", { name: "Action" })}
          className="fa fa-cog text-semantic-brand-default pr-rem-80"
        />
      ),
      isSortable: false,
      isHidden: false,
      width: "15%",
      isPinned: true,
      pinnedDirection: "right",
    },
  ],
};

export const NO_SUBLOCATION_CARD_DATA = {
  icon: (
    <i
      aria-label={t("OPERATION_ICON", {
        label: t("locations.sublocation.no-data-name"),
      })}
      className="text-semantic-content-base-tertiary typography-header3 fa-light fa-location-dot"
      data-testid={getDataTestId("sublocation", "no-data-card")}
    />
  ),
  name: "locations.sublocation.no-data-name",
  NoDataCardButton: () => console.log("Card Clicked"),
  description: "locations.sublocation.no-data-description",
  buttonText: "locations.sublocation.no-data-buttonText",
};

export const SUBLOCATION_DATA: SubLocationProps = {
  locationId: 123,
  tableRowData: SUBLOCATION_TABLE_DATA.tableRowData,
  columnData: SUBLOCATION_TABLE_DATA.tableColumnFn(),
  subLocationCount: true,
  onUserSearch: (value: string) => console.log(value),
  isSearchExpanded: true,
  addButtonText: "ADD",
  onAddOpen: () => console.log("Open Add Modal"),
  onSortChange: (value: string) => console.log(value),
  updateSubLocationCount: (count: number) =>
    console.log("Count Updated", count),
  readOnly: false,
};
