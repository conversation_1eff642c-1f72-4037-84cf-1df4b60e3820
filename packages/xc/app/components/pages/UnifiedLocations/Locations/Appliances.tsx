/* eslint-disable @up/unified-platform/max-file-lines */
"use client";

import { useEffect, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { useTranslation } from "react-i18next";
import { usePathname } from "next/navigation";
import { ZDataTable, Zselect } from "@xc/legacy-components";
import useSWRMutation from "swr/mutation";
import { Button } from "@zs-nimbus/core";
import { faPlus } from "@fortawesome/pro-solid-svg-icons";
import { cn } from "@up/components";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { t } from "i18next";
import useSWR from "swr";
import Tooltip from "../Components/Tootip/Tooltip";
import { VM_PRODUCT_MAP, VM_SIZE_MAP } from "../constants";
import {
  type TableRowItemProps,
  type AppliancesOutputData,
  type AppliancesProps,
  type FilterOptionsProps,
  type ApplianceResponseBody,
  type AppliancesDataSetProps,
} from "./types";
import { LOCATIONS_ENDPOINTS, paginationGetReq } from "./config/apiUtils";
import responseTransformer from "./ApplianceTransformer";
import { tablePaginationData } from "./LocationList/LocationTableConfig";
import NoDataCard from "./NoDataCard/NoDataCard";
import { NO_APPLIANCE_CARD_DATA } from "./Appliances.data";
import SearchBar from "@/components/SearchBar/SearchBar";
import StatusIndicator from "@/components/StatusIndicator/StatusIndicator";
import { getDataTestId } from "@/utils/utils";
import TableLoader from "@/components/TableLoader/TableLoader";
import { endpointConditionHandler } from "@/app/onboarding/apiHelper";
import { API_ENDPOINTS, getReq } from "@/utils/apiHelper";
import { Spinner } from "@/components/Spinner/Spinner";

const ApplianceGroupMapping = ({ data }: { data: AppliancesDataSetProps }) => (
  <div className="flex flex-col w-full">
    <div className="typography-paragraph2 text-semantic-content-base-primary">
      <Tooltip content={data?.name} />
    </div>
    {data?.meta && (
      <span className="text-[10px] text-semantic-content-base-tertiary">
        <Tooltip content={data?.meta} />
      </span>
    )}
  </div>
);

const AppliancesMapping = ({ data }: { data: AppliancesDataSetProps }) => (
  <div className="flex flex-col w-full">
    <Link
      href={
        data.status !== "NOT_DEPLOYED"
          ? `/appliances/${data?.id}?groupId=${data?.applianceGroupId}${data?.serialNumber ? `&serialNumber=${data?.serialNumber}` : ""}${data?.provTemplateName ? `&template=${data?.provTemplateName}` : ""}&name=${data?.name}${data.status ? `&status=${data?.status}` : ""}${data?.type ? `&model=${data.type}` : ""}`
          : ""
      }
      className="typography-paragraph2 text-semantic-content-interactive-primary-default hover:text-semantic-content-interactive-primary-hover"
    >
      <Tooltip content={data?.name} />
    </Link>
    {data?.meta && (
      <span className="text-[10px] text-semantic-content-base-tertiary">
        <Tooltip content={data?.meta} />
      </span>
    )}
  </div>
);

const TypeMapping = ({
  type,
  typeMeta,
}: {
  type: string;
  typeMeta: string | null;
}) => (
  <div className="flex flex-col w-full">
    <span className="text-xs">
      <Tooltip
        content={
          VM_PRODUCT_MAP[type as keyof typeof VM_PRODUCT_MAP]
            ? t(VM_PRODUCT_MAP[type as keyof typeof VM_PRODUCT_MAP])
            : type || "--"
        }
      />
    </span>
    {typeMeta && (
      <span className="text-[10px] text-semantic-content-base-tertiary">
        <Tooltip content={t(typeMeta)} />
      </span>
    )}
  </div>
);

export const StatusDetailsMap = {
  active: {
    icon: "fa-solid fa-circle-check text-semantic-content-status-success-secondary",
    text: "ACTIVE",
  },
  inactive: {
    icon: "fa-solid fa-circle-minus text-semantic-content-status-danger-secondary",
    text: "INACTIVE",
  },
  notdeployed: {
    icon: "fa-solid fa-circle-minus text-[#8590A6]",
    text: "NOT_DEPLOYED",
  },
  readytodeploy: {
    icon: "fa-solid fa-circle-info text-semantic-brand-default",
    text: "READY_TO_DEPLOY",
  },
} as const;

const Appliances = ({
  id,
  typeOptions,
  statusOptions,
  addButtonText,
  columnData,
  onBulkSelect,
  onBulkUnselect,
  onRowSelect,
  onRowUnSelect,
  onAddOpen,
  applianceCount,
  locationId,
  readOnly,
  ztwSuperAdmin,
}: AppliancesProps) => {
  const [userRoleFilters, setUserRoleFilters] = useState<FilterOptionsProps[]>(
    [],
  );
  const [userStatusFilters, setUserStatusFilters] = useState<
    FilterOptionsProps[]
  >([]);
  const pathName = usePathname();
  const match = /locations\/(\d+)/.exec(pathName);
  const applianceId = match?.[1];
  const { t } = useTranslation();
  const ID = getDataTestId(id);
  const tableRef = useRef<{ refreshData: () => void }>(null);
  const [search, setSearch] = useState("");
  const [callSyncAppliance, setCallSyncAppliance] = useState(false);
  const [checkSyncStatus, setCheckSyncStatus] = useState(false);
  const [hidePagination, setHidePagination] = useState(false);
  const handleUserRoleFilter = (options: FilterOptionsProps[]) => {
    setUserRoleFilters(options);
  };

  const handleUserStatusFilter = (options: FilterOptionsProps[]) => {
    setUserStatusFilters(options);
  };

  const handleResetFilter = (options: FilterOptionsProps[]) => {
    setUserRoleFilters(options);
    setUserStatusFilters(options);
  };

  const AddApplianceClickHandler = () => {
    onAddOpen();
  };

  // TODO: Remove _ prefix when actually used
  const handleFilteredResult = debounce(
    (value: string) => setSearch(value),
    300,
  );

  useEffect(() => {
    tableRef?.current?.refreshData();
  }, [userRoleFilters, search, userStatusFilters]);

  const transformTableData = (
    tableData: TableRowItemProps[],
  ): AppliancesOutputData[] =>
    tableData?.map(({ id, name, meta, dataSet }) => {
      const appliance = {
        id,
        name,
        meta,
        type: "",
        typeMeta: "",
        status: "",
        ipAddress: "",
        serialNumber: "",
        applianceGroupId: null,
        provTemplateName: "",
      };

      return {
        id,
        applianceGroup: <ApplianceGroupMapping data={appliance} />,
        isRowExapdable: false,
        children: dataSet?.map(
          ({
            id: childId,
            name: childName,
            meta: childMeta,
            type,
            typeMeta,
            status,
            ipAddress,
            serialNumber,
            applianceGroupId,
            provTemplateName,
            vmSize,
          }) => {
            const { icon, text } =
              StatusDetailsMap[
                status?.toLowerCase() as keyof typeof StatusDetailsMap
              ] || {};
            const childAppliance = {
              id: childId,
              name: childName,
              meta: childMeta,
              type: type,
              typeMeta: typeMeta,
              status: status,
              ipAddress: ipAddress,
              serialNumber: serialNumber,
              applianceGroupId,
              provTemplateName,
              vmSize,
            };

            return {
              id: childId,
              appliance: <AppliancesMapping data={childAppliance} />,
              type: (
                <TypeMapping
                  type={type}
                  typeMeta={VM_SIZE_MAP[vmSize as keyof typeof VM_SIZE_MAP]}
                />
              ),
              status: <StatusIndicator icon={icon} text={t(text)} />,
              ipAddress: ipAddress || "--",
              serialNumber,
            };
          },
        ),
      };
    });

  const updatedNoApplianceCardData = {
    ...NO_APPLIANCE_CARD_DATA,
    readOnly,
    NoDataCardButton: AddApplianceClickHandler,
  };

  useSWR(
    endpointConditionHandler(
      callSyncAppliance,
      `${API_ENDPOINTS.ZUXP}/unified-locations/sync-location-appliances/${locationId}`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setCheckSyncStatus(true);
          setCallSyncAppliance(false);
          void syncApplianceStatus.mutate();
        } else {
          setCallSyncAppliance(false);
          setCheckSyncStatus(false);
          tableRef?.current?.refreshData();
        }
      },
      onError: () => {
        setCallSyncAppliance(false);
        setCheckSyncStatus(true);
      },
    },
  );

  const syncApplianceStatus = useSWR(
    endpointConditionHandler(
      checkSyncStatus,
      `${API_ENDPOINTS.ZUXP}/unified-locations/location-appliances/${locationId}/sync-status`,
    ),
    getReq,
    {
      onSuccess: (data) => {
        if (data !== "COMPLETED") {
          setTimeout(() => {
            void syncApplianceStatus.mutate();
          }, 3000);
        } else {
          setCheckSyncStatus(false);
          tableRef?.current?.refreshData();
        }
      },
      onError: () => {
        setCheckSyncStatus(false);
        setCallSyncAppliance(false);
      },
    },
  );

  const paginationData = useSWRMutation(
    "pagination appliances",
    paginationGetReq,
  );
  const fetchRows = (currentPage: number, pageSize: number) =>
    paginationData
      .trigger({
        EndpointUrl: LOCATIONS_ENDPOINTS.applianceList(applianceId!, {
          limit: pageSize,
          offset: currentPage,
          search: search,
          type: userRoleFilters?.[0]?.id ?? "",
          status: userStatusFilters?.[0]?.value ?? "",
        }),
      })
      .then((response: any) => {
        const { data, pagination } = response as ApplianceResponseBody;
        setHidePagination(!(data.length > 0));
        const transformedData = responseTransformer(
          data,
        ) as TableRowItemProps[];

        return {
          totalItems: pagination.totalCount,
          totalPages: pagination.totalPages,
          // if data is not there mock data will be shown.
          nestedItems: transformTableData(transformedData),
        };
      });

  if (!applianceCount) {
    return (
      <div
        className={`overflow-y-auto overflow-x-hidden h-full min-h-full pt-m`}
      >
        <NoDataCard {...updatedNoApplianceCardData} />
      </div>
    );
  }
  const handleApplianceSync = () => {
    setCallSyncAppliance(true);
  };

  const translatedColumnData = columnData;
  const translatedTableColumnData = translatedColumnData.map((tcd) => {
    if (typeof tcd.name === "string") {
      tcd.name = <Tooltip content={t(tcd.name)} />;
    }

    return tcd;
  });

  return (
    <div
      className="px-rem-80 flex flex-col w-full"
      data-testid="unified-locations-appliances-list"
    >
      <div className="flex justify-between w-full items-center mb-rem-120">
        <div className="flex gap-rem-120 items-center">
          <div className="flex gap-rem-120 items-center">
            <Zselect
              showSelectedOptions={false}
              idAttr={"id"}
              valueAttr={"name"}
              id="type"
              multiSelect={false}
              enableSelectAll={false}
              flip={false}
              enableCancel={false}
              options={typeOptions}
              overrideCollapsedView={(
                ...props: [boolean, FilterOptionsProps[]]
              ) => {
                const show = props?.[0];
                const selectedOption = props?.[1];

                const summaryText = selectedOption
                  .map((elem) => elem.name)
                  .join(", ");

                return (
                  <>
                    <span
                      className={cn(`text-semantic-surface-base-primary`, {
                        "summary-text": !show,
                      })}
                    >
                      {!!summaryText.length ? `${t("TYPE")} = ` : t("TYPE")}
                    </span>
                    {!!summaryText.length && (
                      <span
                        className={cn(`text-semantic-surface-base-primary`, {
                          "text-selected": !show,
                        })}
                      >
                        {summaryText}
                      </span>
                    )}
                  </>
                );
              }}
              preSelectedOptions={userRoleFilters}
              enableClearSelection={false}
              showClearSelection={false}
              isPill
              searchOptions={{ enable: false }}
              onSelectionChange={handleUserRoleFilter}
              customClass="fix-padding-initial-render typography-paragraph1 !font-normal !text-semantic-content-base-primary pill [&_span.summary-text]:!text-semantic-content-base-primary [&_i.fa-caret-down]:!text-semantic-content-interactive-primary-default"
              showDropdownIconVariation={false}
              onActiveShowValue={false}
              minWidth="150"
            />
          </div>
          <div className="flex gap-rem-120 items-center">
            <Zselect
              showSelectedOptions={false}
              idAttr={"id"}
              valueAttr={"name"}
              id="status"
              multiSelect={false}
              enableSelectAll={false}
              flip={false}
              enableCancel={false}
              options={statusOptions.slice(0, 2)}
              overrideCollapsedView={(
                ...props: [boolean, FilterOptionsProps[]]
              ) => {
                const show = props?.[0];
                const selectedOption = props?.[1];

                const summaryText = selectedOption
                  .map((elem) => elem.name)
                  .join(", ");

                return (
                  <>
                    <span
                      className={cn(`text-semantic-surface-base-primary`, {
                        "summary-text": !show,
                      })}
                    >
                      {!!summaryText.length ? `${t("STATUS")} = ` : t("STATUS")}
                    </span>
                    {!!summaryText.length && (
                      <span
                        className={cn(`text-semantic-surface-base-primary`, {
                          "text-selected": !show,
                        })}
                      >
                        {summaryText}
                      </span>
                    )}
                  </>
                );
              }}
              preSelectedOptions={userStatusFilters}
              enableClearSelection={false}
              showClearSelection={false}
              isPill
              searchOptions={{ enable: false }}
              onSelectionChange={handleUserStatusFilter}
              customClass="fix-padding-initial-render typography-paragraph1 !font-normal !text-semantic-content-base-primary pill [&_span.summary-text]:!text-semantic-content-base-primary [&_i.fa-caret-down]:!text-semantic-content-interactive-primary-default"
              showDropdownIconVariation={false}
              onActiveShowValue={false}
            />
          </div>
          {userRoleFilters.length > 0 || userStatusFilters.length > 0 ? (
            <div
              className="text-semantic-content-interactive-primary-default typography-paragraph1 cursor-pointer"
              onClick={() => handleResetFilter([])}
              onKeyDown={() => handleResetFilter([])}
              tabIndex={0}
              data-testid={getDataTestId("reset-filter", ID)}
              role="button"
            >
              <i
                aria-label={t("RESET_ICON")}
                className="fa-solid fa-rotate-left pr-rem-40"
              />
              {t("RESET")}
            </div>
          ) : (
            ""
          )}
        </div>
        <div className="flex flex-row items-center gap-rem-160">
          <Button
            id={getDataTestId("sync-refresh", ID)}
            data-testid={getDataTestId("sync-refresh", ID)}
            key={id}
            onClick={handleApplianceSync}
            disabled={callSyncAppliance || checkSyncStatus}
            suffixIcon={
              (callSyncAppliance || checkSyncStatus) && (
                <Spinner
                  size="lg"
                  id={ID}
                  iconClass="text-semantic-content-interactive-primary-disabled py-[11px]"
                />
              )
            }
            variant={"tertiary"}
          >
            {t("locations.appliance.sync-appliance")}
          </Button>
          <SearchBar
            onChange={handleFilteredResult}
            isOnlyExpanded={true}
            userSearchText=""
          />
          {!readOnly && ztwSuperAdmin && (
            <Button
              id="add-appliance"
              variant="primary"
              prefixIcon={<FontAwesomeIcon icon={faPlus} />}
              onClick={AddApplianceClickHandler}
            >
              {t(addButtonText)}
            </Button>
          )}
        </div>
      </div>
      <div className="flex max-h-[calc(100vh-200px)] overflow-hidden">
        <ZDataTable
          pagination={tablePaginationData}
          columns={translatedTableColumnData}
          id={"appliance-list"}
          noSelection={true}
          fetchRows={fetchRows}
          isHeaderGrey={true}
          shouldRerenderOnResize={true}
          loadingComponent={<TableLoader rows={7} />}
          showExpandCollapseAll={false}
          isExpandAll={true}
          onBulkSelect={onBulkSelect}
          onBulkUnselect={onBulkUnselect}
          onRowSelect={onRowSelect}
          onRowUnSelect={onRowUnSelect}
          isGrouped={true}
          hidePagination={hidePagination}
          ref={tableRef}
        />
      </div>
    </div>
  );
};

export default Appliances;
