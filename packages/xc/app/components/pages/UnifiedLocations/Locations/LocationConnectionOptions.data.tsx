import {
  type LocationConnectionOptionsProps,
  type OptionListProps,
} from "./types";

export const LOCATION_CONNECTION_OPTIONS_LIST_DATA: OptionListProps[] = [
  {
    list: [
      {
        label: "locations.connectionOptions.authentication",
        value: "Disabled",
      },
      {
        label: "locations.connectionOptions.caution-warning",
        value: "Enabled",
      },
      {
        label: "locations.connectionOptions.aup-warning",
        value: "Enabled: 1 day",
      },
    ],
  },

  {
    list: [
      {
        label: "locations.connectionOptions.firewall-control",
        value: "Enabled",
      },
      {
        label: "locations.connectionOptions.ips-control",
        value: "--",
      },
    ],
  },

  {
    list: [
      {
        label: "locations.connectionOptions.xff-forwarding",
        value: "Enabled",
      },
    ],
  },

  {
    list: [
      {
        label: "locations.connectionOptions.bandwidth-control",
        value: "Enabled",
      },
      {
        label: "locations.connectionOptions.download",
        value: "3,000",
      },
      {
        label: "locations.connectionOptions.upload",
        value: "3,000",
      },
    ],
  },
];

export const LOCATION_CONNECTION_OPTIONS_DATA: LocationConnectionOptionsProps =
  {
    pageTitle: "locations.connectionOptions.title",
    handleEditConnection: () => console.log("Edit Connection Option"),
    optionList: LOCATION_CONNECTION_OPTIONS_LIST_DATA,
    readOnly: false,
  };
