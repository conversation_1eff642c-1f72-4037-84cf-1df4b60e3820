import { type NoDataCardProps, type AppliancesProps } from "./types";

export const APPLIANCES_TABLE_DATA = {
  tableRowData: [
    {
      id: 1,
      name: "ord_dg1",
      meta: "Small Branch Templ",
      dataSet: [
        {
          id: 1.1,
          name: "ord_gw1",
          meta: null,
          type: "ZT-800",
          typeMeta: null,
          status: "active",
          ipAddress: "172",
          serialNumber: "",
          applianceGroupId: null,
          provTemplateName: "",
        },
        {
          id: 1.2,
          name: "ord_gw2",
          meta: null,
          type: "ZT-800",
          typeMeta: null,
          status: "active",
          ipAddress: "172",
          serialNumber: "",
          applianceGroupId: null,
          provTemplateName: "",
        },
      ],
    },
    {
      id: 2,
      name: "ord_dg2",
      meta: "Small Branch Templ",
      dataSet: [
        {
          id: 2.1,
          name: "ord_gw1",
          meta: null,
          type: "VMware ESXi",
          typeMeta: "Large VM",
          status: "active",
          ipAddress: "172",
          serialNumber: "",
          applianceGroupId: null,
          provTemplateName: "",
        },
        {
          id: 2.2,
          name: "ord_gw2",
          meta: null,
          type: "VMware ESXi",
          typeMeta: "Large VM",
          status: "inactive",
          ipAddress: "172",
          serialNumber: "",
          applianceGroupId: null,
          provTemplateName: "",
        },
      ],
    },
    {
      id: 3,
      name: "ord_dg3",
      meta: "Small Branch Templ",
      dataSet: [
        {
          id: 3.1,
          name: "ord_gw1",
          meta: "ala3-gw-1-tmpl",
          type: "ZT-800",
          typeMeta: "Large VM",
          status: "readyToDeploy",
          ipAddress: "172",
          serialNumber: "",
          applianceGroupId: null,
          provTemplateName: "",
        },
        {
          id: 3.2,
          name: "ord_gw2",
          meta: "ala3-gw-1-tmpl",
          type: "ZT-800",
          typeMeta: "Large VM",
          status: "notDeployed",
          ipAddress: "172",
          serialNumber: "",
          applianceGroupId: null,
          provTemplateName: "",
        },
      ],
    },
  ],
  tableColumnFn: () => [
    {
      id: "applianceGroup",
      name: "locations.appliance.table.branch-connector",
      isSortable: true,
      isHidden: false,
      width: "25%",
    },
    {
      id: "appliance",
      name: "locations.appliance.table.appliance",
      isSortable: true,
      isHidden: false,
      width: "30%",
    },
    {
      id: "type",
      name: "locations.appliance.table.type",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
    {
      id: "status",
      name: "locations.appliance.table.status",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
    {
      id: "ipAddress",
      name: "locations.appliance.table.ip-address",
      isSortable: true,
      isHidden: false,
      width: "15%",
    },
  ],
};

export const typeOptions = [
  {
    id: "ZT400",
    name: "ZT400",
  },
  {
    id: "ZT600",
    name: "ZT600",
  },
  {
    id: "ZT800",
    name: "ZT800",
  },
  {
    id: "REDHAT_LINUX",
    name: "RedHat Linux",
  },
  {
    id: "VMWARE_ESXI",
    name: "VMware ESXi",
  },
  {
    id: "MICROSOFT_HYPER_V",
    name: "Microsoft Hyper V",
  },
];

export const statusOptions = [
  {
    id: "active",
    name: "Active",
    value: "ACTIVE",
  },
  {
    id: "inactive",
    name: "Inactive",
    value: "INACTIVE",
  },
  {
    id: "unregistered",
    name: "Unregistered",
    value: "UNREGISTERED",
  },
];

export const fullStatusOptions = [
  {
    id: "active",
    name: "Active",
    value: "ACTIVE",
  },
  {
    id: "inactive",
    name: "Inactive",
    value: "INACTIVE",
  },
  {
    id: "unregistered",
    name: "Unregistered",
    value: "UNREGISTERED",
  },
];

export const fullProvStatusOptions = [
  {
    id: "deployed",
    name: "Deployed",
    value: "DEPLOYED",
  },
  {
    id: "ready-to-deploy",
    name: "Ready to Deploy",
    value: "NOT_DEPLOYED",
  },
  {
    id: "staged",
    name: "Staged",
    value: "STAGED",
  },
];

export const APPLIANCES_DATA: AppliancesProps = {
  id: "appliances",
  locationId: 12121,
  typeOptions: typeOptions,
  statusOptions: statusOptions,
  resetButtonText: "RESET",
  addButtonText: "ADD",
  tableRowData: APPLIANCES_TABLE_DATA.tableRowData,
  columnData: APPLIANCES_TABLE_DATA.tableColumnFn(),
  onBulkSelect: () => console.log("Bulk selected"),
  onBulkUnselect: () => console.log("Bulk Un select"),
  onRowSelect: () => console.log("Row select"),
  onRowUnSelect: () => console.log("Row Un select"),
  onAddOpen: () => console.log("Open Add Modal"),
  handleReset: () => console.log("Reset"),
  handleAdd: () => console.log("Add"),
  applianceCount: 0,
  readOnly: false,
  ztwSuperAdmin: false,
};

export const NO_APPLIANCE_CARD_DATA: NoDataCardProps = {
  name: "locations.appliance.no-data-name",
  NoDataCardButton: () => console.log("Add Appliance Clicked"),
  description: "locations.appliance.no-data-description",
  buttonText: "locations.appliance.no-data-buttonText",
  readOnly: false,
};
