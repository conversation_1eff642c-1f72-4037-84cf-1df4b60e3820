import { type LocationIPsecProps, type IPsecList } from "./types";

export const LOCATION_IPSEC_LIST_DATA: IPsecList[] = [
  {
    label: "locations.ipsec.static-ips",
    value: "--",
  },
  {
    label: "locations.ipsec.proxy-ports",
    value: "1642",
  },
  {
    label: "locations.ipsec.vpn-credentials",
    value: "<EMAIL>",
  },
  {
    label: "locations.ipsec.virtual-zens",
    value: "--",
  },
  {
    label: "locations.ipsec.virtual-clusters",
    value: "--",
  },
];

export const LOCATION_GRE_TUNNEL_DATA = {
  tableRowData: [
    {
      ipAddress: "*******",
      greEnabled: true,
      greTunnelIp: "*******",
      primaryGw: "*******",
      secondaryGw: "1*******",
      greRangePrimary: "*******-*******",
      greRangeSecondary: "*******-*******",
    },
  ],
  tableColumnFn: () => [
    {
      id: "serialNumber",
      name: "locations.ipsec.serial-number",
      isSortable: false,
      isHidden: false,
      width: "20%",
    },
    {
      id: "sourceIP",
      name: "locations.ipsec.tunnel-ip",
      isSortable: false,
      isHidden: false,
      width: "20%",
    },
    {
      id: "destinationIP",
      name: "locations.ipsec.destination-ip",
      isSortable: false,
      isHidden: false,
      width: "20%",
    },
    {
      id: "destinationRange",
      name: "locations.ipsec.destination-range",
      isSortable: true,
      isHidden: false,
      width: "60%",
    },
  ],
};

export const LOCATION_IPSEC_DATA: LocationIPsecProps = {
  pageTitle: "locations.ipsec.title",
  handleEditIPsec: () => console.log("Edit IPsec"),
  handleExport: () => console.log("Export GRE Tunnel Info"),
  IPsecList: LOCATION_IPSEC_LIST_DATA,
  tableRowData: LOCATION_GRE_TUNNEL_DATA.tableRowData,
  columnData: LOCATION_GRE_TUNNEL_DATA.tableColumnFn(),
  readOnly: false,
};
