import { Button, SimpleDropdownMenu } from "@zs-nimbus/core";
import { useTranslation } from "react-i18next";
import { type ButtonProps } from "../../Appliances/components/ApplianceList/ApplianceButtonGroup";
import {
  type SimpleDropdownMenuItemData,
  type AdminAccessTypes,
} from "../types";
import SegmentControl, {
  type SegmentControlOptionItemsProps,
} from "@/components/Analytics/SegmentedControl/SegmentedControl";
import SearchBar from "@/components/SearchBar/SearchBar";
import { getDataTestId } from "@/utils/utils";
import { Spinner } from "@/components/Spinner/Spinner";

type SegmentControlProps = {
  controls: SegmentControlOptionItemsProps[];
  value: string;
  onChange: (val: string) => void;
};

// type FilterProps = {};

type SearchProps = {
  text: string;
  onChange: (val: string) => void;
};

type LocationFilterBarProps = {
  segmentControls: SegmentControlProps;
  // filters: FilterProps;
  search: SearchProps;
  handleOn: () => void;
  buttonConfig: ButtonProps;
  adminPermission: AdminAccessTypes;
};

const LocationTableControls = ({
  segmentControls,
  // filters,
  search,
  handleOn,
  buttonConfig,
  adminPermission,
}: LocationFilterBarProps) => {
  // testing
  const { text, type, id, onClick, loading } = buttonConfig;
  const ID = getDataTestId("button-group", id);
  const { t } = useTranslation();
  const handleOnClick = () => {
    handleOn();
  };
  const { ziaSuperAdmin, ztwSuperAdmin } = adminPermission;

  const addLocationsOptions: SimpleDropdownMenuItemData[] = [
    ziaSuperAdmin
      ? {
          id: "ipsec-location",
          name: t("locations.list.add-ipsec-location"),
        }
      : null,
    ztwSuperAdmin
      ? {
          id: "edge-location",
          name: t("locations.list.add-edge-location"),
        }
      : null,
    ztwSuperAdmin
      ? {
          id: "cloud-location",
          name: t("locations.list.add-cloud-location"),
        }
      : null,
  ].filter((item): item is SimpleDropdownMenuItemData => item !== null);

  return (
    <div className="flex justify-between">
      <div className="flex items-center gap-rem-160">
        <SegmentControl
          customClass="flex h-full !bg-semantic-background-primary border border-semantic-border-base-primary"
          customSelectionStyle="bg-semantic-surface-interactive-primary-default text-semantic-content-inverted-base-primary"
          customSelectedLabelStyling={"text-semantic-content-immutable-white"}
          options={segmentControls.controls}
          value={segmentControls.value}
          onChange={segmentControls.onChange}
          id="location-table"
        />
      </div>
      <div className="flex gap-2">
        <Button
          id={getDataTestId("sync-location", ID)}
          data-testid={getDataTestId("sync-location", ID)}
          key={text}
          onClick={onClick}
          variant={type}
          disabled={loading}
          suffixIcon={
            loading && (
              <Spinner
                size="lg"
                id={ID}
                iconClass="text-semantic-content-interactive-primary-disabled py-[11px]"
              />
            )
          }
        >
          {t(text)}
        </Button>
        <SearchBar
          userSearchText={search.text}
          onChange={search.onChange}
          isOnlyExpanded={true}
          id={"location-table"}
        />

        {(ziaSuperAdmin || ztwSuperAdmin) && (
          <SimpleDropdownMenu
            handler={{
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              suffixIcon: <i className="fa-solid fa-caret-down" />,
              label: "Add",
              variant: "primary",
            }}
            id="location-add-Button"
            items={addLocationsOptions}
            onClick={(item) => {
              if (item?.id === "cloud-location") {
                window.location.href =
                  "/ec/administration/provisioning-templates?filter=CC";
              }
              if (item?.id === "edge-location") {
                window.location.href =
                  "/ec/administration/branch-provisioning-templates?filter=BC";
              }
              if (item?.id === "ipsec-location") {
                handleOnClick();
              }
            }}
            placement="bottom start"
            trigger="press"
          />
        )}
      </div>
    </div>
  );
};

export default LocationTableControls;
