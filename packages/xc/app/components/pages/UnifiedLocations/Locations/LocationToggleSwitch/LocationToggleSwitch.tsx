import { ZToggleSwitch } from "@xc/legacy-components";
import { useTranslation } from "react-i18next";
import { type LocationToggleSwitchProps } from "../types";

const LocationToggleSwitch = ({
  locationSwitch,
  disabled,
}: LocationToggleSwitchProps) => {
  const { t } = useTranslation();

  return (
    <>
      {locationSwitch.map(({ id, isEnabled, desc, handleToggle }, index) => (
        <div
          key={index}
          className={`flex items-center ${locationSwitch?.length >= 2 && "pb-rem-160"}`}
          data-testid={`unified-locations-toggle-${id}-${index}`}
        >
          <ZToggleSwitch
            id={id}
            checked={isEnabled}
            small
            disabled={
              (id === "enableTroubleShoot" && locationSwitch[1].isEnabled) ||
              disabled
            }
            type="secondary"
            onChange={(value: boolean) => {
              handleToggle(value, id);
            }}
          />
          <div
            className="typography-paragraph1-strong text-semantic-content-base-primary pl-rem-80"
            aria-label={t(desc)}
            data-testid={`unified-locations-desc-${id}-${index}`}
          >
            {t(desc)}
          </div>
        </div>
      ))}
    </>
  );
};

export default LocationToggleSwitch;
