import { useState } from "react";
import { notFound } from "next/navigation";
import { DateFilterContainer } from "../../Filters/DateFilterContainer/DateFilterContainer";
import { type PillFilterConfigKeys } from "../../Filters/ModalPillGroupContainer/types";
import { ModalPillGroupContainer } from "../../Filters/ModalPillGroupContainer/ModalPillGroupContainer";
import { GridContent } from "@/components/Analytics/GridUtils";
import { PageHeader } from "@/components/Analytics/PageUtils/PageHeader";
import useAnalyticsDateFilterSetup from "@/hooks/useAnalyticsDateFilterSetup";
import { UsersMostIncidentsContainer } from "@/components/Analytics/DataProtection/Dashboard/UsersMostIncidents/UsersMostIncidentsContainer";
import { TotalIncidentsContainer } from "@/components/Analytics/DataProtection/Dashboard/TotalIncidents/TotalIncidentsContainer";
import AllDataChannelsContainer from "@/components/Analytics/DataProtection/Dashboard/AllDataChannels/AllDataChannelsContainer";
import TopSensitiveDataRestContainer from "@/components/Analytics/DataProtection/Dashboard/TopSensitiveDataRest/TopSensitiveDataRestContainer";
import { useFlags } from "@/context/FeatureFlags";

export default function DataProtection({
  id = "data-protection",
}: {
  id?: string;
}) {
  const { can } = useFlags();
  const featureFlag = can("showDataProtection");
  if (!featureFlag) {
    notFound();
  }
  const [isMockData] = useState(true);
  const ID = id;
  const activeMenu = "data-protection";
  const loading = useAnalyticsDateFilterSetup(activeMenu);

  return (
    <>
      {!loading && (
        <GridContent id={ID}>
          <div className="flex justify-between">
            <PageHeader header="PAGE_TITLE_DATA_PROTECTION" id={ID} />
            <DateFilterContainer activeMenu={activeMenu} id={ID} />
          </div>
          <ModalPillGroupContainer
            id={ID}
            filterName={activeMenu as PillFilterConfigKeys}
          />
          <div className="flex gap-rem-160 w-full">
            <div className="flex gap-rem-160 w-full">
              <div className="min-w-[50%] flex">
                <TotalIncidentsContainer isMockData={isMockData} />
              </div>
              <div className="min-w-[50%]">
                <UsersMostIncidentsContainer isMockData={isMockData} />
              </div>
            </div>
            <div className="flex w-[20%] ml-[16px]">
              <AllDataChannelsContainer isMockData={isMockData} />
            </div>
          </div>
          <TopSensitiveDataRestContainer isMockData={isMockData} />
        </GridContent>
      )}
    </>
  );
}
