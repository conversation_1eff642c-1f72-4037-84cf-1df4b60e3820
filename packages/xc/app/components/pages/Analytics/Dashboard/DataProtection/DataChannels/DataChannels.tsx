import { useEffect, useState } from "react";
import { notFound, useSearchParams } from "next/navigation";
import useAnalyticsDateFilterSetup from "@/hooks/useAnalyticsDateFilterSetup";
import FilesContainer from "@/components/Analytics/DataProtection/DataChannels/Files/FilesContainer";
import { GridContent } from "@/components/Analytics/GridUtils";
import EndpointsContainer from "@/components/Analytics/DataProtection/DataChannels/Files/Endpoints/EndpointsContainer";
import EmailContainer from "@/components/Analytics/DataProtection/DataChannels/Files/Email/EmailContainer";
import SaasSecurityContainer from "@/components/Analytics/DataProtection/DataChannels/Files/SaaSSecurity/SaasSecurityContainer";
import InlineContainer from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/InlineContainer";
import { useFlags } from "@/context/FeatureFlags";

export default function DataChannels() {
  const { can } = useFlags();
  const featureFlag = can("showDataProtection");
  if (!featureFlag) {
    notFound();
  }
  const [isMockData] = useState(true);
  const ID = "data-channels";
  const activeMenu = "data-protection";
  const loading = useAnalyticsDateFilterSetup(activeMenu);

  const [activeFileType, setActiveFileType] = useState<number>(0);
  const queryParams = useSearchParams();
  const tab = queryParams?.get("tab") ?? "inline";

  const dataChannelsMapping: Record<string, number> = {
    inline: 0,
    "saas-security": 1,
    endpoints: 2,
    email: 3,
  };
  useEffect(() => {
    const selectedTab = dataChannelsMapping[tab];
    setActiveFileType(selectedTab);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tab]);

  return (
    <>
      {!loading && (
        <GridContent id={ID}>
          <FilesContainer
            id={ID}
            activeFileType={activeFileType}
            isMockData={isMockData}
          />
          {activeFileType === 0 && <InlineContainer isMockData={isMockData} />}
          {activeFileType === 1 && (
            <SaasSecurityContainer isMockData={isMockData} />
          )}
          {activeFileType === 2 && (
            <EndpointsContainer isMockData={isMockData} />
          )}
          {activeFileType === 3 && <EmailContainer isMockData={isMockData} />}
        </GridContent>
      )}
    </>
  );
}
