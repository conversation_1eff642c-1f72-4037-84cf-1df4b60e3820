/* eslint-disable @up/unified-platform/max-file-lines */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { CyberSecurityView as component } from "./CyberSecurityView";
import { type HorizontalBarChartProps } from "@/components/Analytics/Charts/HorizontalBarChart/types";
import { type SankeyProps } from "@/components/Analytics/Charts/Sankey/types";
import { type BarLineProps } from "@/components/Analytics/Charts/BarLineChart/types";
import { type MultiLineChartProps } from "@/components/Analytics/Charts/MultiLineChart/types";
import { type TrendingNumberProps } from "@/components/TrendingNumber/TrendingNumber";
import { type TopCountriesWithThreatsProps } from "@/components/Analytics/CyberSecurity/Dashboard/TopCountriesWithThreats/types";
import { type SegmentControlOptionsProps } from "@/components/Analytics/CyberSecurity/Dashboard/YourCybersecurityTransactions/types";

const meta = {
  title: "pages/Analytics/Dashboard/CyberSecurity/CyberSecurity",
  component,
  parameters: {
    layout: "fullscreen",
    design: {
      type: "figma",
      url: "https://www.figma.com/proto/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?page-id=2434%3A42955&type=design&node-id=2434-55252&viewport=311%2C-3195%2C0.25&t=W4fv2gWumOXNxlWh-1&scaling=scale-down&starting-point-node-id=2434%3A55252",
    },
  },
} satisfies Meta<typeof component>;

export default meta;

const AdvancedThreatsCategoriesData: HorizontalBarChartProps = {
  data: [
    {
      isNameL10nTag: true,
      name: "SUSPICIOUS_DEST",
      total: 39,
    },
    {
      isNameL10nTag: true,
      name: "PHISHING",
      total: 14,
    },
    {
      isNameL10nTag: true,
      name: "MALWARE_SITE",
      total: 12,
    },
    {
      isNameL10nTag: true,
      name: "VIRUS",
      total: 5,
    },
    {
      isNameL10nTag: true,
      name: "BOTNET",
      total: 2,
    },
  ],
  axisKeys: ["name", "total"],
  config: {
    containerClass: "flex h-[300px]",
    columnProps: {
      height: 30,
    },
    axisProps: {
      yAxisProps: { minGridDistance: 1 },
      xAxisProps: { minGridDistance: 70 },
    },
  },
};

const SankeyData: SankeyProps = {
  data: {
    series: [
      { from: "A", to: "B", value: 12 },
      { from: "A", to: "C", value: 12 },
      { from: "B", to: "D", value: 6 },
      { from: "B", to: "E", value: 6 },
      { from: "D", to: "F", value: 3 },
      { from: "D", to: "G", value: 2 },
      { from: "D", to: "H", value: 1 },
    ],
    nodes: [
      {
        id: "A",
        name: "Transactions with Files",
        fill: "#2160E1",
        value: 12,
      },
      { id: "B", name: "Sandbox Required", fill: "#F19325", value: 12 },
      {
        id: "C",
        name: "No Sandbox Detonation Required",
        fill: "#F19325",
        value: 6,
      },
      { id: "D", name: "Malicious", fill: "#DC362E", value: 6 },
      { id: "E", name: "Not Malicious", fill: "#DC362E", value: 4 },
      { id: "F", name: "Allowed", fill: "#8B1A4B", value: 4 },
      { id: "G", name: "Blocked", fill: "#3DA592", value: 2 },
      { id: "H", name: "Isolated", fill: "#3DA592", value: 1 },
    ],
  },
  configs: {
    containerClass: "flex h-[300px]",
    sankey: {
      sourceIdField: "from",
      targetIdField: "to",
      valueField: "value",
      paddingRight: 50,
      nodeWidth: 12,
      nodePadding: 35,
      nodeAlign: "left",
      nodeSort: () => null,
      linkTension: 0,
    },
    nodes: {
      nameField: "name",
      toggleKey: "none",
      interactiveChildren: false,
    },
    links: {
      tooltipText: "",
      fillOpacity: 0.25,
      controlPointDistance: 0.4,
    },
    label: {
      fontSize: 12,
      maxWidth: 100,
      fontWeight: "500",
      oversizedBehavior: "wrap",
      y: 0,
      centerY: 0,
      paddingTop: 4,
      pointeroverText: "{name} ({value})",
      pointeroutText: "{name}",
    },
  },
};

const YourSSLInspectionReviewData: BarLineProps = {
  data: [
    {
      category: "Total Traffic",
      yes: 389800,
    },
    {
      category: "Encrypted (SSL)",
      yes: 269900,
    },
    {
      category: "Qualified",
      yes: 206500,
    },
    {
      category: "Inspected",
      yes: 68900,
      no: 150000,
    },
  ],
  configs: {
    containerClass: "flex h-[350px]",
    tooltip: {
      getFillFromSprite: false,
      autoTextColor: false,
      paddingBottom: 0,
      paddingLeft: 0,
      paddingRight: 0,
      paddingTop: 0,
    },
    tooltipBackground: {
      fill: "#FFFFFF",
      fillOpacity: 1,
      shadowColor: "#191919",
      shadowBlur: 20,
      strokeWidth: 8,
      stroke: "#FFFFFF",
      strokeOpacity: 1,
    },
    xAxisSettings: {
      categoryField: "category",
      startLocation: 0,
      endLocation: 1,
    },
    xAxisTemplateSettings: {
      fill: "#191919",
      fontSize: 14,
      fontWeight: "400",
    },
    yAxisSettings: {
      min: 0,
      numberFormat: "#0a",
      minGridDistance: 50,
    },
    yAxisTemplateSettings: {
      fill: "#767676",
      fontSize: 12,
      fontWeight: "400",
    },
    yAxisLabelSettings: {
      rotation: -90,
      text: "Total Traffic",
      fill: "#767676",
      fontSize: 12,
      fontWeight: "400",
      y: 50,
      centerX: 50,
    },
    lineSeriesSettings: {
      valueYField: "yes",
      categoryXField: "category",
      stroke: "#FFFFFF",
    },
    lineSeriesStrokeSettings: {
      shadowBlur: 2,
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      shadowColor: "#2160E1",
      shadowOpacity: 0.1,
    },
    lineSeriesTemplateSettings: {
      fillOpacity: 1,
      visible: true,
      fillGradient: ["#DDEAFF", "#FFFFFF"],
    },
    columnSeriesSettings: {
      valueYField: "yes",
      categoryXField: "category",
      stacked: true,
    },
    columnSeriesTemplateSettings: {
      width: 38,
      fillGradient: ["#4B7EEC", "#194CBB"],
    },
    stackedColumnSeriesSettings: {
      valueYField: "no",
      categoryXField: "category",
      stacked: true,
    },
    stackedColumnSeriesTemplateSettings: {
      width: 38,
      fill: "#072F84",
      tooltipY: 0,
      tooltipHTML: `<div class="flex flex-col bg-semantic-brand-white px-[20px] py-[8px]">
            <div class="font-medium text-[13px] text-grey-900 mb-[4px] leading-5">SSL/TLS inspection</div>
            <div class="flex font-normal text-[13px] justify-between leading-5">
              <span class="text-grey-900">yes</span>
              <span class="text-grey-900">{yes}</span>
            </div>
            <div class="flex font-normal text-[13px] justify-between leading-5">
              <span class="text-grey-900">no</span>
              <span class="text-grey-900">{no}</span>
            </div>
            <div class="flex font-normal text-[13px] leading-5">
              <span class="text-grey-500">Very Little traffic inspected</span>
            </div>
        </div>`,
    },
  },
};

const MaliciousMultilineChartData: MultiLineChartProps = {
  data: [
    {
      meta: {
        lineColor: "rgba(25, 76, 187, 1)",
        name: "Malicious",
      },
      data: [
        {
          date: "Jan 1",
          value: 0.03,
        },
        {
          date: "Jan 2",
          value: 0.034,
        },
        {
          date: "Jan 3",
          value: 0.0318,
        },
        {
          date: "Jan 4",
          value: 0.038,
        },
        {
          date: "Jan 5",
          value: 0.0336,
        },
        {
          date: "Jan 6",
          value: 0.039,
        },
        {
          date: "Jan 7",
          value: 0.04,
        },
        {
          date: "Jan 14",
          value: 0.05,
        },
        {
          date: "Jan 21",
          value: 0.036,
        },
        {
          date: "Jan 28",
          value: 0.042,
        },
      ],
    },
  ],
  axisKeys: ["date", "value"],
  config: {
    containerClass: "h-[268px] w-full",
    axisProps: {
      xAxisProps: { minorGridEnabled: true },
      xGridProps: {
        visible: false,
      },
      yAxisProps: {
        visible: false,
      },
      yLabelProps: {
        showCustomLabel: true,
        labelFormatter: (text) => {
          if (text === "0") return "0";

          return text + "%";
        },
      },
      yAxisSetting: {
        min: 0,
        max: 0.08,
      },
    },
    tooltipProps: {
      tooltipSetting: {
        labelHTML: `<div style='flex flex-col'>
          <div class="text-sm font-bold">{date}</div>
          <ul class="list-disc" >
            <li>You : {value}</li>
          </ul>
          </div>`,
        pointerOrientation: "left",
      },
    },
    cursorProps: {},
  },
};

const TrendConfigMalicious: TrendingNumberProps = {
  number: 10,
  direction: "Up",
};

const MaliciousTransactionsData = {
  titleConfig: {
    title: "Your Cybersecurity Transactions from Last 30 Days",
    subTitle:
      "Malicious transactions are threats that have been detected and blocked by Zscaler.",
  },
  totalCountTitle: "Total Threats Blocked",
  totalCount: "210k",
  trendConfig: TrendConfigMalicious,
  chartData: MaliciousMultilineChartData,
};

const PolicyBlockMultiLineChartData: MultiLineChartProps = {
  data: [
    {
      meta: {
        lineColor: "rgba(25, 76, 187, 1)",
        name: "Policy blocks",
      },
      data: [
        {
          date: "Jan 1",
          value: 0.03,
        },
        {
          date: "Jan 2",
          value: 0.034,
        },
        {
          date: "Jan 3",
          value: 0.0318,
        },
        {
          date: "Jan 4",
          value: 0.038,
        },
        {
          date: "Jan 5",
          value: 0.0336,
        },
        {
          date: "Jan 6",
          value: 0.039,
        },
        {
          date: "Jan 7",
          value: 0.035,
        },
        {
          date: "Jan 14",
          value: 0.03,
        },
        {
          date: "Jan 21",
          value: 0.042,
        },
        {
          date: "Jan 28",
          value: 0.05,
        },
      ],
    },
  ],
  axisKeys: ["date", "value"],
  config: {
    containerClass: "h-[268px] w-full",
    axisProps: {
      xAxisProps: { minorGridEnabled: true },
      xGridProps: {
        visible: false,
      },
      yAxisProps: {
        visible: false,
      },
      yLabelProps: {
        visible: true,
        labelFormatter: (text) => {
          if (text === "0") return "0";

          return text + "%";
        },
        fontSize: "10px",
      },
      yAxisSetting: {
        min: 0,
        max: 0.1,
      },
    },
    tooltipProps: {
      tooltipSetting: {
        labelHTML: `<div>
          <div>{date}</div>
          <ul class="list-disc mt-rem-160">
            <li>You : {value}</li>
          </ul>
          </div>`,
        pointerOrientation: "left",
      },
    },
    cursorProps: {},
  },
};

const TrendConfigPolicyBlocks: TrendingNumberProps = {
  number: 10,
  direction: "Up",
};

const PolicyBlocksData = {
  titleConfig: {
    title: "Your Cybersecurity Transactions",
    subTitle:
      "Blocks that are caused by policies you have configured to limit exposure to certain types of web content.",
  },
  totalCountTitle: "Total Policy Blocks",
  totalCount: "25.3M",
  trendConfig: TrendConfigPolicyBlocks,
  chartData: PolicyBlockMultiLineChartData,
};

const SegmentOptions = [
  {
    label: "Malicious Transactions",
    value: "malicious-transactions",
  },
  {
    label: "Policy Blocks",
    value: "policy-blocks",
  },
] as SegmentControlOptionsProps[];

const countries = [
  {
    id: "US",
    name: "United States",
    value: 59800,
  },
  {
    id: "CA",
    name: "Canada",
    value: 39900,
  },
  {
    id: "SP",
    name: "Spain",
    value: 31400,
  },
  {
    id: "RU",
    name: "Russia",
    value: 14300,
  },
  {
    id: "IN",
    name: "India",
    value: 11400,
  },
  {
    id: "AE",
    name: "United Arab Emirates",
    value: 74000,
  },
  {
    id: "GB",
    name: "United Kingdom",
    value: 21000,
  },
];

const TopCountriesWithThreatsData: TopCountriesWithThreatsProps = {
  heading: "TOP_COUNTRIES_WITH_THREATS",
  subHeading: "THESE_ARE_THE_TOP_COUNTRIES_WHERE_THREATS_ARE_ORIGINATING_FROM",
  countryPanelProps: {
    countryData: countries,
    totalThreatsText: "Total Threats",
  },
  mapProps: {
    data: countries,
    dataKey: "value",
    customConfig: {
      valuedBubbleMapProps: {
        tooltipSettingsProps: {
          pointerOrientation: "vertical",
          getFillFromSprite: false,
          getStrokeFromSprite: false,
          autoTextColor: false,
          getLabelFillFromSprite: false,
        },
        bubbleLabelProps: {
          fontSize: 10,
        },
        bubbleSeriesProps: {
          min: 12,
          max: 20,
        },
        containerClass: "flex h-[350px]",
        tooltipHTML: `<div class="px-xs">
                  <div class="font-bold text-[14px] text-grey-900 mb-[8px]">{name}</div>
                  <div class="flex justify-between font-normal text-[13px]">
                  <span class="text-grey-900 pr-default">Total Threats</span>
                  <span class="text-grey-500">{value.formatNumber('#.#a')}</span>
                  </div>
                </div>`,
        bubbleLabelFormatter: function (val: number): string {
          const isFloatingPoint = val % 1 !== 0;
          const formattedValue = Number(val) / 1000;

          return isFloatingPoint
            ? formattedValue.toFixed(2)
            : formattedValue.toString();
        },
      },
    },
  },
};

export const CyberSecurity: StoryObj<typeof meta> = {
  args: {
    yourCybersecurityTransactions: {
      maliciousTransactionsData: MaliciousTransactionsData,
      policyBlocksData: PolicyBlocksData,
      segmentOptions: SegmentOptions,
    },
    advancedThreatsCategories: { chartData: AdvancedThreatsCategoriesData },
    sandboxThreats: {
      chartData: SankeyData,
      cardConfig: {
        cardTitle: "SANDBOX_THREATS",
        cardDescription: "SANDBOX_THREATS_CARD_DESC",
        cardFooterTitle: "SANDBOX_THREATS_FOOTER_TITLE",
      },
    },
    topCountriesWithThreats: TopCountriesWithThreatsData,
    yourSSLInspectionReview: { chartData: YourSSLInspectionReviewData },
  },
};
