import { DateFilterContainer } from "../Filters/DateFilterContainer/DateFilterContainer";

import { GridColumn, GridContent } from "@/components/Analytics/GridUtils";
import {
  ConnectivityCenterContainer,
  DevicesDiscoveredContainer,
  PrimaryTrafficSourcesContainer,
  TrafficInMyOrganization,
  TrafficDistributionContainer,
  type TrafficType,
} from "@/components/Analytics/Networking";
import { PageHeader } from "@/components/Analytics/PageUtils/PageHeader";
import {
  INTERNET,
  NETWORKING,
  NETWORKING_PRIVATE,
} from "@/configs/constants/analytics";
import useAnalyticsDateFilterSetup from "@/hooks/useAnalyticsDateFilterSetup";
import { isZPAEntitledWithoutZIA } from "@/utils/productAccessUtils";

const Networking = () => {
  const ID = "networking";
  const activeMenu = NETWORKING;
  const loading = useAnalyticsDateFilterSetup(activeMenu);

  const trafficType: TrafficType = isZPAEntitledWithoutZIA()
    ? NETWORKING_PRIVATE
    : INTERNET;

  return (
    <>
      {!loading && (
        <GridContent id={ID}>
          <div className="flex justify-between">
            <PageHeader header="PAGE_TITLE_NETWORKING" id={ID} />
            <DateFilterContainer activeMenu={activeMenu} id={ID} />
          </div>
          <TrafficInMyOrganization id={ID} selectedTab={trafficType} />
          <GridColumn size={2} id={ID}>
            <TrafficDistributionContainer id={ID} />
            <PrimaryTrafficSourcesContainer id={ID} />
          </GridColumn>
          <ConnectivityCenterContainer id={ID} />
          <DevicesDiscoveredContainer id={ID} />
        </GridContent>
      )}
    </>
  );
};

export default Networking;
