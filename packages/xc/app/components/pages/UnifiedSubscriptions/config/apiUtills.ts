import {
  API_ENDPOINTS,
  type ResData,
  buildQueryString,
  getBaseUrl,
  getHeaders,
  responseHandler,
} from "@/utils/apiHelper";
import { type FetchParams } from "@/configs/urls/onboarding/types";

export const SUBSCRIPTIONS_DEFAULT_PARAMS = {
  fetchSubscriptions: {
    query: "",
  },
};

export const paginationGetReq = (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  key: string,
  {
    arg,
  }: {
    arg: {
      EndpointUrl: string;
    };
  },
): ResData =>
  fetch(getBaseUrl(API_ENDPOINTS.ZIA) + arg.EndpointUrl, {
    method: "GET",
    headers: getHeaders(API_ENDPOINTS.ZIA),
  }).then(responseHandler);

export const SUBSCRIPTION_ENDPOINTS = {
  fetchsubscriptions: (params: FetchParams) =>
    `${API_ENDPOINTS.ZIA}/subscriptions?${buildQueryString({ ...SUBSCRIPTIONS_DEFAULT_PARAMS.fetchSubscriptions, ...params })}`,
  subscriptionsList: `${API_ENDPOINTS.ZIA}/subscriptions`,
};
