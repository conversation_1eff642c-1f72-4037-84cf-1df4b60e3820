"use client";

import { ZDataTable } from "@xc/legacy-components";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { debounce } from "lodash";
import useSWR from "swr";
import { LoadingSpinner } from "@zs-nimbus/core";
import useSWRMutation from "swr/mutation";
import {
  SUBSCRIPTION_ENDPOINTS,
  paginationGetReq,
} from "../../config/apiUtills";
import {
  type SubscriptionTableColumnItemProps,
  type SubscriptionRowProps,
} from "../../types";
import CustomizeColumns from "../components/CustomizeColumns/CustomizeColumns";
import Tooltip from "../components/Tootip/Tooltip";
import responseTransformer, { tableColumnFn } from "./SubscriptionTableconfig";
import SubscriptionTableControls from "./SubscriptionTableControls";
import TableLoader from "@/components/TableLoader/TableLoader";
import SearchBar from "@/components/SearchBar/SearchBar";
import { useFlags } from "@/context/FeatureFlags";
import { getReq } from "@/utils/apiHelper";

const SubscriptionList = () => {
  const [tableData, setTableData] = useState<SubscriptionRowProps[]>([]);
  const [search, setSearch] = useState("");
  const tableRef = useRef<{ refreshData: () => void }>(null);
  const searchRef = useRef<string>("");
  const [showCustomizeColumn, setShowCustomizeColumn] =
    useState<boolean>(false);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [filteredTableColumns, setFilteredTableColumns] = useState<
    SubscriptionTableColumnItemProps[]
  >([]);

  const [updatedCustomisedColumns, setUpdatedCustomisedColumns] = useState<
    SubscriptionTableColumnItemProps[]
  >([]);
  const { t } = useTranslation();
  const { can } = useFlags();

  const debouncedFetch = useRef(
    debounce(() => {
      tableRef?.current?.refreshData();
    }, 300),
  ).current;

  // Handle input changes and trigger debounce
  const handleFilteredResult = (value: string) => {
    const trimmedValue = value.trim();
    setSearch(trimmedValue);
    searchRef.current = trimmedValue;
    debouncedFetch();
  };

  const filteredColumns = (
    data: SubscriptionTableColumnItemProps[],
  ): SubscriptionTableColumnItemProps[] =>
    data?.filter(
      (col: SubscriptionTableColumnItemProps) =>
        !selectedColumns.includes(col?.id as string),
    );

  const translatedTableColumnData = filteredColumns(filteredTableColumns)?.map(
    (tcd: SubscriptionTableColumnItemProps) => {
      if (typeof tcd.name === "string") {
        tcd.name = <Tooltip content={t(tcd.name)} />;
      }

      return tcd;
    },
  );

  const recalculateWidth = (
    data: SubscriptionTableColumnItemProps[],
  ): SubscriptionTableColumnItemProps[] => {
    const visibleColumns = data.filter((col) => !col.isHidden);
    const totalWidth = 100;
    const newWidth = `${Math.round(totalWidth / visibleColumns?.length)}%`;

    return data?.map((col) => ({
      ...col,
      width: !col.isHidden ? (newWidth === "33%" ? "34%" : newWidth) : "10%",
    }));
  };

  const subscriptionListData = useSWR<SubscriptionRowProps[]>(
    SUBSCRIPTION_ENDPOINTS.subscriptionsList,
    getReq,
  );

  const getUpdatedColumns = () =>
    tableColumnFn()?.filter(
      (item) => !selectedColumns?.includes(item?.id?.toString()),
    );

  useEffect(() => {
    const filteredColumns = getUpdatedColumns();
    setFilteredTableColumns(filteredColumns);
    if (selectedColumns?.length) {
      const updatedColumns = recalculateWidth(filteredColumns);
      setFilteredTableColumns(updatedColumns);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedColumns]);

  const { isLoading } = subscriptionListData;
  const paginationData = useSWRMutation(
    "paginationSubscription",
    paginationGetReq,
  );
  useEffect(() => {
    if (search.trim() === "") {
      tableRef?.current?.refreshData(); // Trigger table data refresh
    }
  }, [search]);

  useEffect(() => {
    setUpdatedCustomisedColumns(translatedTableColumnData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableData]);
  if (!can("showUnifiedSubscriptions")) {
    window.location.replace("/error");
  }

  if (isLoading) {
    return (
      <div className="flex justify-center h-full items-center">
        <LoadingSpinner ariaLabel={t("LOADER_ICON")} />
      </div>
    );
  }

  const fetchRows = () => {
    const query = searchRef.current;

    return paginationData
      .trigger({
        EndpointUrl: SUBSCRIPTION_ENDPOINTS.fetchsubscriptions({
          query: query.length > 3 ? query : "",
        }),
      })
      .then((response) => {
        const data = response as SubscriptionRowProps[];
        setTableData(data);
        const transformedItems = responseTransformer(data);

        return {
          items: transformedItems,
        };
      })
      .catch((err) => console.error("Error in fetchRows: ", err));
  };

  return (
    <div
      className="p-rem-160 flex flex-col gap-rem-80"
      data-testid="unified-subscriptions-list"
    >
      <div className="typography-header3 text-semantic-content-base-primary">
        {t("subscriptions.title")}
      </div>
      <div className="flex justify-between w-full items-center mb-rem-120">
        <SubscriptionTableControls />
        <SearchBar
          id={"subscription-list"}
          onChange={handleFilteredResult}
          isOnlyExpanded={true}
          userSearchText={search}
        />
      </div>
      <div className="flex max-h-[calc(100vh-200px)] overflow-hidden relative">
        <ZDataTable
          id={"subscription-list"}
          columns={translatedTableColumnData}
          noSelection
          items={responseTransformer(tableData)}
          isHeaderGrey={true}
          onSortChange={(value: string) => console.log(value)}
          ref={tableRef}
          fetchRows={fetchRows}
          sorting={{
            enabled: true,
          }}
          shouldRerenderOnResize={true}
          loadingComponent={<TableLoader rows={50} />}
        />
        <div className="absolute z-40 right-rem-80 top-rem-140">
          <i
            aria-label={t("ACTION_ICON")}
            role="button"
            tabIndex={0}
            className="fa-regular fa-gear text-semantic-brand-default w-rem-200 h-rem-200"
            onClick={() => setShowCustomizeColumn(!showCustomizeColumn)}
            onKeyDown={() => setShowCustomizeColumn(!showCustomizeColumn)}
          />
        </div>
        {showCustomizeColumn && (
          <div className="absolute rounded-80 shadow-lg top-[35px] right-[35px] z-50 !bg-semantic-content-inverted-base-primary">
            <CustomizeColumns
              data={updatedCustomisedColumns}
              selectedColumns={selectedColumns}
              setSelectedColumns={setSelectedColumns}
              setShowCustomizeColumn={setShowCustomizeColumn}
              setFilteredTableColumns={setFilteredTableColumns}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionList;
