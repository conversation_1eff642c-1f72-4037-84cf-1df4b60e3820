import { t } from "i18next";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCircleCheck,
  faCircleExclamation,
  faCircleX,
} from "@fortawesome/pro-solid-svg-icons";
import { type PolicyURLProps } from "./types";
import ONBOARDING_PATHS from "@/configs/locales/OnboardingPaths";
import { WIZARD_DATA } from "@/components/PolicyWizard/PolicyWizard.data";

export const POLICY_URL_DATA: PolicyURLProps = {
  itemLabels: {
    INFORMATION_TECHNOLOGY: "Information Technology",
    ADULT_MATERIAL: "Adult Material",
    BUSINESS_AND_ECONOMY: "Business and Economy",
    DRUGS: "Drugs",
    EDUCATION: "Education",
    ENTERTAINMENT_AND_RECREATION: "Entertainment and Recreation",
    GAMBLING: "Gambling",
    GAMES: "Games",
    GOVERNMENT_AND_POLITICS: "Government and Politics",
    HEALTH: "Health",
    ILLEGAL_OR_QUESTIONABLE: "Illegal or Questionable",
    INTERNET_COMMUNICATION: "Internet Communication",
    JOB_SEARCH: "Job Search",
    MILITANCY_HATE_AND_EXTREMISM: "Militancy Hate and Extremism",
    MISCELLANEOUS: "Miscellaneous",
    NEWS_AND_MEDIA: "News and Media",
    RELIGION: "Religion",
    SOCIAL_AND_FAMILY_ISSUES: "Social and Family Issues",
    SOCIETY_AND_LIFESTYLE: "Society and Lifestyle",
    SHOPPING_AND_AUCTIONS: "Shopping and Auctions",
    SPECIAL_INTERESTS_SOCIAL_ORGANIZATIONS:
      "Special Interests Social Organizations",
    SPORTS: "Sports",
    TASTELESS: "Tasteless",
    TRAVEL: "Travel",
    VEHICLES: "Vehicles",
    VIOLENCE: "Violence",
    WEAPONS_AND_BOMBS: "Weapons and Bombs",
    USER_DEFINED: "User Defined",
    SECURITY: "Security",
  },
  initialFilterState: {
    allow: [
      "INFORMATION_TECHNOLOGY",
      "BUSINESS_AND_ECONOMY",
      "EDUCATION",
      "ENTERTAINMENT_AND_RECREATION",
      "GOVERNMENT_AND_POLITICS",
      "HEALTH",
      "INTERNET_COMMUNICATION",
      "JOB_SEARCH",
      "MISCELLANEOUS",
      "NEWS_AND_MEDIA",
      "RELIGION",
      "SOCIAL_AND_FAMILY_ISSUES",
      "SOCIETY_AND_LIFESTYLE",
      "SHOPPING_AND_AUCTIONS",
      "SPECIAL_INTERESTS_SOCIAL_ORGANIZATIONS",
      "SPORTS",
      "TRAVEL",
      "VEHICLES",
      "USER_DEFINED",
    ],
    block: [
      "ADULT_MATERIAL",
      "DRUGS",
      "ILLEGAL_OR_QUESTIONABLE",
      "MILITANCY_HATE_AND_EXTREMISM",
      "TASTELESS",
      "VIOLENCE",
      "WEAPONS_AND_BOMBS",
      "SECURITY",
    ],
    isolate: ["GAMBLING", "GAMES"],
  },
  filterState: {
    allow: [
      "INFORMATION_TECHNOLOGY",
      "BUSINESS_AND_ECONOMY",
      "EDUCATION",
      "ENTERTAINMENT_AND_RECREATION",
      "GOVERNMENT_AND_POLITICS",
      "HEALTH",
      "INTERNET_COMMUNICATION",
      "JOB_SEARCH",
      "MISCELLANEOUS",
      "NEWS_AND_MEDIA",
      "RELIGION",
      "SOCIAL_AND_FAMILY_ISSUES",
      "SOCIETY_AND_LIFESTYLE",
      "SHOPPING_AND_AUCTIONS",
      "SPECIAL_INTERESTS_SOCIAL_ORGANIZATIONS",
      "SPORTS",
      "TRAVEL",
      "VEHICLES",
      "USER_DEFINED",
    ],
    block: [
      "ADULT_MATERIAL",
      "DRUGS",
      "GAMBLING",
      "GAMES",
      "ILLEGAL_OR_QUESTIONABLE",
      "MILITANCY_HATE_AND_EXTREMISM",
      "TASTELESS",
      "VIOLENCE",
      "WEAPONS_AND_BOMBS",
      "SECURITY",
    ],
    isolate: ["GAMBLING", "GAMES"],
  },
  onChange: (state) => {
    console.log(state);
  },
  usageText: () => (
    <>
      {t("OB_DRAG_INFO")}
      <span className="text-semantic-content-base-secondary font-medium">
        &nbsp;{t("OB_URL_ALL")}
      </span>
    </>
  ),
  header: "OB_URL_HEADER",
  subHeader: "OB_URL_SUB_HEADER",
  sectionHeaders: {
    block: {
      chip: {
        label: "OB_SECTION_BLOCK_HEADER",
        color: "danger",
        icon: <FontAwesomeIcon icon={faCircleX} />,
      },
      subHeader: "OB_SECTION_BLOCK_SUB_HEADER",
    },
    isolate: {
      chip: {
        label: "OB_SECTION_ISOLATE_HEADER",
        color: "warning",
        icon: <FontAwesomeIcon icon={faCircleExclamation} />,
      },
      subHeader: "OB_SECTION_ISOLATE_SUB_HEADER",
    },
    allow: {
      chip: {
        label: "OB_SECTION_ALLOW_HEADER",
        color: "success",
        icon: <FontAwesomeIcon icon={faCircleCheck} />,
      },
      subHeader: "OB_SECTION_ALLOW_SUB_HEADER",
    },
  },
  resetText: "RESET",
  instructionText: "OB_INSTRUCTION_TEXT",
  flowControl: {
    next: {
      text: "NEXT",
      href: ONBOARDING_PATHS.policy_ssl,
      onClick: () => console.log("next clicked"),
    },
    cancel: {
      text: "CANCEL",
      href: {
        pathname: ONBOARDING_PATHS.threeSteps,
        query: { stage: "traffic-done" },
      },
      onClick: () => console.log("Cancel clicked"),
    },
  },
  wizardData: [...WIZARD_DATA],
  loading: false,
  browserIsolationMissing: true,
};
