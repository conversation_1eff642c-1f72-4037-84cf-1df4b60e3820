import { useCallback, useState } from "react";
import {
  Navigation,
  NavigationBar,
  NavigationSection,
  NavigationMegaMenu,
  NavigationSearchAction,
  NavigationSearch,
  type TreeNode,
  useNavigationContext,
  useNavigationStore,
} from "@up/navigation";
import { Userpilot } from "userpilot";
import { resolveRoute, type RouteFn } from "@up/navigation/src/core";

import { NavigationLogo } from "./NavigationLogo";
import { AccountIcon, type AccountIconProps } from "./AccountIcon";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";
import { DEFAULT_DEV_FLAGS } from "@/modules/devFlags/types";
import { useFlags } from "@/context/FeatureFlags";
import { useDevFlags } from "@/modules/devFlags/DevFlagProvider";

export type TopNavigationProps = AccountIconProps;

export default function TopNavigation<T extends object>(
  props: TopNavigationProps,
) {
  const productAccessInfo = useProductAccessProvider();

  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [showSearch, setShowSearch] = useState<boolean>(false);

  const { navigator, defaultRoute } = useNavigationContext();

  const { can } = useFlags();

  const { getCurrentNavigation, setCurrentNavigation, setMegaMenuTab } =
    useNavigationStore();

  const handleNavigation = useCallback(
    <T,>(href: string | RouteFn<T>, external = false, entitlements?: T) => {
      setShowMenu(false);
      setCurrentNavigation(undefined);

      const route = resolveRoute(href, entitlements);

      if (navigator) {
        navigator.push(route, { external });
        navigator.refresh();
      }
    },
    [navigator, setCurrentNavigation],
  );

  const { isLoading: devFlagsLoading, devFlags } = useDevFlags();

  const pap = {
    ...productAccessInfo,
    devFlags: devFlagsLoading ? DEFAULT_DEV_FLAGS : devFlags,
    can,
  } as unknown as T;

  return (
    <div className="flex flex-col">
      <div>
        <Navigation
          orientation="horizontal"
          logo={<NavigationLogo href={defaultRoute} />}
          testId="top-navigation"
        >
          <NavigationSection
            placement="right"
            testId="top-nav-options"
            onHover={() => setShowMenu(false)}
            component={
              <AccountIcon
                {...props}
                search={
                  <NavigationSearchAction
                    active={showSearch}
                    triggerKey="k"
                    onToggle={() => {
                      Userpilot.track("quick-search-triggered");
                      setShowSearch(!showSearch);
                    }}
                  />
                }
              />
            }
          />

          <NavigationBar<T>
            entitlements={pap}
            onHover={(n?: TreeNode<T>, children = false) => {
              if (n && !showSearch) {
                setMegaMenuTab(undefined);
                setCurrentNavigation(n as TreeNode<object>);
                setShowMenu(children);
              }
            }}
            current={getCurrentNavigation()}
            testId="nav-pills"
          />

          {showSearch && (
            <NavigationSearch<T>
              triggerKey="k"
              onNavigate={(href: string | RouteFn<T>, external?: boolean) => {
                setShowSearch(false);
                handleNavigation(href, external, pap);
              }}
              onClose={() => {
                setShowSearch(!showSearch);
              }}
              entitlements={pap}
            />
          )}
        </Navigation>
        {showMenu && (
          <NavigationMegaMenu<T>
            entitlements={pap}
            currentNode={getCurrentNavigation()}
            onClose={() => {
              setShowMenu(false);
              setCurrentNavigation(undefined);
            }}
            testId="mega-menu"
          />
        )}
      </div>
    </div>
  );
}
