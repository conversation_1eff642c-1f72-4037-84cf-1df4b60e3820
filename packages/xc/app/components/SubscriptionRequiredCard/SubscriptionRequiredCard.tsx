import { useTranslation } from "react-i18next";
import { type ReactElement } from "react";
import { Link } from "@zs-nimbus/core";
import { cn } from "@up/components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faLock } from "@fortawesome/pro-solid-svg-icons";
import { getDataTestId } from "@/utils/utils";

type SubscriptionRequiredCardProps = {
  bordered?: boolean;
  maxWidth?: string;
  text: string;
  subText?: string | ReactElement;
  benefits?: string[];
  learnMoreURL?: "";
  id?: string;
  direction?: "row" | "col";
};

export const SubscriptionRequiredCard = ({
  text,
  benefits,
  learnMoreURL,
  subText,
  id,
  bordered = false,
  maxWidth = "w-full",
  direction = "col",
}: SubscriptionRequiredCardProps) => {
  const { t } = useTranslation();
  const baseClass =
    "flex justify-center items-center h-full px-6 py-4 typography-paragraph1";

  return (
    <div
      className={cn(
        baseClass,
        direction === "row" ? "flex-row gap-4" : "flex-col gap-2",
        maxWidth,
        { "border border-semantic-border-base-primary rounded-lg": bordered },
      )}
      data-testid={getDataTestId(id, "subscription-required")}
    >
      <div className="flex justify-center items-center rounded-full size-10 bg-semantic-brand-pale03">
        <FontAwesomeIcon
          aria-label={t("LOCK_ICON")}
          icon={faLock}
          className="text-semantic-content-interactive-primary-default"
        />
      </div>
      <div
        className={cn("flex flex-col gap-1", {
          "justify-center items-center": direction === "col",
        })}
      >
        <div className="typography-header5 text-semantic-content-base-subdued">
          {t(text)}
        </div>
        {benefits && (
          <div className="my-4">
            <span className="typography-paragraph1-strong mb-2">
              {t("BENEFITS")}:
            </span>
            <ul className="text-semantic-content-base-subdued mb-none">
              {benefits.map((item, index) => (
                <li key={index}>{`\u2022 ${item}`}</li>
              ))}
              {learnMoreURL && (
                <Link
                  href={learnMoreURL}
                  className="text-semantic-content-interactive-primary-default pl-3"
                >
                  {t("LEARN_MORE")}.
                </Link>
              )}
            </ul>
          </div>
        )}
        {subText && (
          <span
            className={cn("text-semantic-content-base-tertiary", {
              "text-center": direction === "col",
            })}
          >
            {subText}
          </span>
        )}
      </div>
    </div>
  );
};
