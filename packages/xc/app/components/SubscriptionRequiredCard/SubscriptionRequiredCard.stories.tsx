import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { <PERSON> } from "@zs-nimbus/core";
import { SubscriptionRequiredCard as component } from "./SubscriptionRequiredCard";

const meta = {
  title: "Components/SubscriptionRequiredCard",
  component,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/ioIRK0SSHfj0oKuPQBZkl8/Unified_Onboarding_1.0?node-id=27921-243120&m=dev",
    },
  },
} satisfies Meta<typeof component>;

export default meta;
type Story = StoryObj<typeof meta>;

export const SubscriptionRequiredCard: Story = {
  args: {
    text: "Subscription Required",
    subText: (
      <>
        Your organization does not currently have this subscription. To learn
        more, please <Link href="">{"contact sales representative"}</Link>
      </>
    ),
  },
};

export const SubscriptionRequiredWithDirection: Story = {
  args: {
    text: "Subscription Required",
    direction: "row",
    subText: (
      <>
        Your organization does not currently have this subscription. To learn
        more, please <Link href="">{"contact sales representative"}</Link>
      </>
    ),
  },
};

export const SubscriptionRequiredWithBenefits: Story = {
  args: {
    text: "Subscription Required",
    maxWidth: "max-w-[450px]",
    bordered: true,
    benefits: [
      "Protects sensitive information",
      "Enhances cyber posture",
      "Endpoint protection",
      "Enhanced threat detection",
    ],
    subText: (
      <>
        Your organization does not currently have this subscription. To learn
        more, please <Link href="">{"contact sales representative"}</Link>
      </>
    ),
  },
};
