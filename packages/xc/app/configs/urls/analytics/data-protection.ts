import { API_ENDPOINTS } from "@/utils/apiHelper";

type DataChannels = {
  InlineWeb: {
    TopSensitiveDataTypes: string;
    TopHighRiskApplicationsToEliminate: string;
    TopHighRiskApplicationsToSecure: string;
    SensitiveGenAIVolumeData: string;
    SensitiveGenAITransactionData: string;
    TopUnsanctionedInstancesSaaApplications: string;
  };
  SaaSSecurity: {
    SaaSIncidents: string;
    TopUsersSaaSIncidents: string;
    TopDepartmentsSaaSIncidents: string;
    TopSaaSApplicationsHighRiskMisconfigurations: string;
    SaaSApplicationsDataExposure: string;
  };
  Email: {
    SensitiveData: string;
    TopDomainsSensitiveDataBeingSentToChart: string;
    TopDomainsSensitiveDataBeingSentToTable: string;
    TopDepartmentEmailIncidents: string;
    TopUsersEmailIncidents: string;
  };
  Endpoints: {
    SensitiveData: string;
    TopEndpointsSensitiveDataBeingExfiltratedTable: string;
    TopEndpointsSensitiveDataBeingExfiltratedChart: string;
    TopDepartmentEndpointIncidents: string;
    TopUsersEndpointIncidents: string;
    TotalEndpointIncidents: string;
  };
};

type Dashboard = {
  UsersMostIncidentsWeb: string;
  UsersMostIncidentsCASB: string;
  UsersMostIncidentsEDLP: string;
  UsersMostIncidentsEmailDLP: string;
  TotalIncidentsWeb: string;
  TotalIncidentsCASB: string;
  TotalIncidentsEDLP: string;
  TotalIncidentsEmailDLP: string;
  AllChannelsWeb: string;
  SensitiveDataAtRisk: string;
  SensitiveDataRestDrawer: string;
};

type DataProtectionApiUrls = {
  DataChannels: DataChannels;
  Dashboard: Dashboard;
};

export const DataProtection: DataProtectionApiUrls = {
  DataChannels: {
    InlineWeb: {
      TopSensitiveDataTypes: `${API_ENDPOINTS.ZIA}/reportData/web`,
      TopHighRiskApplicationsToEliminate: `${API_ENDPOINTS.ZIA}/reportData/web`,
      TopHighRiskApplicationsToSecure: `${API_ENDPOINTS.ZIA}/reportData/web`,
      SensitiveGenAIVolumeData: `${API_ENDPOINTS.ZIA}/genAIReport/group`,
      SensitiveGenAITransactionData: `${API_ENDPOINTS.ZIA}/genAIReport/group`,
      TopUnsanctionedInstancesSaaApplications: `${API_ENDPOINTS.ZIA}/reportData/web`,
    },
    SaaSSecurity: {
      SaaSIncidents: `${API_ENDPOINTS.ZIA}/reportData/casb`,
      TopUsersSaaSIncidents: `${API_ENDPOINTS.ZIA}/reportData/casb`,
      TopDepartmentsSaaSIncidents: `${API_ENDPOINTS.ZIA}/reportData/casb`,
      TopSaaSApplicationsHighRiskMisconfigurations: `${API_ENDPOINTS.ZIA}/reportData/casb`,
      SaaSApplicationsDataExposure: `${API_ENDPOINTS.ZIA}/reportData/casb`,
    },
    Email: {
      SensitiveData: `${API_ENDPOINTS.ZIA}/emailDlp/group`,
      TopDomainsSensitiveDataBeingSentToChart: `${API_ENDPOINTS.ZIA}/emailDlp/group`,
      TopDomainsSensitiveDataBeingSentToTable: `${API_ENDPOINTS.ZIA}/emailDlp/group`,
      TopDepartmentEmailIncidents: `${API_ENDPOINTS.ZIA}/reportData/emailDLP`,
      TopUsersEmailIncidents: `${API_ENDPOINTS.ZIA}/emailDlp/group`,
    },
    Endpoints: {
      SensitiveData: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
      TotalEndpointIncidents: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
      TopDepartmentEndpointIncidents: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
      TopUsersEndpointIncidents: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
      TopEndpointsSensitiveDataBeingExfiltratedChart: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
      TopEndpointsSensitiveDataBeingExfiltratedTable: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
    },
  },
  Dashboard: {
    UsersMostIncidentsWeb: `${API_ENDPOINTS.ZIA}/reportData/web`,
    UsersMostIncidentsCASB: `${API_ENDPOINTS.ZIA}/reportData/casb`,
    UsersMostIncidentsEDLP: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
    UsersMostIncidentsEmailDLP: `${API_ENDPOINTS.ZIA}/reportData/emailDLP`,
    TotalIncidentsWeb: `${API_ENDPOINTS.ZIA}/reportData/web`,
    TotalIncidentsCASB: `${API_ENDPOINTS.ZIA}/reportData/casb`,
    TotalIncidentsEDLP: `${API_ENDPOINTS.ZIA}/reportData/edlp`,
    TotalIncidentsEmailDLP: `${API_ENDPOINTS.ZIA}/reportData/emailDLP`,
    AllChannelsWeb: `${API_ENDPOINTS.ZIA}/reportData/web`,
    SensitiveDataAtRisk: `${API_ENDPOINTS.DSPM}/dashboard/v2/metrics/chart`,
    SensitiveDataRestDrawer: `${API_ENDPOINTS.DSPM}/dashboard/v2/metrics/chart/account`,
  },
};
