/**
 * Hosted User page types
 */

export type FetchParams = {
  limit?: number;
  offset?: number;
  excludeDynamicGroups?: boolean;
  excludeGroups?: string;
  requireTotal?: boolean;
  name?: string;
  defaultIdp?: boolean;
  requirePseudoDomain?: boolean;
  domainType?: string;
  requireInternalDomain?: boolean;
  search?: string;
  query?: string;
  type?: string;
  status?: string;
  fieldName?: string;
  orderBy?: string;
  deploymentStatus?: string;
};

export type UploadCSVParams = {
  override?: boolean;
};

/**
 * Three Steps page types
 */

export type ConnectedUserParams = {
  page?: number;
  pageSize?: number;
};

export type ConnectedDeviceParams = {
  page?: number;
  pageSize?: number;
};

export type IdpMetadataParams = {
  metadataurl: string;
  idpProfileType?: string;
};
