import { TopEndpointsSensitiveDataBeingExfiltratedTableMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Endpoints/TopEndpointsSensitiveDataBeingExfiltrated/TopEndpointsSensitiveDataBeingExfiltratedTable/mock.data";
import { TrendCardMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/mock.data";
import { TopHighRiskApplicationsEliminateTableRowData } from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/TopHighRisk/TopHighRiskApplicationsEliminate/mock.data";
import { TopHighRiskApplicationsSecureTableRowData } from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/TopHighRisk/TopHighRiskApplicationsSecure/mock.data";
import { TopEndpointsSensitiveDataBeingExfiltratedChartMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Endpoints/TopEndpointsSensitiveDataBeingExfiltrated/TopEndpointsSensitiveDataBeingExfiltratedChart/mock.data";
import { TopSensitiveChartData } from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/TopSensitiveDataTypes/mock.data";
import { TopUnsanctionedInstancesSanctionedSaaSApplicationsChartMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/DataApplications/TopUnsanctionedInstancesSanctionedSaaSApplications/mock.data";
import { SaaSIncidentsChartMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/SaaSSecurity/SaaSIncidents/mock.data";
import { SaaSApplicationsDataExposureTableRowData } from "@/components/Analytics/DataProtection/DataChannels/Files/SaaSSecurity/SaaSApplicationsDataExposure/mock.data";
import { TopUsersEndpointIncidentsMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Endpoints/EndpointIncidents/TopEndpointIncidents/TopUsersEndpointIncidents/mock.data";
import { TopSaaSApplicationsHighRiskMisconfigurationsContainerMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/SaaSSecurity/TopSaas/TopSaaSApplicationsHighRiskMisconfigurations/mock.data";
import { TopDomainsSensitiveDataBeingSentToChartMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Email/TopDomainsSensitiveDataBeingSentTo/TopDomainsSensitiveDataBeingSentToChart/mock.data";
import { TopDomainsSensitiveDataBeingSentToTableMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Email/TopDomainsSensitiveDataBeingSentTo/TopDomainsSensitiveDataBeingSentToTable/mock.data";
import { TopDepartmentEmailIncidentsTableMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Email/TopEmailIncidents/TopDepartmentEmailIncidents/mock.data";
import { TotalEndpointIncidentsMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Endpoints/EndpointIncidents/TotalEndpointIncidents/mock.data";
import { TopDepartmentEndpointIncidentsMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Endpoints/EndpointIncidents/TopEndpointIncidents/TopDepartmentEndpointIncidents/mock.data";
import { TopUsersEmailIncidentsTableMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Email/TopEmailIncidents/TopUsersEmailIncidents/mock.data";
import { TotalIncidentsMockData } from "@/components/Analytics/DataProtection/Dashboard/TotalIncidents/mock.data";
import { TopUsersSaaSIncidentsMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/SaaSSecurity/TopSaas/TopSaaSIncidents/TopUsersSaaSIncidents/mock.data";
import { TopDepartmentsSaaSIncidentsMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/SaaSSecurity/TopSaas/TopSaaSIncidents/TopDepartmentsSaaSIncidents/mock.data";
import { SensitiveGenAIVolumeMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/DataApplications/SensitiveGenAIData/SensitiveGenAIVolumeData/mock.data";
import { SensitiveGenAITransactionMockData } from "@/components/Analytics/DataProtection/DataChannels/Files/Inline/DataApplications/SensitiveGenAIData/SensitiveGenAITransactionData/mock.data";
import { AllDataChannelsMockData } from "@/components/Analytics/DataProtection/Dashboard/AllDataChannels/mock.data";
import { BubbleTreeChartsMockDataForDLPEngines } from "@/components/Analytics/DataProtection/Dashboard/TopSensitiveDataRest/TopSensitiveDataDLPEngine/mock.data";
import { BubbleTreeChartsMockDataForGenAI } from "@/components/Analytics/DataProtection/Dashboard/TopSensitiveDataRest/TopSensitiveDataGenAIClassification/mock.data";
import { TopSensitiveDataRestDrawerMockData } from "@/components/Analytics/DataProtection/Dashboard/TopSensitiveDataRest/TopSensitiveDataRestDrawer/mock.data";

export const DATA_PROTECTION_API_TO_MOCK: Record<string, object> = {
  "DataProtection.DataChannels.Email.SensitiveData": [],
  "DataProtection.DataChannels.Email.TopDomainsSensitiveDataBeingSentToChart":
    TopDomainsSensitiveDataBeingSentToChartMockData,
  "DataProtection.DataChannels.Email.TopDomainsSensitiveDataBeingSentToTable":
    TopDomainsSensitiveDataBeingSentToTableMockData,
  "DataProtection.DataChannels.Email.TopDepartmentEmailIncidents":
    TopDepartmentEmailIncidentsTableMockData,
  "DataProtection.DataChannels.Email.TopUsersEmailIncidents":
    TopUsersEmailIncidentsTableMockData,
  "DataProtection.DataChannels.Endpoints.SensitiveData": [],
  "DataProtection.DataChannels.Endpoints.TotalEndpointIncidents":
    TotalEndpointIncidentsMockData,
  "DataProtection.DataChannels.Endpoints.TopUsersEndpointIncidents":
    TopUsersEndpointIncidentsMockData,
  "DataProtection.DataChannels.Endpoints.TopDepartmentEndpointIncidents":
    TopDepartmentEndpointIncidentsMockData,
  "DataProtection.DataChannels.Endpoints.TopEndpointsSensitiveDataBeingExfiltratedTable":
    TopEndpointsSensitiveDataBeingExfiltratedTableMockData,
  "DataProtection.DataChannels.Endpoints.TopEndpointsSensitiveDataBeingExfiltratedChart":
    TopEndpointsSensitiveDataBeingExfiltratedChartMockData,
  "DataProtection.DataChannels.Inline.TopSensitiveChart": TopSensitiveChartData,
  "DataProtection.DataChannels.TrentCard": TrendCardMockData,
  "DataProtection.DataChannels.Inline.TopHighRiskApplicationsEliminate":
    TopHighRiskApplicationsEliminateTableRowData,
  "DataProtection.DataChannels.Inline.TopHighRiskApplicationsSecure":
    TopHighRiskApplicationsSecureTableRowData,
  "DataProtection.DataChannels.Inline.TopUnsanctionedInstancesSanctionedSaaSApplications":
    TopUnsanctionedInstancesSanctionedSaaSApplicationsChartMockData,
  "DataProtection.DataChannels.Inline.SensitiveGenAIVolume":
    SensitiveGenAIVolumeMockData,
  "DataProtection.DataChannels.Inline.SensitiveGenAITransaction":
    SensitiveGenAITransactionMockData,
  "DataProtection.DataChannels.SaaSIncidents": SaaSIncidentsChartMockData,
  "DataProtection.DataChannels.SaaSApplicationsDataExposure":
    SaaSApplicationsDataExposureTableRowData,
  "DataProtection.DataChannels.TopSaaSApplicationsHighRiskMisconfigurations":
    TopSaaSApplicationsHighRiskMisconfigurationsContainerMockData,
  "DataProtection.DataChannels.TopUsersSaaSIncidents":
    TopUsersSaaSIncidentsMockData,
  "DataProtection.DataChannels.TopDepartmentsSaaSIncidents":
    TopDepartmentsSaaSIncidentsMockData,
  "DataProtection.Dashboard.Totalncidents": TotalIncidentsMockData,
  "DataProtection.Dahboard.AllChannelsData": AllDataChannelsMockData,
  "DataProtection.Dashboard.SensitiveGENAIData":
    BubbleTreeChartsMockDataForGenAI,
  "DataProtection.Dashboard.SensitiveDLPData":
    BubbleTreeChartsMockDataForDLPEngines,
  "DataProtection.Dashboard.SensitiveDataRestDrawer":
    TopSensitiveDataRestDrawerMockData,
};
