/* eslint-disable @up/unified-platform/max-file-lines */
import { type NavigationBuilder } from "@up/navigation";
import { ZIA_ROUTE_PREFIX } from "@zia/rbac";
import { type ProductAccessInfoWithDevFlags } from "@/configs/navigation/types";
import {
  CcNavUtils,
  isZPALocalScopeAdmin,
  isZtdsMenuHidden,
  ZiaNavUtils,
  ZpaFeatureFlag,
  ZPAIsZSDKEnabledTenant,
} from "@/utils/GlobalNavigationUtils";
import { maHideNavLink } from "@/utils/maUtils";
import { CC_ROUTE_PREFIX } from "@/utils/ccUtils";

export const drawInfrastructureBuilder = (
  b: NavigationBuilder<ProductAccessInfoWithDevFlags>,
) => {
  b.Group(
    {
      key: "MM_INFRA_TAB_INET_SASS",
      icon: "fa-cloud",
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    () => {
      b.Group(
        {
          key: "MM_INF_INET_SASS_VTAB_FORWARDING",
          icon: "fa-regular fa-arrow-up-arrow-down",
        },
        () => {
          b.Group("MMC_INFRA_PRV_SVC_EDGES", () => {
            b.Menu({
              key: "MMC_INFRA_VIRT_SRVC_EDGES",
              route: `/${ZIA_ROUTE_PREFIX}#administration/virtualserviceedge-settings`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.cloudConfig.vseSettings")(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_VIRT_ZENS",
              route: `/${ZIA_ROUTE_PREFIX}#administration/vzen-settings`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.cloudConfig.vzenSettings")(pai)
                  ? "hidden"
                  : "visible",
            });
          });

          b.Group("TN_NETWORKING_IS_PI", () => {
            b.Menu({
              key: "MMC_INFRA_PINT_SDWAN",
              route: `/${ZIA_ROUTE_PREFIX}#administration/partner-integration?SDWAN`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils(
                  "administration.cloudConfig.partnerIntegration.sdWan",
                )(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_PINT_AZURE_VWAN",
              route: `/${ZIA_ROUTE_PREFIX}#administration/partner-integration?AZURE_VIRTUAL_WAN`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils(
                  "administration.cloudConfig.partnerIntegration.azureWan",
                )(pai)
                  ? "hidden"
                  : "visible",
            });
          });

          b.Group("TN_NETWORKING_IS_TRAFFIC_STEERING", () => {
            b.Menu({
              key: "MMC_INFRA_HOSTED_PAC_FILES",
              route: `/${ZIA_ROUTE_PREFIX}#administration/pac-file-versioning`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils(
                  "administration.trafficForwarding.pacFileVersioning",
                )(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_SUBCLOUDS",
              route: `/${ZIA_ROUTE_PREFIX}#administration/subclouds`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.dcExclusion.subclouds")(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_DC_EXCLUSION",
              route: `/${ZIA_ROUTE_PREFIX}#administration/dc-exclusion`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.dcExclusion.dcExclusion")(pai)
                  ? "hidden"
                  : "visible",
            });
          });

          b.Group("TN_NETWORKING_IS_IP_CONF", () => {
            b.Menu({
              key: "MMC_INFRA_IPV6_CFG",
              route: `/${ZIA_ROUTE_PREFIX}#administration/ipv6-configuration`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.ipv6")(pai) ? "hidden" : "visible",
            });
          });
        },
      );
      b.Group(
        {
          key: "MM_INF_INET_SASS_VTAB_NET_POLICY",
          icon: "fa-regular fa-sliders-up",
        },
        () => {
          b.Group("TN_NETWORKING_IS_FC", () => {
            b.Menu({
              key: "MMC_INFRA_FORWARDING_CTRL_POLICY",
              route: `/${ZIA_ROUTE_PREFIX}#policy/firewall/forwarding-control`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("policy.firewall.forwardingControl")(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_PROXIES_GATWEWAYS",
              route: `/${ZIA_ROUTE_PREFIX}#administration/proxies-gateways`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.forwardingMethods.proxiesGateways")(
                  pai,
                )
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_DEDICATED_IP",
              route: `/${ZIA_ROUTE_PREFIX}#administration/dedicated-ip`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.forwardingMethods.dedicatedIp")(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_ZSCALER_PA",
              route: `/${ZIA_ROUTE_PREFIX}#administration/zs-private-access`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils(
                  "administration.forwardingMethods.zscalerPrivateAccess",
                )(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_ROOT_CERTS",
              route: `/${ZIA_ROUTE_PREFIX}#administration/root-certificate`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.rootCertificate")(pai)
                  ? "hidden"
                  : "visible",
            });
          });
          b.Group("TN_NETWORKING_IS_BANDWIDTH_CONTROL", () => {
            b.Menu({
              key: "MMC_INFRA_BANDWIDTH_CTRL",
              route: `/${ZIA_ROUTE_PREFIX}#policy/web/bandwidth-control`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("policy.web.accessControl.bandwidthControl")(pai)
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_BANDWIDTH_CLASSES",
              route: `/${ZIA_ROUTE_PREFIX}#administration/bandwidth`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                ZiaNavUtils("administration.accessControl.bandwidth")(pai)
                  ? "hidden"
                  : "visible",
            });
          });
        },
      );
      b.Group(
        { key: "MM_INF_INET_SASS_TRAFFIC_CAPTURE", icon: "fa-file-lines" },
        () => {
          b.Group(
            {
              key: "MM_INF_INET_SASS_TC_CAPTURE_CONTROLS",
            },
            () => {
              b.Menu({
                key: "MM_INF_INET_SASS_TC_TRAFFIC_CAPTURE_SETTING",
                route: `/${ZIA_ROUTE_PREFIX}#administration/traffic-capture-settings`,
                visibility: (pai: ProductAccessInfoWithDevFlags) =>
                  ZiaNavUtils(
                    "administration.trafficForwarding.trafficCapture",
                  )(pai)
                    ? "hidden"
                    : "visible",
              });
            },
          );
        },
      );
    },
  );

  b.Group({ key: "MM_INFRA_TAB_PRIV_ACCAESS", icon: "fa-window" }, () => {
    b.Group(
      {
        key: "MM_PRIV_ACCESS_VTAB_COMPONENT",
        icon: "fa-regular fa-objects-column",
      },
      () => {
        b.Group("MMC_INFRA_APP_CONNECTORS", () => {
          b.Menu({
            key: "MMC_INFRA_APP_CONNECTORS",
            route: "/private#connectors",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
          b.Menu({
            key: "MMC_INFRA_APP_CONNECTOR_GROUPS",
            route: "/private#connectorGroups",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
          b.Menu({
            key: "MMC_INFRA_APP_CONNECTOR_KEYS",
            route: "/private#connectorProvisioning",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
        });

        b.Group("TN_NETWORKING_PI_PRIVATE_SE", () => {
          b.Menu({
            key: "MMC_INFRA_PRV_SVC_EDGES",
            route: "/private#privateBrokers",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
          b.Menu({
            key: "MMC_INFRA_SVC_EDGE_GROUPS",
            route: "/private#privateBrokerGroups",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
          b.Menu({
            key: "MMC_INFRA_SVC_EDGE_KEYS",
            route: "/private#privateBrokerProvisioning",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
        });

        b.Group("TN_NETWORKING_PI_CBCR", () => {
          b.Menu({
            key: "MMC_INFRA_CLOUD_CONNECTOR",
            route: "/private#cloudConnectors",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              ZPAIsZSDKEnabledTenant(features) ||
              !ZpaFeatureFlag(features, "ui.connector.cloudconnector")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CLOUD_CONNECTOR_GROUPS",
            route: "/private#cloudConnectorGroups",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              ZPAIsZSDKEnabledTenant(features) ||
              !ZpaFeatureFlag(features, "ui.connector.cloudconnector")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_BRANCH_CONNECTOR",
            route: "/private#branchConnectors",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              ZpaFeatureFlag(features, "feature.branchconnector")
                ? "visible"
                : "hidden",
          });
          b.Menu({
            key: "MMC_INFRA_BRANCH_CONNECTOR_GROUPS",
            route: "/private#branchConnectorGroups",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              ZpaFeatureFlag(features, "feature.branchconnector")
                ? "visible"
                : "hidden",
          });
        });

        b.Group("TN_NETWORKING_PI_IC", () => {
          b.Menu({
            key: "MMC_INFRA_ENROLLMENT_CERTS",
            route: "/private#enrollmentCerts",
            visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
              entitlements.zpa ? "visible" : "hidden",
          });
        });
        b.Group("TN_NETWORKING_PI_VPN", () => {
          b.Menu({
            key: "MMC_INFRA_VPN_DASHBOARD",
            route: "/private#networkPresence/dashboard",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_CONNECTORS",
            route: "/private#/networkPresence/networkConnector",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_CONNECTOR_GROUPS",
            route: "/private#/networkPresence/networkConnectorGroup",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_CONNECTOR_PROV_KEYS",
            route: "/private#/networkPresence/networkConnectorProvisioningKey",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_SVC_EDGES",
            route: "/private#/networkPresence/networkServiceEdges",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_CONNECTED_USERS",
            route: "/private#/networkPresence/networkConnectedUsers",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_SEGMENT",
            route: "/private#/networkPresence/networkSegment",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !ZpaFeatureFlag(features, "feature.ui_api.network_presence")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_USER_ENABLEMENT",
            route: "/private#/vpnTunnelPolicy",

            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              isZPALocalScopeAdmin(features) ||
              !(
                ZpaFeatureFlag(features, "feature.ui_api.network_presence") &&
                ZpaFeatureFlag(features, "feature.np.vpn_tunnel_policy")
              )
                ? "hidden"
                : "visible",
          });
        });
      },
    );

    b.Group(
      {
        key: "MM_PRIV_ACCESS_VTAB_BUS_CONTINUITY",
        icon: "fa-regular  fa-circle-location-arrow",
      },
      () => {
        b.Group("MM_PRIV_ACCESS_VTAB_BUS_CONTINUITY", () => {
          b.Menu({
            key: "MMC_INFRA_BC_PRV_CLOUD_CTRLS",
            route: "/private#/privateCloud/PrivateCloudControllers",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.ddil.config")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_BC_PRV_CLOUD_CTRL_GROUPS",
            route: "/private#/privateCloud/PrivateCloudControllerGroups",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.ddil.config")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_BC_PRV_CLOUD_CTRL_PROV_KEYS",
            route:
              "/private#/privateCloud/PrivateCloudControllerProvisioningKeys",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.ddil.config")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_BC_PRV_CLOUDS",
            route: "/private#/privateCloud/PrivateCloud",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.ddil.config")
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_BC_SETTINGS",
            route: "/private#/privateCloud/BusinessContinuitySettings",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.ddil.config")
                ? "hidden"
                : "visible",
          });
        });
        b.Group("TN_NETWORKING_PI_DISASTER_REC", () => {
          b.Menu({
            key: "MMC_INFRA_BC_DISASTER_RECOVERY",
            route: "/private#disasterRecovery",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.disaster.recovery")
                ? "hidden"
                : "visible",
          });
        });
      },
    );

    b.Group(
      {
        key: "MM_PRIV_ACCESS_VTAB_B2B_EXCHANGE",
        icon: "fa-regular fa-circle-nodes",
      },
      () => {
        b.Group("MM_PRIV_ACCESS_VTAB_EXTRANET", () => {
          b.Menu({
            key: "MM_PRIV_ACCESS_VTAB_EXTRANET",
            route: `/${ZIA_ROUTE_PREFIX}#administration/extranet`,
            visibility: (props: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils("administration.trafficForwarding.extranet")(props)
                ? "hidden"
                : "visible",
          });
        });
      },
    );

    b.Group(
      {
        key: "MM_PRIV_ACCESS_VTAB_CC_POLICIES",
        icon: "fa-regular fa-sliders-up",
      },
      () => {
        b.Group("MM_PRIV_ACCESS_VTAB_CC_POLICIES", () => {
          b.Menu({
            key: "MMC_INFRA_CCP_CLIENT_FWD_POLICIES",
            route: "/private#bypassPolicy",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              !entitlements.zpa || ZPAIsZSDKEnabledTenant(features)
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CCP_REDIRECTION_POLICIES",
            route: "/private#redirectionPolicy",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "feature.pse.policy_redirect")
                ? "hidden"
                : "visible",
          });
        });
        b.Group("MMC_INFRA_CCP_IP_ASSIGNMENT", () => {
          b.Menu({
            key: "MMC_INFRA_CCP_IP_ASSIGNMENT",
            route: "/private#clientConnectorIpAssignment",
            visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
              !ZpaFeatureFlag(features, "ui.application.c2c_ip")
                ? "hidden"
                : "visible",
          });
        });
      },
    );
  });

  b.Group({ key: "LOCATIONS", icon: "fa-location-dot" }, () => {
    b.Container(() => {
      b.Group(
        {
          key: "TN_NETWORKING_IS_LOCATION_MGT",
          icon: "fa-regular fa-location-dot",
        },
        () => {
          b.Menu({
            key: "MM_INFRA_TAB_LOCATIONS",
            route: `/locations`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils("administration.trafficForwarding.locations")(pai) ||
              !pai.can("showUnifiedLocations") ||
              CcNavUtils(pai, [
                "EDGE_CONNECTOR_CLOUD_PROVISIONING",
                "EDGE_CONNECTOR_TEMPLATE",
              ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MM_INFRA_TAB_LEGACY_LOCATIONS",
            route: `/${ZIA_ROUTE_PREFIX}#administration/locations`,
            visibility: (pia: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils("administration.trafficForwarding.locations")(pia)
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MM_INFRA_LOCATIONS_GROUPS",
            route: `/${ZIA_ROUTE_PREFIX}#administration/locations?LOCATION_GROUPS`,
            visibility: (props: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils("administration.trafficForwarding.locations")(props)
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "Azure Virtual WAN Locations",
            route: `/${ZIA_ROUTE_PREFIX}#administration/locations?AZURE_VIRTUAL_WAN_LOCATIONS`,
            visibility: (props: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils(
                "administration.trafficForwarding.locations.azureVirtualWan",
              )(props)
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "MMC_INFRA_LOCATION_CC_TN",
            route: "/ma/admin/trusted-networks",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "networking.trustedNetwork")
                ? "hidden"
                : "visible",
          });
        },
      );

      b.Group(
        { key: "menu.LocationResources", icon: "fa-regular fa-objects-column" },
        () => {
          b.Menu({
            key: "MM_INFRA_LOCATIONS_TEMPLATES",
            route: `/${CC_ROUTE_PREFIX}/administration/location-templates`,
            visibility: (props: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(props, [
                "EDGE_CONNECTOR_TEMPLATE",
                "EDGE_CONNECTOR_LOCATION_MANAGEMENT",
              ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_STATICIP_GRE_TUNNELS",
            route: `/${ZIA_ROUTE_PREFIX}#administration/gre-self-sign`,
            visibility: (props: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils("administration.trafficForwarding.greSelfSign")(props)
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_VPN_CREDS",
            route: `/${ZIA_ROUTE_PREFIX}#administration/vpn-credentials`,
            visibility: (props: ProductAccessInfoWithDevFlags) =>
              ZiaNavUtils("administration.trafficForwarding.vpnCredentials")(
                props,
              )
                ? "hidden"
                : "visible",
          });
        },
      );

      // mlussier:4/3/25 - I commented this out as we do not allow duplicate
      // routes in the menu tree as this creates problems with sidebav and which
      // item to highlight
      // b.Group("MM_INFRA_TAB_CONNECTOR", () => {
      //   b.Menu({
      //     key: "MMC_INFRA_LOCATION_CC_TN",
      //     route: "/ma/admin/trusted-networks",
      //     visibility: ({
      //       features,
      //       entitlements,
      //       can,
      //     }: ProductAccessInfoWithDevFlags) =>
      //       maHideNavLink(
      //         entitlements,
      //         features,
      //         "networking.trustedNetwork",
      //       ) || !can("showUnifiedLocations")
      //         ? "hidden"
      //         : "visible",
      //   });
      // });
    });
  });

  b.Group({ key: "nav.infrastructure.connectors", icon: "fa-plug" }, () => {
    b.Group(
      { key: "MM_CONNECTOR_VTAB_CLIENT", icon: "fa-regular fa-laptop-mobile" },
      () => {
        b.Group("MM_CLIENT_CONNECTOR_VTAB_ENROLLED_DEVICES", () => {
          b.Menu({
            key: "MMC_INFRA_CC_ED_DO",
            route: "/ma/enrolled-devices/device-overview",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_ED_PD",
            route: "/ma/enrolled-devices/partner-devices",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_ED_MT",
            route: "/ma/enrolled-devices/machine-tunnel",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_ED_DG",
            route: "/ma/admin/device-groups",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "networking.deviceGroups")
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "CC_VDI_DEVICE_MANAGEMENT",
            route: `/${CC_ROUTE_PREFIX}/administration/vdi/device-management/devices`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                ? "hidden"
                : "visible",
          });
        });

        b.Group("TN_NETWORKING_CC_PS", () => {
          b.Menu({
            key: "MMC_INFRA_CC_PS_WINDOWS",
            route: "/ma/admin/platform-settings/windows",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_PS_MACOS",
            route: "/ma/admin/platform-settings/macos",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_PS_LINUX",
            route: "/ma/admin/platform-settings/linux",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_PS_IOS",
            route: "/ma/admin/platform-settings/ios",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_PS_ANDROID",
            route: "/ma/admin/platform-settings/android",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
        });

        b.Group("TN_NETWORKING_CC_FP", () => {
          b.Menu({
            key: "MMC_INFRA_CC_FP_PLATFORMS",
            route: "/ma/admin/forwarding-profile",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "CC_VDI_PROFILE",
            route: `/${CC_ROUTE_PREFIX}/administration/vdi/agent-forwarding-profile`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                ? "hidden"
                : "visible",
          });
        });

        b.Group("TN_NETWORKING_CC_GLOBAL_SET", () => {
          b.Menu({
            key: "MMC_INFRA_CC_GS_UA",
            route: "/ma/admin/user-agent",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_GS_DPP",
            route: "/ma/admin/dedicated-proxy-port",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(
                entitlements,
                features,
                "networking.dedicatedProxyPort",
              )
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "MMC_INFRA_CC_GS_ZD",
            route: "/ma/admin/zscaler-deception",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(
                entitlements,
                features,
                "networking.zscalerDeception",
              )
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "MMC_INFRA_CC_GS_EC",
            route:
              "/ma/admin/client-connector-support?tab=ENDPOINT_INTEGRATION",
            visibility: ({
              entitlements,
              features,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_GS_DSCRC",
            route:
              "/ma/admin/client-connector-support?tab=ADVANCED_CONFIGURATION",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "networking.deviceCleanup")
                ? "hidden"
                : "visible",
          });
        });

        b.Group("TN_NETWORKING_CC_ST", () => {
          b.Menu({
            key: "MMC_INFRA_CC_SUPPORT_AS",
            route: "/ma/admin/client-connector-support?tab=APP_SUPPORTABILITY",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_SUPPORT_NP",
            route: "/ma/admin/client-connector-support?tab=NETWORK_PERFORMANCE",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_SUPPORT_UP",
            route: "/ma/admin/client-connector-support?tab=USER_PRIVACY",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_SUPPORT_AFO",
            route: "/ma/admin/client-connector-support?tab=APP_FAIL_OPEN",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MMC_INFRA_CC_SUPPORT_CCDM",
            route: "/ma/admin/client-connector-support?tab=DEVICE_CLEANUP",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(entitlements, features, "networking.deviceCleanup")
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "MMC_INFRA_CC_SUPPORT_BC",
            route: "/ma/admin/business-continuity",
            visibility: ({
              features,
              entitlements,
            }: ProductAccessInfoWithDevFlags) =>
              maHideNavLink(
                entitlements,
                features,
                "networking.businessContinuity",
              )
                ? "hidden"
                : "visible",
          });
        });
      },
    );
    b.Group(
      { key: "MM_CONNECTOR_VTAB_EDGE", icon: "fa-regular fa-buildings" },
      () => {
        b.Group("CC_MANAGEMENT", () => {
          b.Menu({
            key: "APPLIANCES",
            route: "/appliances",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, [
                "EDGE_CONNECTOR_CLOUD_PROVISIONING",
                "EDGE_CONNECTOR_TEMPLATE",
              ]) ||
              !pai.can("showUnifiedLocations") ||
              ZiaNavUtils("administration.trafficForwarding.locations")(pai)
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_BC_TRAFFIC",
            route: `/${CC_ROUTE_PREFIX}/dashboard/connector-monitoring?filter=BC`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_DASHBOARD"])
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "CC_BRANCH_PROVISIONING",
            route: `/${CC_ROUTE_PREFIX}/administration/branch-provisioning-templates?filter=BC`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, [
                "EDGE_CONNECTOR_CLOUD_PROVISIONING",
                "EDGE_CONNECTOR_TEMPLATE",
              ])
                ? "hidden"
                : "visible",
          });
        });

        b.Group("CC_TRAFFIC_STEERING", () => {
          b.Menu({
            key: "CC_FWD_POLICY",
            route: `/${CC_ROUTE_PREFIX}/policy/edge-connector-traffic-forwarding-control`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_LOG_AND_STREAMING",
            route: `/${CC_ROUTE_PREFIX}/policy/edge-connector-log-and-control-forwarding-control`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_DNS",
            route: `/${CC_ROUTE_PREFIX}/policy/edge-connector-dns-control`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                ? "hidden"
                : "visible",
          });
        });

        b.Group("ZERO_TRUST_BRANCH", () => {
          b.Menu({
            key: "SEGMENTATION_SITES",
            route: "/segmentation/sites/gateways/isolation",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_HUBS",
            route: "/segmentation/sites/gateways/access",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_TEMPLATES",
            route: "/segmentation/templates",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_ASSETS",
            route: "/segmentation/assets",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_INSIGHTS",
            route: "/segmentation/insights",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_CHARTS",
            route: "/segmentation/charts",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_GLOBAL",
            route: "/segmentation/settings",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
          b.Menu({
            key: "SEGMENTATION_INTEGRATIONS",
            route: "/segmentation/setting-integration",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              isZtdsMenuHidden(pai) ? "hidden" : "visible",
          });
        });
      },
    );

    b.Group(
      { key: "MM_CONNECTOR_VTAB_CLOUD", icon: "fa-regular fa-cloud" },
      () => {
        b.Group("CC_MANAGEMENT", () => {
          b.Menu({
            key: "CC_PROVISIONING",
            route: `/${CC_ROUTE_PREFIX}/administration/provisioning-templates?filter=CC`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, [
                "EDGE_CONNECTOR_CLOUD_PROVISIONING",
                "EDGE_CONNECTOR_TEMPLATE",
              ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_DEPLOYMENT_TEMPLATES",
            route: `/${CC_ROUTE_PREFIX}/administration/deployment-templates`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_CLOUD_CONNECTOR_GROUPS",
            route: `/${CC_ROUTE_PREFIX}/administration/cloud-connector-groups`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "CC_CLOUD_CONNECTOR_DETAILS",
            route: `/${CC_ROUTE_PREFIX}/dashboard/connector-monitoring?filter=CC`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_DASHBOARD"])
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "CC_PARTNER_INTEGRATIONS",
            route: `/${CC_ROUTE_PREFIX}/administration/partner-integrations/aws`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, [
                "EDGE_CONNECTOR_CLOUD_PROVISIONING",
                "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT",
              ])
                ? "hidden"
                : "visible",
          });
        });
        b.Group("CC_TRAFFIC_STEERING", () => {
          b.Menu({
            key: "CC_FWD_POLICY",
            route: `/${CC_ROUTE_PREFIX}/policy/cloud-connector-traffic-forwarding-control`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_LOG_AND_STREAMING",
            route: `/${CC_ROUTE_PREFIX}/policy/cloud-connector-log-and-control-forwarding-control`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                ? "hidden"
                : "visible",
          });

          b.Menu({
            key: "CC_DNS_FOR_VM",
            route: `/${CC_ROUTE_PREFIX}/policy/cloud-connector-dns-control`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                ? "hidden"
                : "visible",
          });
        });
        b.Group("CC_CLOUD_CONFIGURATION", () => {
          b.Menu({
            key: "CC_NSS",
            route: "/ec/administration/nss-settings-servers",
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_NSS_CONFIGURATION"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "CC_ADVANCED_SETTINGS",
            route: `/${CC_ROUTE_PREFIX}/administration/cloud-configuration-advanced-settings`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, ["EDGE_CONNECTOR_ADMIN_MANAGEMENT"])
                ? "hidden"
                : "visible",
          });
        });
        b.Group("CC_ZERO_TRUST_GATEWAY", () => {
          b.Menu({
            key: "CC_ZERO_TRUST_GATEWAY",
            route: `/${CC_ROUTE_PREFIX}/administration/zero-trust-gateway`,
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              CcNavUtils(pai, [
                "EDGE_CONNECTOR_CLOUD_PROVISIONING",
                "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT",
              ])
                ? "hidden"
                : "visible",
          });
        });
        b.Group("MICROSEGMENTATION", () => {
          b.Menu({
            key: "MICROSEGMENTATION_AGENTS",
            route: "/microsegmentation/agent",
            visibility: ({ can }: ProductAccessInfoWithDevFlags) =>
              !can("showZMS") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MICROSEGMENTATION_AGENT_GROUPS",
            route: `/microsegmentation/agent-group`,
            visibility: ({ can }: ProductAccessInfoWithDevFlags) =>
              !can("showZMS") ? "hidden" : "visible",
          });
          b.Menu({
            key: "MICROSEGMENTATION_AGENT_PROVISIONING",
            route: `/microsegmentation/agent-provisioning`,
            visibility: ({ can }: ProductAccessInfoWithDevFlags) =>
              !can("showZMS") ? "hidden" : "visible",
          });
        });
      },
    );
  });

  b.Group(
    { key: "nav.infrastructure.common-resources", icon: "fa-objects-column" },
    () => {
      b.Group(
        {
          key: "MM_COMMON_RESOURCES_VTAB_GATEWAYS",
          icon: "fa-regular fa-server",
        },
        () => {
          b.Group("MM_COMMON_RESOURCES_VTAB_GATEWAYS", () => {
            b.Menu({
              key: "CC_ZIA",
              route: `/${CC_ROUTE_PREFIX}/administration/gateways`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "CC_LOG_AND_CONTROL",
              route: `/${CC_ROUTE_PREFIX}/administration/log-and-control-gateways`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "CC_DNS",
              route: `/${CC_ROUTE_PREFIX}/administration/dns-gateways`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                  ? "hidden"
                  : "visible",
            });
          });
        },
      );

      b.Group(
        {
          key: "MM_COMMON_RESOURCES_VTAB_APPLICATION",
          icon: "fa-regular fa-list",
        },
        () => {
          b.Group("TN_NETWORKING_CR_CA", () => {
            b.Menu({
              key: "MMC_INFRA_CC_CA_IP_BASED",
              route: "/ma/admin/application-bypass?tab=IP-Based",
              visibility: ({
                features,
                entitlements,
              }: ProductAccessInfoWithDevFlags) =>
                maHideNavLink(
                  entitlements,
                  features,
                  "networking.applicationBypass.ipBased",
                )
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "MMC_INFRA_CC_CA_PROCESS_BASED",
              route: "/ma/admin/application-bypass?tab=PROCESS_BASED",
              visibility: ({
                features,
                entitlements,
              }: ProductAccessInfoWithDevFlags) =>
                maHideNavLink(
                  entitlements,
                  features,
                  "networking.applicationBypass.processBased",
                )
                  ? "hidden"
                  : "visible",
            });
          });
          b.Group("CC_IPGROUP_FQDN", () => {
            b.Menu({
              key: "CC_SRC_IPGROUP",
              route: `/${CC_ROUTE_PREFIX}/administration/source-ip-groups`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "CC_DEST_IPGROUP",
              route: `/${CC_ROUTE_PREFIX}/administration/destination-ip-groups`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "CC_IP_POOL",
              route: `/${CC_ROUTE_PREFIX}/administration/ip-pool`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_FORWARDING"])
                  ? "hidden"
                  : "visible",
            });
          });
        },
      );

      // b.Menu({
      //   key: "",
      //   route: "",
      //   visibility: ({}: ProductAccessInfoWithDevFlags) => "visible",
      // });
      b.Group(
        {
          key: "MM_CLIENT_CONNECTOR_VTAB_DEPLOYMENT",
          icon: "fa-regular fa-cloud-arrow-up",
        },
        () => {
          b.Group("MM_COMMON_RESOURCES_VTAB_CC_DEPLOYMENT", () => {
            b.Menu({
              key: "MMC_INFRA_CC_DEPLOY_FRO",
              route: "/ma/admin/client-connector-app-store?tab=UPDATE_SETTINGS",
              visibility: ({
                features,
                entitlements,
              }: ProductAccessInfoWithDevFlags) =>
                maHideNavLink(entitlements, features, "")
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "MMC_INFRA_CC_DEPLOY_RD",
              route:
                "/ma/admin/client-connector-app-store?tab=REGISTERED_DEVICES",
              visibility: ({
                features,
                entitlements,
              }: ProductAccessInfoWithDevFlags) =>
                maHideNavLink(entitlements, features, "")
                  ? "hidden"
                  : "visible",
            });
            b.Menu({
              key: "MMC_INFRA_CC_DEPLOY_PR",
              route: "/ma/admin/client-connector-app-store?tab=NEW_RELEASES",
              visibility: ({
                features,
                entitlements,
              }: ProductAccessInfoWithDevFlags) =>
                maHideNavLink(entitlements, features, "")
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "MMC_INFRA_CC_DEPLOY_ZDXR",
              route: "/ma/admin/client-connector-app-store?tab=ZDX_MODULE",
              visibility: ({
                features,
                entitlements,
              }: ProductAccessInfoWithDevFlags) =>
                maHideNavLink(entitlements, features, "networking.zdxModule")
                  ? "hidden"
                  : "visible",
            });

            b.Menu({
              key: "CC_APP_STORE_FOR_VDI",
              route: `/${CC_ROUTE_PREFIX}/administration/vdi/agent-app/ga`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                  ? "hidden"
                  : "visible",
            });
          });

          b.Group("CC_AND_EDGE_VM_DEPLOYMENT", () => {
            b.Menu({
              key: "CC_CONNECTION_TEMPLATE",
              route: `/${CC_ROUTE_PREFIX}/administration/deployment-templates`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                  ? "hidden"
                  : "visible",
            });
          });
          b.Group("MMC_INFRA_BRANCH_CONNECTOR", () => {
            b.Menu({
              key: "CC_VM_IMAGES",
              route: `/${CC_ROUTE_PREFIX}/administration/branch-connector-images`,
              visibility: (pai: ProductAccessInfoWithDevFlags) =>
                CcNavUtils(pai, ["EDGE_CONNECTOR_CLOUD_PROVISIONING"])
                  ? "hidden"
                  : "visible",
            });
          });
        },
      );
    },
  );
};
