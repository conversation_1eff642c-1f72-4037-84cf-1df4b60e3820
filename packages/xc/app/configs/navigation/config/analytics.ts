/* eslint-disable @up/unified-platform/max-file-lines */
import { type NavigationBuilder } from "@up/navigation";

import { Visibility } from "@up/navigation/src/core";
import { ZIA_ROUTE_PREFIX } from "@zia/rbac";
import { ZDX_ROUTE_PREFIX } from "./administration";
import { type ProductAccessInfoWithDevFlags } from "@/configs/navigation/types";
import {
  getCCNavItemState,
  getDPNavItemState,
  getNavItemState,
  getNetworkingNavItemState,
  getRisk360HiddenNav,
  getRisk360SubscriptionState,
  getZCCNavItemState,
  getZIANavItemState,
} from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnaltyicsNavUtils";
import { PRIVILEGES } from "@/utils/zdxHelper";
import {
  isZPALocalScopeAdmin,
  isZtdsMenuHidden,
  ZdxNavDisabledSubUtils,
  ZdxNavEnabledSubUtils,
  ZdxNavUtils,
  ZiaNavUtils,
  ZpaFeatureFlag,
} from "@/utils/GlobalNavigationUtils";
import { maHideNavLink } from "@/utils/maUtils";

export const drawAnalyticsBuilder = (
  b: NavigationBuilder<ProductAccessInfoWithDevFlags>,
) => {
  b.Container(() => {
    b.Group(
      {
        key: "LM_ANALYTICS_NETWORKING",
        icon: "fa-regular fa-arrow-down-arrow-up",
        route: "/analytics/networking",
        visibility: (pai: ProductAccessInfoWithDevFlags) => {
          if (pai.loading) {
            return Visibility.LOADING;
          }

          return getNetworkingNavItemState(pai) ? "hidden" : "visible";
        },
      },
      () => {
        b.Menu({
          key: "LM_ANALYTICS_NETWORKING_CONNECTOR_ACTIVITY",
          route: "/analytics/networking/connector-activity",
          visibility: (props: ProductAccessInfoWithDevFlags) => {
            if (props.loading) {
              return Visibility.LOADING;
            }

            return !getCCNavItemState(props, "branch-cloud-connectors")
              ? "hidden"
              : "visible";
          },
        });
      },
    );

    b.Group(
      {
        key: "RISK",
        route: "/analytics/risk360",
        icon: "fa-solid fa-shield-quartered",
        visibility: (pai: ProductAccessInfoWithDevFlags) => {
          if (pai.loading) {
            return Visibility.LOADING;
          }

          const { can } = pai;

          return !can("showRisk360") || getRisk360SubscriptionState(pai)
            ? "hidden"
            : "visible";
        },
      },
      () => {
        b.Menu({
          key: "LM_RISK_FACTORS",
          route: "/analytics/risk360/factors",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            getRisk360HiddenNav(pai, "factors") ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_RISK_ASSETS",
          route: "/analytics/risk360/assets",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            getRisk360HiddenNav(pai, "assets") ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_RISK_INSIGHTS",
          route: "/analytics/risk360/insights",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            getRisk360HiddenNav(pai, "insights") ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_RISK_FINANCIAL_RISK",
          route: "/analytics/risk360/financial-risk",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            getRisk360HiddenNav(pai, "financialRisk") ? "hidden" : "visible",
        });

        b.Group("MK_RISK360_FRAMEWORKS", () => {
          b.Menu({
            key: "LM_RISK_MITRE",
            route: "/analytics/risk360/mitre-framework",
            options: {
              disabledTooltip: "RISK360_ADV_SUB_MSG",
            },
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              getRisk360HiddenNav(pai, "mitreAttack") ? "disabled" : "hidden",
          });

          b.Menu({
            key: "LM_RISK_NIST",
            route: "/analytics/risk360/nist-framework",
            options: {
              disabledTooltip: "RISK360_ADV_SUB_MSG",
            },
            visibility: (pai: ProductAccessInfoWithDevFlags) =>
              getRisk360HiddenNav(pai, "nistCsf") ? "disabled" : "hidden",
          });
        });
        b.Menu({
          key: "LM_RISK_REPORTS",
          route: "/analytics/risk360/reports",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            getRisk360HiddenNav(pai, "reports") ? "hidden" : "visible",
        });
      },
    );

    b.Group(
      {
        key: "LM_ANALYTICS_DE_DE",
        route: "/analytics/digital-experience",
        icon: "fa-regular fa-laptop",
        visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) => {
          if (productAccessInfo.loading) {
            return Visibility.LOADING;
          }

          return getNavItemState(
            productAccessInfo,
            "DigitalExperience.Dashboard",
          )
            ? "hidden"
            : "visible";
        },
      },
      () => {
        b.Menu({
          key: "LM_ANALYTICS_DE_ACTIVITY",
          route: "/analytics/digital-experience/activity",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getNavItemState(productAccessInfo, "DigitalExperience.Activity")
              ? "hidden"
              : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_DE_APPLICATIONS",
          route: "/analytics/digital-experience/applications",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getNavItemState(productAccessInfo, "DigitalExperience.Applications")
              ? "hidden"
              : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_DE_INCIDENTS",
          route: "/analytics/digital-experience/incidents",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getNavItemState(productAccessInfo, "DigitalExperience.Incidents")
              ? "hidden"
              : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_DE_SELF_SVC",
          route: "/analytics/digital-experience/self-service",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getNavItemState(productAccessInfo, "DigitalExperience.SelfService")
              ? "hidden"
              : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_DE_MEETINGS",
          route: "/analytics/digital-experience/meetings",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getNavItemState(productAccessInfo, "DigitalExperience.Meetings")
              ? "hidden"
              : "visible",
        });
      },
    );

    b.Group(
      {
        key: "LM_ANALYTICS_CS_CS",
        route: "/analytics/cybersecurity",
        icon: "fa-regular  fa-chart-network",
        visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) => {
          if (productAccessInfo.loading) {
            return "loading";
          }

          return getZIANavItemState(productAccessInfo) ? "hidden" : "visible";
        },
      },
      () => {
        b.Menu({
          key: "LM_ANALYTICS_CS_TRANS_ACTIVITY",
          route: "/analytics/cybersecurity/transactional-activity",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getZIANavItemState(productAccessInfo) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_CS_ADV_THREATS",
          route: "/analytics/cybersecurity/advanced-threat",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getZIANavItemState(productAccessInfo) ? "hidden" : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_CS_SBOX_THREATS",
          route: "/analytics/cybersecurity/sandbox-threat",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getZIANavItemState(productAccessInfo) ? "hidden" : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_CS_THREATS_LOCS",
          route: "/analytics/cybersecurity/threat-location",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getZIANavItemState(productAccessInfo) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_CS_TLS_INSPECT",
          route: "/analytics/cybersecurity/ssl-inspection",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getZIANavItemState(productAccessInfo) ? "hidden" : "visible",
        });
      },
    );

    b.Group(
      {
        key: "LM_ANALYTICS_DATA_PROTECTION",
        icon: "fa-regular fa-database",
        route: "/analytics/data-protection",
        visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) => {
          if (productAccessInfo.loading) {
            return "loading";
          }

          return getDPNavItemState() &&
            productAccessInfo.can("showDataProtection")
            ? "visible"
            : "hidden";
        },
      },
      () => {
        b.Menu({
          key: "LM_ANALYTICS_DATA_CHANNELS",
          route: "/analytics/data-protection/data-channels?tab=inline",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) => {
            if (productAccessInfo.loading) {
              return "loading";
            }

            return getDPNavItemState() &&
              productAccessInfo.can("showDataProtection")
              ? "visible"
              : "hidden";
          },
        });
      },
    );

    b.Group(
      {
        key: "LM_ANALYTICS_OPERATIONAL_OPERATIONAL",
        icon: "fa-regular fa-gauge-high",
        visibility: (props: ProductAccessInfoWithDevFlags) => {
          if (props.loading) {
            return Visibility.LOADING;
          }

          return getZCCNavItemState(props) &&
            !getCCNavItemState(props, "branch-cloud-connectors")
            ? "hidden"
            : "visible";
        },
      },
      () => {
        b.Menu({
          key: "LM_ANALYTICS_OPERATIONAL_DEVICES",
          route: "/analytics/operational/devices",
          visibility: (productAccessInfo: ProductAccessInfoWithDevFlags) =>
            getZCCNavItemState(productAccessInfo) ? "hidden" : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_OPERATIONAL_APPLIANCES",
          route: "/analytics/operational/appliances",
          visibility: (props: ProductAccessInfoWithDevFlags) =>
            !getCCNavItemState(props, "branch-cloud-connectors")
              ? "hidden"
              : "visible",
        });
      },
    );

    b.Menu({
      key: "LM_ANALYTICS_COPILOT",
      route: (pai?: ProductAccessInfoWithDevFlags) =>
        pai && pai.can("copilotDynamicRoutePrefix")
          ? "/analytics/copilot"
          : `/${ZDX_ROUTE_PREFIX}/copilot`,

      icon: "fa-solid fa-wand-magic-sparkles",
      visibility: ({
        // features,
        // roles,
        loading,
        entitlements,
        // eslint-disable-next-line arrow-body-style
      }: ProductAccessInfoWithDevFlags) => {
        if (loading) {
          return "loading";
        }

        // return ZdxNavUtils(roles, [
        //   PRIVILEGES.ZDX_COPILOT_READ,
        //   PRIVILEGES.ZDX_COPILOT_WRITE,
        // ]) ||
        //   ZdxNavDisabledSubUtils(features, [
        //     "Z_ZDX_M365",
        //     "Z_ZDX_STD",
        //     " ZDX_ADVANCED",
        //   ])
        //   ? "hidden"
        //   : "visible";

        return !entitlements.copilot ? "hidden" : "visible";
      },
    });
  });
  b.Container(() => {
    b.Group({ key: "MM_ACCCTRL_VTAB_INET_SASS", icon: "fa-cloud" }, () => {
      b.Group({ key: "menu.Dashboard" }, () => {
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_WEB_OVERVIEW",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/1`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.web")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_SECURITY",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/2`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.web")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_BROWSING",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/3`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.web")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_CLOUD_APPS",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/4`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.web")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_MOBILE_APPS",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/5`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.mobile")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_FW_OVERVIEW",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/7`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.7")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_DNS_OVERVIEW",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/8`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.8")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_FW_OVERVIEW",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/9`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.9")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_BANDWIDTH_CTRL",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/10`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.web")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_O365",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/11`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.web")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_IPS_OVERVIEW",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/12`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.12")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_ENDPOINT_DLP",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/13`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.13")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_EMAIL_DLP",
          route: `/${ZIA_ROUTE_PREFIX}#dashboard/14`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("dashboard.14")(pia) ? "hidden" : "visible",
        });
      });
      b.Group({ key: "menu.Analytics" }, () => {
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_CS_INSIGHTS",
          route: `/${ZIA_ROUTE_PREFIX}#cybersecurity-insights`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.cybersecurityInsights")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_INTERACTIVE_RPTS",
          route: `/${ZIA_ROUTE_PREFIX}#reports`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.interactiveReports")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_RISK_SCORE_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#riskscore-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.companyRiskScoreReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_PEER_COMPARISON",
          route: `/${ZIA_ROUTE_PREFIX}#peer-comparison-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.industryPeerComparison")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_SYSTEM_AUDIT_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#system-audit-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.systemAuditReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_SEC_POLICY_AUDIT_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#security-audit-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.securityPolicyAuditReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_SBOX_ACTIVITY_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#sandbox-activity-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.sandboxActivityReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_QBR_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#quarterly-business-review-reports`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.qbrReports")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_ASSET_SUM_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#casb-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.saasAssetSummaryReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_SEC_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#saas-security-api-all-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.saasSecurityReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_CFG_RISK_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#configuration-risk-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.configurationRiskReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_IOT_DISC_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#iot-discovery-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.iotDiscoveryReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_THREAT_INSIGHTS",
          route: `/${ZIA_ROUTE_PREFIX}#insights/threat`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.threatInsights")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_DATA_DISC_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#data-discovery-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.dataDiscoveryReport")(pia)
              ? "hidden"
              : "visible",
        });

        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_EPDLP_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#endpoint-dlp-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.endpointDlpReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_EMAIL_SEC_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#email-dlp-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.emailDlpReport")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_GENAI_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#generative-ai-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.generativeAiReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_SASS_ATTACK_SFC_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#attack-surface-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.attackSurfaceReport")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_ANOMALY_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#anomaly-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.anomalyReport")(pia) ? "hidden" : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_INST_DISC_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#instance-discovery-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.instanceDiscovery")(pia)
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "LM_ANALYTICS_RPTS_INET_EXEC_INSIGHT_RPT",
          route: `/${ZIA_ROUTE_PREFIX}#exec-insights-report`,
          visibility: (pia: ProductAccessInfoWithDevFlags) =>
            ZiaNavUtils("analytics.execInsightsReport")(pia)
              ? "hidden"
              : "visible",
        });
        // b.Menu({
        //   key: "",
        //   route: ``,
        //   visibility: (pia: ProductAccessInfoWithDevFlags) => "visible",
        // });
      });
    });
    b.Group({ key: "menu.PrivateApplications", icon: "fa-window" }, () => {
      b.Menu({
        key: "REPORTS_PA_APPS",
        route: "/private#dashboard/appsDashboard",
        visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
          entitlements.zpa ? "visible" : "hidden",
      });
      b.Menu({
        key: "REPORTS_PA_USERS",
        route: "/private#dashboard/usersDashboard",
        visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
          entitlements.zpa ? "visible" : "hidden",
      });
      b.Menu({
        key: "REPORTS_PA_HEALTH",
        route: "/private#dashboard/health",
        visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
          entitlements.zpa ? "visible" : "hidden",
      });
      b.Menu({
        key: "REPORTS_PA_APP_CNCTR",
        route: "/private#dashboard/connectorDashboard",
        visibility: ({ entitlements }: ProductAccessInfoWithDevFlags) =>
          entitlements.zpa ? "visible" : "hidden",
      });
      b.Menu({
        key: "REPORTS_PA_PSE",
        route: "/private#dashboard/privateServiceEdgeDashboard",
        visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
          ZpaFeatureFlag(features, "feature.pse.visualization")
            ? "visible"
            : "hidden",
      });
      b.Menu({
        key: "REPORTS_PA_PCC",
        route: "/private#dashboard/privateCloudControllersDashboard",
        visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
          ZpaFeatureFlag(features, "feature.ddil.config")
            ? "visible"
            : "hidden",
      });
      b.Group({ key: "menu.Security" }, () => {
        b.Menu({
          key: "REPORTS_PA_SECURITY",
          route: "/private#dashboard/securityDashboard/appProtection",
          visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
            isZPALocalScopeAdmin(features) ||
            !ZpaFeatureFlag(features, "ui.waf")
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "REPORTS_PA_SECURITY_BROWSER_SESSION_PROTECTION",
          route: "/private#dashboard/securityDashboard/browserProtection",
          visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
            isZPALocalScopeAdmin(features) ||
            !ZpaFeatureFlag(features, "feature.csp")
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "REPORTS_PA_SECURITY_API_PROTECTION",
          route: "/private#dashboard/securityDashboard/apiProtection",
          visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
            isZPALocalScopeAdmin(features) ||
            !(
              ZpaFeatureFlag(features, "ui.waf") &&
              ZpaFeatureFlag(features, "feature.api_protection")
            )
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "REPORTS_PA_SECURITY_PROTOCOL_DISCOVERY",
          route: "/private#dashboard/securityDashboard/autoDetect",
          visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
            isZPALocalScopeAdmin(features) ||
            !(
              ZpaFeatureFlag(features, "ui.waf") &&
              ZpaFeatureFlag(features, "feature.ptag")
            )
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "REPORTS_PA_SECURITY_AD_PROTECTION",
          route: "/private#dashboard/securityDashboard/adProtection",
          visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
            isZPALocalScopeAdmin(features) ||
            !(
              ZpaFeatureFlag(features, "ui.waf") &&
              ZpaFeatureFlag(features, "feature.adp") &&
              window.config.enableADDashboard
            )
              ? "hidden"
              : "visible",
        });
      });
      b.Menu({
        key: "REPORTS_PA_SRCIP_ANCHORING",
        route: "/private#dashboard/sipa",
        visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
          isZPALocalScopeAdmin(features) ? "hidden" : "visible",
      });
      b.Menu({
        key: "REPORTS_PA_EXTRANET",
        route: "/private#dashboard/extranet",
        visibility: ({ features }: ProductAccessInfoWithDevFlags) =>
          isZPALocalScopeAdmin(features) ||
          !ZpaFeatureFlag(features, "feature.extranet")
            ? "hidden"
            : "visible",
      });
    });
    b.Group({ key: "menu.ClientConnector", icon: "fa-plug" }, () => {
      b.Group({ key: "menu.Dashboard" }, () => {
        b.Menu({
          key: "REPORTS_CC_DASHBOARD",
          route: "/ma/dashboard/platform-details",
          visibility: ({
            features,
            entitlements,
          }: ProductAccessInfoWithDevFlags) =>
            maHideNavLink(entitlements, features, "") ? "hidden" : "visible",
        });
        b.Menu({
          key: "REPORTS_CC_DEVICE_EVENTS",
          route: "/ma/dashboard/device-posture",
          visibility: ({
            features,
            entitlements,
          }: ProductAccessInfoWithDevFlags) =>
            maHideNavLink(entitlements, features, "dashboard.postureFailure")
              ? "hidden"
              : "visible",
        });
      });
    });
    b.Group(
      { key: "menu.DigitalExperienceManagement", icon: "fa-laptop" },
      () => {
        b.Group({ key: "menu.Dashboard" }, () => {
          b.Menu({
            key: "REPORTS_DEM_PERF_DASHBOARD",
            route: `/${ZDX_ROUTE_PREFIX}/dashboard`,
            visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [
                PRIVILEGES.ZDX_DASHBOARD_PERF_READ,
                PRIVILEGES.ZDX_UCAAS_MEETING_READ,
                PRIVILEGES.ZDX_UCAAS_APPLICATION_READ,
              ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_PERF_INCIDENTS_DASH",
            route: `/${ZDX_ROUTE_PREFIX}/incidents`,
            visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DASHBOARD_PERF_READ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_PERF_SELFSERV_DASH",
            route: `/${ZDX_ROUTE_PREFIX}/self-service-it`,
            visibility: ({ features, roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [
                PRIVILEGES.ZDX_SELF_SERVICE_IT_READ,
                PRIVILEGES.ZDX_SELF_SERVICE_IT_WRITE,
              ]) ||
              ZdxNavDisabledSubUtils(features, [
                "Z_ZDX_M365",
                "Z_ZDX_STD",
                "ZDX_ADVANCED",
              ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_PERF_WIFI_DASH",
            route: `/${ZDX_ROUTE_PREFIX}/wifi`,
            visibility: ({ features, roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [
                PRIVILEGES.ZDX_WIFI_DASHBOARD_READ,
                PRIVILEGES.ZDX_WIFI_DASHBOARD_WRITE,
              ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
        });
        b.Menu({
          key: "REPORTS_DEM_NI_DASH",
          route: `/${ZDX_ROUTE_PREFIX}/network-intelligence`,
          visibility: ({
            features,
            roles,
            loading,
            can,
          }: ProductAccessInfoWithDevFlags) => {
            if (loading) return "loading";
            // TODO: add new badge when its ready

            return !can("showNetworkIntelligence") ||
              ZdxNavUtils(roles, [
                PRIVILEGES.ZDX_NI_DASHBOARD_READ,
                PRIVILEGES.ZDX_NI_DASHBOARD_WRITE,
              ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
              ? "hidden"
              : "visible";
          },
        });
        b.Menu({
          key: "REPORTS_DEM_APPLICATIONS",
          route: `/${ZDX_ROUTE_PREFIX}/applications`,
          visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
            ZdxNavUtils(roles, [
              PRIVILEGES.ZDX_DASHBOARD_APP_OVERVIEW_READ,
              PRIVILEGES.ZDX_UCAAS_MEETING_READ,
              PRIVILEGES.ZDX_UCAAS_APPLICATION_READ,
            ])
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "REPORTS_DEM_USERS",
          route: `/${ZDX_ROUTE_PREFIX}/users`,
          visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
            ZdxNavUtils(roles, [PRIVILEGES.ZDX_DASHBOARD_USER_OVERVIEW_READ])
              ? "hidden"
              : "visible",
        });
        b.Menu({
          key: "REPORTS_DEM_USR_DEV_SEARCH",
          route: `/${ZDX_ROUTE_PREFIX}/user-search`,
          visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
            ZdxNavUtils(roles, [
              PRIVILEGES.ZDX_USER_NAME_ACCESS_READ,
              PRIVILEGES.ZDX_DASHBOARD_USER_DASHBOARD_READ,
            ])
              ? "hidden"
              : "visible",
        });
        b.Group({ key: "menu.Inventory" }, () => {
          b.Menu({
            key: "REPORTS_DEM_INV_SOFTWARE_OVERVIEW",
            route: `/${ZDX_ROUTE_PREFIX}/software-overview`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_INV_SOFTWARE_INVENTORY",
            route: `/${ZDX_ROUTE_PREFIX}/software-inventory`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_INV_SOFTWARE_PATCH_OVERVIEW",
            route: `/${ZDX_ROUTE_PREFIX}/software-patch-inventory`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavUtils(roles, [
                PRIVILEGES.ZDX_DEVICE_SOFTWARE_PATCH_INVENTORY_READ,
              ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_INV_PROCESSING_OVERVIEW",
            route: `/${ZDX_ROUTE_PREFIX}/process-overview`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavEnabledSubUtils(features, ["ZDX_ADV_PLUS"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_INV_PROCESS_INV",
            route: `/${ZDX_ROUTE_PREFIX}/process-inventory`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavEnabledSubUtils(features, ["ZDX_ADV_PLUS"])
                ? "hidden"
                : "visible",
          });
        });
        b.Group({ key: "menu.Device" }, () => {
          b.Menu({
            key: "REPORTS_DEM_DEVICE_OVERVIEW",
            route: `/${ZDX_ROUTE_PREFIX}/device-overview`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_DEVICE_INV",
            route: `/${ZDX_ROUTE_PREFIX}/device-inventory`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_DEVICE_INVENTORY_READ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
        });
        b.Menu({
          key: "REPORTS_DEM_DIAG",
          route: `/${ZDX_ROUTE_PREFIX}/diagnostics`,
          visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
            ZdxNavUtils(roles, [PRIVILEGES.ZDX_LTSESSION_READ]) ||
            ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
              ? "hidden"
              : "visible",
        });
        b.Group({ key: "menu.Reporting" }, () => {
          b.Menu({
            key: "REPORTS_DEM_RPT_SYSTEM_GENERATED",
            route: `/${ZDX_ROUTE_PREFIX}/scheduled-reports`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_STATIC_REPORT_READ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_RPT_QBR",
            route: `/${ZDX_ROUTE_PREFIX}/business-review-reports`,
            visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_QBR_READ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_RPT_ZS_HOSTED",
            route: `/${ZDX_ROUTE_PREFIX}/zprobe-analytics`,
            visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_ZPROBE_ANALYTICS_READ])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_RPT_DATA_EXP",
            route: `/${ZDX_ROUTE_PREFIX}/data-explorer`,
            visibility: ({ roles, features }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_QUERY_REPORT_READ]) ||
              ZdxNavDisabledSubUtils(features, ["Z_ZDX_M365", "Z_ZDX_STD"])
                ? "hidden"
                : "visible",
          });
          b.Menu({
            key: "REPORTS_DEM_RPT_ZDX_SNAPSHOTS",
            route: `/${ZDX_ROUTE_PREFIX}/snapshot-management`,
            visibility: ({ roles }: ProductAccessInfoWithDevFlags) =>
              ZdxNavUtils(roles, [PRIVILEGES.ZDX_LIVE_LINKS_READ])
                ? "hidden"
                : "visible",
          });
        });
      },
    );
    b.Group(
      { key: "menu.DeviceSegmentation", icon: "fa-network-wired" },
      () => {
        b.Menu({
          key: "SEGMENTATION_INSIGHTS",
          route: "/segmentation/existing-insights",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            isZtdsMenuHidden(pai) ? "hidden" : "visible",
        });
        b.Menu({
          key: "SEGMENTATION_CHARTS",
          route: "/segmentation/existing-charts",
          visibility: (pai: ProductAccessInfoWithDevFlags) =>
            isZtdsMenuHidden(pai) ? "hidden" : "visible",
        });
      },
    );
    b.Group({ key: "menu.Microsegmentation", icon: "fa-chart-network" }, () => {
      b.Group({ key: "menu.Dashboard" }, () => {
        b.Menu({
          key: "MICROSEGMENTATION_FLOW",
          route: "/microsegmentation/dashboard/flow",
          visibility: ({ can }: ProductAccessInfoWithDevFlags) =>
            !can("showZMS") ? "hidden" : "default",
        });
        b.Menu({
          key: "MICROSEGMENTATION_AGENT",
          route: "/microsegmentation/dashboard/agent",
          visibility: ({ can }: ProductAccessInfoWithDevFlags) =>
            !can("showZMS") ? "hidden" : "default",
        });
        b.Menu({
          key: "MICROSEGMENTATION_APPLICATION_MAP",
          route: "/microsegmentation/dashboard/application-map",
          visibility: ({ can }: ProductAccessInfoWithDevFlags) =>
            !can("showZMS") ? "hidden" : "default",
        });
      });
    });
  }, 1);
};
