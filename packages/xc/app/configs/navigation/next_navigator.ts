import { type RouteFn, type NavigatorOptions } from "@up/navigation";
import * as routerReducerTypes from "next/dist/client/components/router-reducer/router-reducer-types";
import { type AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export class NextNavigatorImplementation<T> {
  private _instance: AppRouterInstance;
  private _resolver: (route: string | RouteFn<T>, data?: T) => string;
  private _prefetched: string[];

  constructor(
    instance: AppRouterInstance,
    resolver: (route: string | RouteFn<T>, data?: T) => string,
  ) {
    this._instance = instance;
    this._resolver = resolver;
    this._prefetched = [];
  }

  back(): void {
    this._instance.back();
  }

  forward(): void {
    this._instance.forward();
  }

  refresh(): void {
    this._instance.refresh();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  push(href: string, options?: NavigatorOptions): void {
    if (options?.external) {
      window.open(href, "_blank");
    } else {
      this._instance.push(href, { scroll: false });
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  replace(href: string, options?: NavigatorOptions): void {
    this._instance.replace(href, { scroll: false });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  prefetch(href: string): void {
    if (!this._prefetched.includes(href)) {
      this._prefetched.push(href);
      this._instance.prefetch(href, {
        kind: routerReducerTypes.PrefetchKind.AUTO,
      });
    }
  }
}
