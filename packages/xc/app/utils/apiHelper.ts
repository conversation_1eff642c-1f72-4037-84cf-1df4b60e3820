/* eslint-disable @up/unified-platform/max-file-lines */
import { BEARER_TOKEN, BEARER_TOKEN_EXPIRY, HOUR_MS, MINUTE_MS } from "@up/std";
import { getSessionInfo } from "@up/std";
import { handleLogout } from "./auth/handleLogout";
import {
  getCloudList,
  getMultiCloudStatus,
  getSelectedZiaCloud,
  getServiceTypeFromUrl,
  isZInsightsEndpoint,
  isZUXPService,
} from "./multiCloudConfig";
import environment from "@/utils/environment";

const UNKNOWN_ERROR_MSG = "Unknown Error occured";

// export type gateWayType = "zsapi" | "api";

type ApiHelperQueryParams = Record<
  string,
  string | number | boolean | string[] | number[] | boolean[]
>;

export type BatchResponse = {
  isLoading: boolean;
  data: object[];
  error: Error;
  mutate: (object?: object) => void;
  isValidating: boolean;
};

export const getTenantCldValue = (
  url: string,
  serviceType?: string,
): Record<string, string> => {
  const subsystem = serviceType ?? getServiceTypeFromUrl(url) ?? "zia";
  const cloudList = getCloudList();
  const selectedCloud = cloudList?.[subsystem]?.find((item) =>
    subsystem === "zdx"
      ? item?.ziaCloudName === getSelectedZiaCloud()
      : item.cloudName === getSelectedZiaCloud(),
  );
  const zdxTenant = selectedCloud
    ? `${selectedCloud?.ziaCloudName}.${selectedCloud?.tenantId}`
    : "";
  const headers: Record<string, string> = {};
  const headerType = isZInsightsEndpoint(url) ? `x-zinsights` : `x-zscaler`;

  if (isZUXPService(url)) {
    const sessionInfo = getSessionInfo();
    const cld = sessionInfo?.find((item) => item.prd === "XC")?.cld ?? "0";
    headers["x-zscaler-cloud"] = cld ?? "";
  } else {
    headers[`${headerType}-cloud`] = selectedCloud?.cloudName ?? "";
    headers[`${headerType}-tenant`] =
      subsystem === "zdx" ? zdxTenant : (selectedCloud?.tenantId ?? "");
  }

  return headers;
};

export const API_ENDPOINTS = {
  ZIA: "/private/zia/zsapi/v1",
  ZIAM: "/private/ziam/admin/internal-api/v1",
  ZIAM_SAML: "/private/ziam/authn/api/v1",
  ZIAM_ADAPTIVE_ACCESS: "/private/ziam/adaptive-access",
  ZCC_ONE_UI: "/private/zcc/api/oneui/v1/zcc",
  ZCC_WEBSERVICE: "/private/zcc/webservice/api/web",
  ZCC_WEB: "/private/zcc/api/web",
  ZCC_PAPI: "/zcc/papi",
  ZDX: "/private/zdx/api/v1",
  ZUXP: "/private/zuxp/v1", // This value will be updated in the config wrapper based on the feature flag status.
  ZUXP_SERVICE: process.env.NEXT_PUBLIC_LOCAL_SERVICE
    ? "/service/zuxp/v1"
    : "/private/zuxp/v1", // This value will be updated in the config wrapper based on the feature flag status.
  ZTDS: "/private/ztds/api",
  ZPA: "/api/druidservice/zpn/aggregates",
  CLOUD_CONNECTOR: "/private/ztw/api/v1",
  RISK_360: "/private/zra/api/v1",
  Z_INSIGHTS: "/zins/graphql",
  ZPA_MGMT_API: "",
  ZUXP_LOGIN_SERVICE: "/private/zuxp/v1", // ZUXP login services do not use oneAPI, so an additional flag was added to handle this case.
  DSPM: "/private/dspm",
};

export const ONE_API_EXCEPTIONS = [
  API_ENDPOINTS.ZUXP,
  API_ENDPOINTS.ZPA,
  API_ENDPOINTS.ZUXP_SERVICE,
  `${API_ENDPOINTS.ZUXP_LOGIN_SERVICE}/access-token`,
  `${API_ENDPOINTS.ZUXP_LOGIN_SERVICE}/login-tenant`,
  `${API_ENDPOINTS.ZUXP_LOGIN_SERVICE}/login`,
  `${API_ENDPOINTS.ZUXP_LOGIN_SERVICE}/logout`,
];

export const SERVICE_TYPE = {
  zdx: "zdx",
  zia: "zia",
  zcc: "zcc",
  ztw: "ztw",
};

const getTenantHeaders = (
  url: string,
  subsystem?: string,
): Record<string, string> => {
  const serviceType = subsystem ?? getServiceTypeFromUrl(url);

  if ((serviceType && getMultiCloudStatus(serviceType)) || isZUXPService(url)) {
    return getTenantCldValue(url, serviceType);
  }

  return {};
};

export const getHeaders = (
  url: string,
  subsystem?: string,
): Record<string, string> => ({
  "Content-Type": "application/json",
  Authorization: "Bearer " + getBearerToken(),
  ...getTenantHeaders(url, subsystem),
});

const getCsvHeader = (url: string): Record<string, string> => ({
  Authorization: "Bearer " + getBearerToken(),
  ...getTenantHeaders(url),
});

export const getBaseUrl = (url = ""): string => {
  const { api: apiEndpoint, app: appEndpoint } = environment.endpoints();
  const gateWayType =
    ONE_API_EXCEPTIONS.some((endpoint) => url.includes(endpoint)) &&
    !url.includes("/private/xc")
      ? "api"
      : "zsapi";
  const isLocal =
    process.env.NODE_ENV === "development" || process.env.NEXT_PUBLIC_LOCAL;
  const endpoints = {
    zsapi: isLocal ? "/zsapi" : apiEndpoint,
    api: isLocal ? "/api" : (appEndpoint ?? window.location.origin),
  };

  const result = endpoints[gateWayType] ?? endpoints.zsapi;

  return result;
};

/**
 * This response handler does not differentiate between successful and failed response.
 * Here, response is just an object that was received when the fetch call was made.
 * It can either be an object containing valid data or an error object.
 * an Error object looks like this
 * {
 error: "Not Found";
 path: "/api/v1/web/device/deviceByOSList";
 requestId: "3a3997c0-48600";
 status: 404;
 timestamp: "2024-05-31T19:56:58.606+00:00";
 }

 Please refer: https://developer.mozilla.org/en-US/docs/Web/API/Response to understand Response
 */
export const responseHandler = async (res: Response) => {
  const contentType = res.headers.get("content-type");
  const isJsonResponse = contentType === "application/json";
  /**
   * [ok] is instance method that checks if the response status is in the range of 200-299.
   * If the response is not 2XX, then we need to trigger error workflow.
   *
   * For now we are showing a generalized error message if any error occurs but later we can have granular
   * error messages for specific error codes
   *
   * We have a few cases like delete request/icons upload api calls which return empty response
   * even if thay are successfull so we check the content type for such successfull response
   */
  if (res.status === 401) {
    if (!isTokenValid()) {
      console.error("[faield fetch] 401 -", res.url ?? "unknown url");
      handleLogout();

      return Promise.reject();
    }
  }

  if (!res.ok) {
    const error = new Error(res?.statusText ?? "Unknow Error occured", {
      cause: {
        status: res.status,
        info: (isJsonResponse ? await res.json() : false) as unknown,
      },
    });

    return Promise.reject(error);
  }
  if (res.url.includes(API_ENDPOINTS.Z_INSIGHTS)) {
    const response = (await res.json()) as {
      errors: Array<{ message: string }>;
      data: unknown;
    };

    if (response?.errors) {
      const graphqlError = new Error(
        response?.errors?.[0]?.message ?? "Error occurred",
        {
          cause: {
            status: 422,
            info: response?.errors as unknown,
          },
        },
      );

      return Promise.reject(graphqlError);
    }

    return Promise.resolve(response);
  }

  return isJsonResponse || res.url.includes(API_ENDPOINTS.ZTDS)
    ? res.json()
    : true;
};

type BatchReqArg = {
  name?: string;
  url: string;
  arg?: object;
};

type ReqData = { arg?: object };

export type ResData = object & { error?: Error };

// TODO: Remove `credentials: include` after the completion of OneAPI Phase 2.
// Some ZIA endpoints currently validate the `JSESSIONID` cookie.
// To ensure the cookie is passed, `credentials: include` has been temporarily added.

// TO THE REVIEWER: The following is commented out but left as we want to come back to this later

export const getReq = (
  url: string,
  withCredentials = false,
  subsystem?: string,
): Promise<any> =>
  fetch(getBaseUrl(url) + url, {
    method: "GET",
    ...(withCredentials && { credentials: "include" }),
    headers: getHeaders(url, subsystem),
  }).then(responseHandler);

// export const tracedGetRequest = (
//   name: string,
//   url: string,
//   withCredentials = false,
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
// ): Promise<any> => {
//   const options: RequestInit = {
//     method: "GET",
//     ...(withCredentials && { credentials: "include" }),
//     headers: getHeaders(url),
//   };

//   const fetchUrl = getBaseUrl(url) + url;

//   const serviceName = process.env.NEXT_PUBLIC_TELEMETRY_SVC_NAME ?? "xc-ui";
//   const serviceVersion = process.env.NEXT_PUBLIC_BUILD_VERSION ?? "0.0.0-local";

//   // Get the current tracer
//   const tracer = trace.getTracer(serviceName, serviceVersion);

//   // Start a new span
//   return tracer.startActiveSpan(
//     name ?? "xc-unnamed-fetch",
//     async (span: Span) => {
//       try {
//         // Add attributes to the span
//         span.setAttributes({
//           "http.url": fetchUrl,
//           "http.method": options?.method ?? "GET",
//           component: "fetch",
//         });

//         // Make the fetch call
//         const response = await fetch(url, options);

//         // Record response information
//         span.setAttributes({
//           "http.status_code": response.status,
//           "http.status_text": response.statusText,
//         });

//         // Mark as successful if status is 2xx
//         if (response.ok) {
//           span.setStatus({ code: SpanStatusCode.OK });
//         } else {
//           span.setStatus({
//             code: SpanStatusCode.ERROR,
//             message: `HTTP ${response.status}`,
//           });
//         }

//         const contentType = response.headers.get("content-type");
//         const isJsonResponse = contentType === "application/json";
//         if (response.status === 401) {
//           if (!isTokenValid()) {
//             console.error(
//               "[faield fetch] 401 -",
//               response.url ?? "unknown url",
//             );
//             handleLogout();

//             return Promise.reject(new Error("unauthorized"));
//           }
//         }

//         if (!response.ok) {
//           const error = new Error(
//             response?.statusText ?? "Unknow Error occured",
//             {
//               cause: {
//                 status: response.status,
//                 info: (isJsonResponse
//                   ? await response.json()
//                   : false) as unknown,
//               },
//             },
//           );

//           return Promise.reject(error);
//         }

//         return isJsonResponse || response.url.includes(API_ENDPOINTS.ZTDS)
//           ? response.json()
//           : response;

//         // return response;
//       } catch (error) {
//         // Record the error
//         span.recordException(error as Error);
//         span.setStatus({
//           code: SpanStatusCode.ERROR,
//           message: (error as Error).message,
//         });
//         throw error;
//       } finally {
//         // End the span
//         span.end();
//       }
//     },
//   );
// };

export const getTextReq = (url: string) =>
  fetch(getBaseUrl(url) + url, {
    method: "GET",
    headers: getHeaders(url),
  }).then(async (res) => {
    // to handle status code outside 200-299
    if (!res.ok) {
      const error = new Error(res?.statusText ?? UNKNOWN_ERROR_MSG, {
        cause: {
          status: res.status,
          info: await res.text(),
        },
      });

      return Promise.reject(error);
    }

    return res.text();
  });

export const downloadReq = (url: string) =>
  fetch(getBaseUrl(url) + url, {
    method: "GET",
    headers: getHeaders(url),
    // we may need to rethink this, core framework approach should support this edge case
    // conversations are happening now
  }).then((res) => res.blob());

// TODO: Remove `credentials: include` after the completion of OneAPI Phase 2.
// Some ZIA endpoints currently validate the `JSESSIONID` cookie.
// To ensure the cookie is passed, `credentials: include` has been temporarily added.
export const postReq = (
  url: string,
  { arg }: ReqData = {},
  withCredentials = false,
  subsystem?: string,
): ResData =>
  fetch(getBaseUrl(url) + url, {
    method: "POST",
    ...(withCredentials && { credentials: "include" }),
    body: JSON.stringify(arg),
    headers: getHeaders(url, subsystem),
  }).then(responseHandler);

export const postReqPromise = (url: string, { arg }: ReqData = {}) =>
  fetch(getBaseUrl(url) + url, {
    method: "POST",
    body: JSON.stringify(arg),
    headers: getHeaders(url),
  }).then(responseHandler);

export const putReq = (url: string, { arg }: ReqData = {}): ResData =>
  fetch(getBaseUrl(url) + url, {
    method: "PUT",
    body: JSON.stringify(arg),
    headers: getHeaders(url),
  }).then(responseHandler);

export const deleteReq = (url: string, { arg }: ReqData = {}): ResData =>
  fetch(getBaseUrl(url) + url, {
    method: "DELETE",
    body: JSON.stringify(arg),
    headers: getHeaders(url),
  }).then(responseHandler);

export const postUploadReq = (url: string, { arg }: ReqData = {}): object =>
  fetch(getBaseUrl(url) + url, {
    method: "POST",
    body: arg as FormData,
    headers: getCsvHeader(url),
  }).then(responseHandler);

// Put by Id wrapper over Put Req
export const putByIdReq = (
  url: string,
  { arg }: { arg: { id: string; payload: object } },
): object => {
  const { id = "", payload } = arg;

  return putReq(`${url}/${id}`, { arg: payload });
};

// Delete by Id wrapper over Delete Req
export const deleteByIdReq = (
  url: string,
  { arg }: { arg: { id: string } },
): object => {
  const { id = "" } = arg;

  return deleteReq(`${url}/${id}`);
};

export const batchPostReq = (reqUrls: BatchReqArg[]) => {
  const responses = Promise.all(
    reqUrls.map(({ url, arg }): object => postReq(url, { arg })),
  );

  return responses;
};

export const batchGetReq = async (reqUrls: BatchReqArg[]) => {
  const responses = await Promise.allSettled(
    reqUrls.map(({ url }): object => getReq(url)),
  );

  return responses.map((result) => {
    if (result.status === "fulfilled") return result.value;

    return {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      error: result.reason,
    };
  });
};

// TO THE REVIEWER: The following is commented out but left as we want to come back to this later

// export const tracedBatchGetReq = async (reqUrls: BatchReqArg[]) => {
//   const responses = await Promise.allSettled(
//     reqUrls.map(({ url, name }): object =>
//       tracedGetRequest(name ?? "no-name", url),
//     ),
//   );

//   return responses.map((result) => {
//     if (result.status === "fulfilled") return result.value;

//     return {
//       // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
//       error: result.reason,
//     };
//   });
// };

/**
 * These are temporary changes to accommodate unique behavior of ZPA.
 * Since ZPA can not provide URL in RoleProvider, they are providing promise which will get us feature flag information.
 * We are adopting this route as inside ZUXP, every subsystem should follow common route for getting rbac / entitlements / feature flags etc.
 * It helps in defining common error handling pattern and simplifies code reading.
 * This method will not be needed once we refactor RoleProvider to support subsystem based bucketing.
 */
type BatchFeatureFlagReqArg = {
  request: (() => Promise<unknown>) | string;
  name?: string;
};

export const batchGetReqMixed = async (
  reqUrls: Array<{ url: string; type: "text" | "json" }>,
) => {
  const responses = await Promise.allSettled(
    reqUrls.map(({ url, type }): object =>
      type === "text" ? getTextReq(url) : getReq(url),
    ),
  );

  return responses.map((result) =>
    result.status === "fulfilled"
      ? result.value
      : { error: result.reason as object },
  );
};

export const batchFeatureFlagReq = async (
  requests: BatchFeatureFlagReqArg[],
) => {
  const responses = await Promise.allSettled(
    requests.map(({ request }): object =>
      typeof request === "string"
        ? // ? tracedGetRequest(name ?? "no-name", request)
          getReq(request)
        : request(),
    ),
  );

  return responses.map((result) =>
    result.status === "fulfilled"
      ? result.value
      : { error: result.reason as object },
  );
};

export const buildQueryString = (
  params: ApiHelperQueryParams,
  isEncoding = true,
): string =>
  Object.entries(params)
    .map(([key, value]) => {
      if (
        typeof value === "string" ||
        typeof value === "number" ||
        typeof value === "boolean"
      ) {
        return isEncoding
          ? `${encodeURIComponent(key)}=${encodeURIComponent(value.toString())}`
          : `${key}=${value.toString()}`;
      } else {
        console.warn(`Unsupported type for key ${key}`, typeof value);
      }
    })
    .filter((part) => part != "")
    .join("&");

export const getBearerTokenExpiry = () => {
  if (process.env.NEXT_PUBLIC_LOCAL_SERVICE) {
    return sessionStorage.getItem(BEARER_TOKEN_EXPIRY) ?? null;
  } else if (
    process.env.NODE_ENV === "development" ||
    process.env.NEXT_PUBLIC_LOCAL
  ) {
    return (Date.now() + HOUR_MS) / 1000;
  }

  return sessionStorage.getItem(BEARER_TOKEN_EXPIRY) ?? null;
};

export const isTokenValid = () => {
  const expiry = getBearerTokenExpiry();
  if (!expiry) return false;
  const diff = Number(expiry) * 1000 - Date.now();

  return !(!expiry || diff < MINUTE_MS);
};

export const getBearerToken = (): string | null => {
  if (
    (process.env.NODE_ENV === "development" || process.env.NEXT_PUBLIC_LOCAL) &&
    process.env.NEXT_PUBLIC_TOKEN
  ) {
    return process.env.NEXT_PUBLIC_TOKEN ?? null;
  } else {
    return sessionStorage.getItem(BEARER_TOKEN) ?? null;
  }
};
