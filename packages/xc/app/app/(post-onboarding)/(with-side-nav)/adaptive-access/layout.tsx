"use client";

import { type ReactNode } from "react";
import {
  AppProvider,
  initializeHttpClient,
  type FeatureFlagsType,
} from "@aae/pages";
import environment from "@/utils/environment";
import * as multiCloudConfig from "@/utils/multiCloudConfig";
import { useFlags } from "@/context/FeatureFlags";

import "@aae/pages/dist/zuxp.scss";
import "@aae/pages/dist/index.css";

type PageLayoutProps = {
  children: ReactNode;
};

initializeHttpClient(environment, {
  getCloudList: () => {
    const fullCloudList = multiCloudConfig.getCloudList();

    return {
      ziam: fullCloudList.ziam || [],
    };
  },
});

const PageLayout = ({ children }: PageLayoutProps) => {
  const { can } = useFlags();
  const featureFlags: FeatureFlagsType = {
    isAAEIntegrationEnabled: can("isAAEIntegrationEnabled"),
  };

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  return <AppProvider featureFlags={featureFlags}>{children}</AppProvider>;
};

export default PageLayout;
