"use client";
// eslint-disable-next-line import/no-unresolved
import { initializeHttpClient } from "@aae/aae-ui"; // Keep imports that don't require dynamic loading
import environment from "@/utils/environment";
import * as multiCloudConfig from "@/utils/multiCloudConfig";

// eslint-disable-next-line import/no-unresolved
import "@aae/aae-ui/dist/zuxp.scss";
// eslint-disable-next-line import/no-unresolved
import "@aae/aae-ui/dist/index.css";

// Initialize the HTTP client outside the component
initializeHttpClient(environment, {
  getCloudList: () => {
    const fullCloudList = multiCloudConfig.getCloudList();

    return {
      ziam: fullCloudList.ziam || [],
    };
  },
});

// eslint-disable-next-line import/no-unresolved
export { AppProvider as default } from "@aae/aae-ui";
