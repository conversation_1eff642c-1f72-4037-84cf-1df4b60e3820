"use client";

import { useEffect, useContext, useLayoutEffect } from "react";
import "@zia/combined-packages/styles/override.scss";
import { BEARER_TOKEN } from "@up/std";
import { useLoadZia } from "./(utils)/useLoadZia";
import { useLoadZiaCss } from "./(utils)/useLoadZiaCss";
import { useSyncZiaRouter } from "./(utils)/useSyncZiaRouter";
import {
  useZiaPrerequisiteData,
  type ZiaPrerequisiteData,
} from "./(utils)/useZiaPrerequisiteData";
import { UserPreferenceContext } from "@/context/UserPreferenceContext";
import { DEVELOPMENT } from "@/configs/constants/analytics";
import { getCloudList, getSelectedZiaCloud } from "@/utils/multiCloudConfig";

type ZiaUserPreferences = {
  locale: string;
};

declare global {
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface Window {
    ZiaUxpApp: {
      ready: boolean;
      init: (
        userPreferences: ZiaUserPreferences,
        ziaPrerequisiteData: ZiaPrerequisiteData,
      ) => void;
      destroy: () => void;
      cleanup: () => void;
      mainView?: HTMLElement;
      renderMainView: () => void;
      basePath: string;
      baseAPIUrl: string;
      syncUserPreferences: (userPreferences: ZiaUserPreferences) => void;
      tenantId?: string;
      cloudName?: string;
      define?: unknown;
    };
    Backbone: {
      history: {
        on: (event: "route", callback: () => void) => void;
        off: (event: "route", callback: () => void) => void;
      };
    };
    define?: unknown;
  }
}

const clearZiaUnsavedData = () => localStorage.setItem("unSavedReactData", "0");

const ZIA = () => {
  const isZiaLoaded = useLoadZia();
  const isCssLoaded = useLoadZiaCss();
  const { ziaPrerequisiteDataRef } = useZiaPrerequisiteData();
  const { userPreference } = useContext(UserPreferenceContext);

  const setBearerToken = () => {
    if (
      (process.env.NODE_ENV === DEVELOPMENT || process.env.NEXT_PUBLIC_LOCAL) &&
      sessionStorage.getItem(BEARER_TOKEN) !== process.env.NEXT_PUBLIC_TOKEN
    ) {
      // For ZIA dev purpose, store token in sessionStorage
      sessionStorage.setItem(BEARER_TOKEN, process.env.NEXT_PUBLIC_TOKEN ?? "");
    }
  };

  const getZiaLocale = (locale: string) =>
    locale === "zh-TW" ? "zh-CN" : locale;

  useLayoutEffect(() => {
    setBearerToken();
    clearZiaUnsavedData();
  }, []);

  useEffect(() => {
    const cloudName = getSelectedZiaCloud();
    window.ZiaUxpApp.cloudName = cloudName;
    const tenant = getCloudList().zia?.find(
      (item) => item.cloudName === cloudName,
    );
    window.ZiaUxpApp.tenantId = tenant?.tenantId;
    // FIX: We should move this to a const
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getSelectedZiaCloud()]);

  useEffect(() => {
    if (window?.ZiaUxpApp?.mainView && window.ZiaUxpApp.syncUserPreferences) {
      window.ZiaUxpApp.syncUserPreferences({
        locale: getZiaLocale(userPreference.language),
      });
    }
  }, [userPreference]);

  useSyncZiaRouter(isCssLoaded && isZiaLoaded);

  useEffect(() => {
    if (!isZiaLoaded || !isCssLoaded) return;

    const ZiaUxpApp = window.ZiaUxpApp;

    // restore the saved define
    if (ZiaUxpApp.define) {
      window.define = ZiaUxpApp.define;
    }

    if (!ZiaUxpApp.mainView) {
      ZiaUxpApp.init(
        {
          locale: getZiaLocale(userPreference.language),
        },
        ziaPrerequisiteDataRef.current,
      );
    } else {
      ZiaUxpApp.renderMainView();
    }

    return () => {
      // save define to a variable
      if (window.define) {
        ZiaUxpApp.define = window.define;
        window.define = undefined;
      }
      // only clean up if ZIA is not initialized successfully
      if (!ZiaUxpApp.mainView) {
        ZiaUxpApp.destroy();
      } else {
        ZiaUxpApp.cleanup();
      }
    };
  }, [
    isZiaLoaded,
    isCssLoaded,
    userPreference.language,
    ziaPrerequisiteDataRef,
  ]);

  return (
    <>
      <div id="zia-view" data-testid={`zia-view`}>
        <div className="js-uxp-zia-page-wrapper">
          <div id="main-page" />
        </div>
        <div id="zia-uxp-loader" className="lean-loader">
          <div className="spinner" />
        </div>
      </div>
    </>
  );
};

export default ZIA;
