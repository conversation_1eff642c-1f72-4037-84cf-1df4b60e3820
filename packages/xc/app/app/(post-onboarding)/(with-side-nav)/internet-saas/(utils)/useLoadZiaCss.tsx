import { useEffect, useState } from "react";
import { linkExists, loadStyleSheet } from "@up/std";
import { useZiaRbac } from "@/hooks/useZiaRbac";

export const useLoadZiaCss = () => {
  const [loaded, setLoaded] = useState(false);
  const { loading, entitled, version } = useZiaRbac();

  useEffect(() => {
    if (loading || !entitled) return;

    const { dir } = version;

    const cssFile = `${dir}/css/app_uxp.css`;

    if (linkExists(cssFile)) {
      setLoaded(true);

      return;
    }

    const link = loadStyleSheet(cssFile);
    link.onload = () => {
      setLoaded(true);
    };
  }, [loading, entitled, version]);

  return loaded;
};
