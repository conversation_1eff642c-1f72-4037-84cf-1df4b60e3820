import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { ZIA_ROUTE_PREFIX } from "@zia/rbac";

export const useSyncZiaRouter = (isZiaReady: boolean) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [ziaPath, setZiaPath] = useState<string>();

  useEffect(() => {
    if (!isZiaReady) return;

    // Listen to Backbone route changes events
    const listener = () => {
      setZiaPath(window.location.pathname + window.location.hash);
    };
    window.Backbone.history.on("route", listener);

    return () => window.Backbone.history.off("route", listener);
  }, [isZiaReady]);

  useEffect(() => {
    if (!isZiaReady) return;

    // When next.js route changes, notify Backbone router
    // This is required for Backbone navigation to trigger on hash change
    window.dispatchEvent(new Event("handleZIARouteChange"));
  }, [isZiaReady, pathname, searchParams]);

  useEffect(() => {
    if (!ziaPath || !isZiaReady) return;

    // When Backbone route changes, notify next.js router
    if (ziaPath.startsWith(`/${ZIA_ROUTE_PREFIX}`)) {
      router.replace(ziaPath);
    }
  }, [router, ziaPath, isZiaReady]);
};
