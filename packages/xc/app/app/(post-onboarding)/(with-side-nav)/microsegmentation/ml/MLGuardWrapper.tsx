"use client";

import { MLRecommendationsGuard } from "@zms/pages";
import { useRouter } from "next/navigation";

export default function PolicyGuardWrapper({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();

  return (
    <MLRecommendationsGuard
      navigate={(url: string) => router.push(url)}
      resourceGroupRoute="/microsegmentation/resource-group"
    >
      {children}
    </MLRecommendationsGuard>
  );
}
