"use client";

import { EditAppZonePage } from "@zms/pages";
import { useRouter, useSearchParams } from "next/navigation";

export default function EditAppZoneWrapper() {
  const router = useRouter();
  const searchParams = useSearchParams();

  return (
    <EditAppZonePage
      navigate={(url: string) => router.push(url)}
      searchParams={searchParams}
      backRoute="/microsegmentation/app-zone"
    />
  );
}
