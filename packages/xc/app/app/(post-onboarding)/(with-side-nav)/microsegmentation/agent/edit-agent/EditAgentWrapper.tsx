"use client";

import { EditAgentPage } from "@zms/pages";
import { useRouter, useSearchParams } from "next/navigation";

export default function EditAgentWrapper() {
  const router = useRouter();
  const searchParams = useSearchParams();

  return (
    <EditAgentPage
      navigate={(url: string) => router.push(url)}
      searchParams={searchParams}
      agentsRoute="/microsegmentation/agent"
    />
  );
}
