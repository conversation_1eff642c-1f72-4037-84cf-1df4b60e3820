"use client";

import { EditResourceGroupPage } from "@zms/pages";
import { useRouter, useSearchParams } from "next/navigation";

export default function EditResourceGroupWrapper() {
  const router = useRouter();
  const searchParams = useSearchParams();

  return (
    <EditResourceGroupPage
      navigate={(url: string) => router.push(url)}
      searchParams={searchParams}
      backRoute="/microsegmentation/resource-group"
    />
  );
}
