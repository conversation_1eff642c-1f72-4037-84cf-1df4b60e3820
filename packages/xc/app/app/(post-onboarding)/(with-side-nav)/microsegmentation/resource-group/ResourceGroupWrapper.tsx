"use client";

import { ResourceGroupPage } from "@zms/pages";
import { useRouter } from "next/navigation";

export default function ResourceGroupWrapper() {
  const router = useRouter();

  return (
    <ResourceGroupPage
      navigate={(url: string) => router.push(url)}
      addResourceGroupRoute="/microsegmentation/resource-group/add-resource-group"
      editResourceGroupRoute="/microsegmentation/resource-group/edit-resource-group"
      mlRoute="/microsegmentation/ml/recommendations"
    />
  );
}
