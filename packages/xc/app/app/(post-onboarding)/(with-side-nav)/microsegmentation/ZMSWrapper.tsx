"use client";

import { Style<PERSON>rov<PERSON>, ZmsProvider } from "@zms/pages";
import type { ZPAUserData } from "@zuxp/zpa";
import { useEffect, useState } from "react";
import { getBearerToken } from "@up/std";
import { getZMSConfig } from "@zms/utils";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";
import { useFlags } from "@/context/FeatureFlags";
import { getZpaDataFromToken } from "@/utils/zpaUtils/zpaEnvConfig";
import { usePreferences } from "@/context/UserPreferenceContext";
import { Spinner } from "@/components/Spinner/Spinner";

export default function ZMSWrapper({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { can } = useFlags();
  const bearerToken = getBearerToken();

  const { features } = useProductAccessProvider();
  const ZPA = features?.ZPA as ZPAUserData;
  const customerId = ZPA?.zoneDetails?.accessingCustomerId;
  const [zmsConfig, setZMSConfig] = useState<Record<string, string>>();

  const showZMS = () =>
    can("showZMS") &&
    !!customerId &&
    customerId.length > 0 &&
    !!bearerToken &&
    !!zmsConfig;

  useEffect(() => {
    // This needs to be done ahead of time to save time
    const { cld = "" } = getZpaDataFromToken();
    void getZMSConfig(cld).then((config) => {
      setZMSConfig(config.default);
    });
  }, []);

  const { isLoading } = usePreferences();

  return !isLoading ? (
    showZMS() && (
      <StyleProvider>
        <ZmsProvider
          customerId={customerId!}
          authToken={bearerToken!}
          eyezConfig={zmsConfig!}
        >
          {children}
        </ZmsProvider>
      </StyleProvider>
    )
  ) : (
    <Spinner size="2xl" defaultClass="min-h-[100vh]" />
  );
}
