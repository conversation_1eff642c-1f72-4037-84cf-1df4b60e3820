"use client";

import { EditPolicyRulePage } from "@zms/pages";
import { useRouter, useSearchParams } from "next/navigation";

export default function EditPolicyRuleWrapper() {
  const router = useRouter();
  const searchParams = useSearchParams();

  return (
    <EditPolicyRulePage
      navigate={(url: string) => router.push(url)}
      searchParams={searchParams}
      backRoute="/microsegmentation/policy/resource-policy"
    />
  );
}
