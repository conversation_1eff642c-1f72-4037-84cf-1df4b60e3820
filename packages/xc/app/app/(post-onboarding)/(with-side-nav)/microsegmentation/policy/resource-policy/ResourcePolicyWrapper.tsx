"use client";

import { PolicyRulePage } from "@zms/pages";
import { useRouter } from "next/navigation";

export default function ResourcePolicyWrapper() {
  const router = useRouter();

  return (
    <PolicyRulePage
      navigate={(url: string) => router.push(url)}
      addRuleRoute="/microsegmentation/policy/resource-policy/add-policy-rule"
      editRuleRoute="/microsegmentation/policy/resource-policy/edit-policy-rule"
      settingsRoute="/microsegmentation/policy/policy-settings"
    />
  );
}
