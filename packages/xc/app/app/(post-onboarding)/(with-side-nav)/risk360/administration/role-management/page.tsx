"use client";

import dynamic from "next/dynamic";
import { Suspense } from "react";
import { Spinner } from "@/components/Spinner/Spinner";

const RoleManagementWrapper = dynamic(() => import("./roleManagementWrapper"), {
  ssr: false,
  loading: () => <Spinner size="2xl" defaultClass="min-h-[100vh]" />,
});

export default function Page() {
  return (
    <Suspense fallback={<div>Loading</div>}>
      <RoleManagementWrapper />
    </Suspense>
  );
}
