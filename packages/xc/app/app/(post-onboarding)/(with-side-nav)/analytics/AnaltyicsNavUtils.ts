import {
  ccWidgetCheckAccess,
  Risk360SkuFilteredItems,
  zdxWidgetAccess,
} from "@/utils/unifiedAnalytics/utils";
import {
  type Product,
  type ProductAccessInfo,
} from "@/context/ProductAccessProvider";
import { SUBSCRIPTION_TYPES } from "@/configs/constants/entitlements";
import { type ProductAccessInfoWithDevFlags } from "@/configs/navigation/types";

type NavId =
  | "DigitalExperience.Dashboard"
  | "DigitalExperience.Incidents"
  | "DigitalExperience.Applications"
  | "DigitalExperience.Activity"
  | "DigitalExperience.Meetings"
  | "DigitalExperience.SelfService";

const navProductAccessData: Record<NavId, string[]> = {
  "DigitalExperience.Dashboard": [
    "ApplicationExperience",
    "EndPointSelfService",
    "NetworkLatencyGeoview.Zscaler",
    "NetworkLatencyGeoview.DNS Resolution",
    "OverallDigitalExperience",
    "UnifiedCommunicationExperience",
    "UserExperience",
  ],
  "DigitalExperience.Incidents": [
    "KeyAreas",
    "OverTime",
    "Epicenters",
    "IncidentsTable",
    "IncidentDetails",
  ],
  "DigitalExperience.Applications": [
    "ApplicationsTable",
    "ImpactedDepartments",
    "ImpactedLocations",
    "ImpactedMeetings",
    "ImpactedRegions",
    "ApplicationPageFetchTime",
    "ApplicationZDXScore",
    "ApplicationRegionsZDXScoreChartTime",
    "ApplicationSummary",
    "ImpactedUsers",
  ],
  "DigitalExperience.Activity": [
    "UserDistributionByExperienceScore",
    "TopUsedApps",
    "RegionsByAvgExScore",
    "ActiveUsers",
    "ActiveDevices",
    "WifiExperience",
  ],
  "DigitalExperience.Meetings": ["MeetingsTable", "ParticipantsList"],
  "DigitalExperience.SelfService": [
    "NotificationsSentOverTime",
    "NotificationTable",
  ],
};

const getNavItemState = (
  productAccessInfo: ProductAccessInfo,
  navId: NavId,
): boolean => {
  let disabledState = true;
  navProductAccessData[navId]?.map((widgetId: string) => {
    const subscriptionStatus = zdxWidgetAccess(
      navId + "." + widgetId,
      productAccessInfo,
    );
    const currentState = SUBSCRIPTION_TYPES.includes(subscriptionStatus);
    disabledState = disabledState && currentState;
  });

  return disabledState;
};

export { getNavItemState };

export const getNetworkingNavItemState = ({ products }: ProductAccessInfo) => {
  const networkingProducts = ["CLOUD_CONNECTOR", "ZIA", "ZPA"] as Product[];

  return !networkingProducts.some((product) => products?.includes(product));
};

export const getZIANavItemState = ({ entitlements }: ProductAccessInfo) =>
  !entitlements.zia;

export const getDPNavItemState = () => true;

export const getZCCNavItemState = ({ entitlements }: ProductAccessInfo) =>
  !entitlements?.zcc;

export const getCCNavItemState = (
  productAccessInfo: ProductAccessInfo,
  id: string,
) => (ccWidgetCheckAccess(productAccessInfo, id) === "" ? true : false);

type Modules = Record<string, any>;

export const getRisk360NavState = (
  productAccessInfo: ProductAccessInfo,
  id: string,
) => {
  const skuInfo = productAccessInfo?.features?.["ZRA@PROFILE"]
    ?.modules as Modules;

  return Risk360SkuFilteredItems(skuInfo, id);
};

export const getRisk360FlagStatus = (
  productAccessInfo: ProductAccessInfo,
  id: string,
) => {
  const flagsInfo = productAccessInfo?.features?.["ZRA@PROFILE"]
    ?.featureFlags as Modules;

  if (Object.keys(flagsInfo ?? {}).length > 0) {
    return !flagsInfo[id];
  }

  return true;
};

export const getRisk360SubscriptionState = (
  productAccessInfo: ProductAccessInfoWithDevFlags,
) =>
  productAccessInfo?.features?.["ZRA@PROFILE"]?.subscription === "Z_RA" ||
  !productAccessInfo.products?.includes("ZRA");

export const getRisk360FeatureFlagHiddenNav = (
  productAccessInfo: ProductAccessInfoWithDevFlags,
  id: string,
) =>
  !productAccessInfo.can("showRisk360") ||
  getRisk360SubscriptionState(productAccessInfo) ||
  getRisk360FlagStatus(productAccessInfo, id);

export const getRisk360HiddenNav = (
  productAccessInfo: ProductAccessInfoWithDevFlags,
  id: string,
) =>
  !productAccessInfo.can("showRisk360") ||
  getRisk360SubscriptionState(productAccessInfo) ||
  getRisk360NavState(productAccessInfo, id);
