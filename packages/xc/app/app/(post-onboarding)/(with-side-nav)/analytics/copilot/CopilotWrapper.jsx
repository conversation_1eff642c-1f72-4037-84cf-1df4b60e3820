"use client";
import CopilotStandalone from "@zscaler/copilot";
import { getBearerToken, SK_API_ENDPOINT } from "@up/std";
import { getTenantCldValue, API_ENDPOINTS } from "@/utils/apiHelper";
import "@zscaler/copilot/dist/style.css";

export default function CopilotWrapper() {
  const bearerToken = getBearerToken() ?? "";
  const headers = getTenantCldValue(API_ENDPOINTS.ZDX) || {};

  return (
    <CopilotStandalone
      options={{
        auth: {
          csrfToken: "",
          bearerToken,
          ...(headers && {
            headers,
          }),
        },
        api: {
          base: sessionStorage.getItem(SK_API_ENDPOINT),
          conversationEndpoint: "/private/copilot/copilot/v1/completions",
          landingWidgetsEndpoint: "/private/copilot/copilot/v1/landing-widgets",
          historyEndpoint: "/private/copilot/copilot/v1/history",
          delHistoryEndpoint: "/private/copilot/copilot/v1/history",
          feedbackEndpoint: "/private/copilot/copilot/v1/feedback",
        },
      }}
    />
  );
}
