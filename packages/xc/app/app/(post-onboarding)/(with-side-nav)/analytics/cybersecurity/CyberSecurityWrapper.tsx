"use client";

import { NavigationPerformanceTracker } from "@up/performance";
import { CyberSecurity } from "@/components/pages/Analytics/Dashboard/CyberSecurity/CyberSecurity/CyberSecurity";

export default function CyberSecurityWrapper() {
  return (
    <>
      <NavigationPerformanceTracker selector='[data-testid="cybersecurity-your-cs-trans-card-description"]' />
      <CyberSecurity id="cybersecurity" />
    </>
  );
}
