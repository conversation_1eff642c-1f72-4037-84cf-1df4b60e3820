import {
  type Dispatch,
  type SetStateAction,
  type ReactNode,
  createContext,
  useContext,
  useState,
} from "react";
import type {
  FormModalNames,
  ProviderAppsResponseType,
} from "@/components/SaaS/InstanceAPI/types";
import { useGetSWR } from "@/utils/apiUtils";
import { Onboarding } from "@/configs/urls/policies/applications/onboarding";

type ProviderProps = {
  children: ReactNode;
};

export type ModalsCommonStateProps = Partial<{
  saasApplication: string;
  instanceId: string;
}>;

export type SaaSPageConfigProps = Partial<{
  alert: {
    message: string;
    type: "warning" | "error" | "success" | "info";
    id: string;
    customStyleClass?: string;
  };
}>;

export type SaaSOnboardingContextProps = {
  activeFormModal: FormModalNames;
  setActiveFormModal: Dispatch<SetStateAction<FormModalNames>>;
  providerAppsList: ProviderAppsResponseType[];
  setProviderAppsList: Dispatch<SetStateAction<ProviderAppsResponseType[]>>;
  modalStates: ModalsCommonStateProps;
  setModalStates: Dispatch<SetStateAction<ModalsCommonStateProps>>;
  pageConfig: SaaSPageConfigProps;
  setPageConfig: Dispatch<SetStateAction<SaaSPageConfigProps>>;
  manualInstanceAppMap?: unknown;
  casbProviderAppData?: unknown;
};

export const UnifiedSaaSOnboardingContext =
  createContext<SaaSOnboardingContextProps>({
    activeFormModal: "",
    setActiveFormModal: () => undefined,
    providerAppsList: [],
    setProviderAppsList: () => undefined,
    modalStates: {},
    setModalStates: () => undefined,
    pageConfig: {},
    setPageConfig: () => undefined,
  });

export const UnifiedSaaSOnboardingProvider = ({ children }: ProviderProps) => {
  const [activeFormModal, setActiveFormModal] = useState<FormModalNames>("");
  const [providerAppsList, setProviderAppsList] = useState<
    ProviderAppsResponseType[]
  >([]);
  const [modalStates, setModalStates] = useState<ModalsCommonStateProps>({});
  const [pageConfig, setPageConfig] = useState<SaaSPageConfigProps>({});

  const casbProviderAppUrlId = "Onboarding.List.RecommendedApplications";
  const { data: casbProviderAppData } = useGetSWR(
    Onboarding.List.RecommendedApplications,
    {
      key: casbProviderAppUrlId,
    },
  );

  const manualInstanceAppUrlId = "Onboarding.ManualInstance.InstanceType";
  const { data: manualInstanceAppMap } = useGetSWR(
    Onboarding.ManualInstance.InstanceType,
    {
      key: manualInstanceAppUrlId,
    },
  );

  const providerValues = {
    activeFormModal,
    setActiveFormModal,
    providerAppsList,
    setProviderAppsList,
    modalStates,
    setModalStates,
    pageConfig,
    setPageConfig,
    manualInstanceAppMap,
    casbProviderAppData,
  };

  return (
    <UnifiedSaaSOnboardingContext.Provider value={providerValues}>
      {children}
    </UnifiedSaaSOnboardingContext.Provider>
  );
};

export const useSaaSOnboarding = () => useContext(UnifiedSaaSOnboardingContext);
