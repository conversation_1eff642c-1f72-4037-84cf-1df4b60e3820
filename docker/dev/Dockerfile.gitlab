FROM nexus.corp.zscaler.com:9016/node:22.14.0-alpine

WORKDIR /usr/share/zscaler/oneui

RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --chown=nextjs:nodejs ../../packages/xc/app/.next/standalone ./
COPY --chown=nextjs:nodejs ../../packages/xc/app/.next/static ./packages/xc/app/.next/static
COPY --chown=nextjs:nodejs ../../packages/xc/app/public ./packages/xc/app/public

USER nextjs

ENV PORT 3000

ENV HOSTNAME=0.0.0.0

CMD ["node", "packages/xc/app/server.js"]