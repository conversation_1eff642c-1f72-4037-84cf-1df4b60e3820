import { defineConfig, devices } from '@playwright/test';
import { defineBddConfig } from 'playwright-bdd';

export default defineConfig({
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 0 : 0,
  workers: undefined,
  reporter: [
    ['list'],
    ['html'],
    ['allure-playwright', { outputFolder: 'allure-results' }]
  ],
  use: {
    // trace: { mode: 'on', snapshots: true },
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    {
      name: 'Login',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/*.feature'],   
        require: ['src/tests/ui/step_definitions/*_steps.ts',
                  'src/tests/ui/pages/*_page.ts'
                ],
        outputDir: 'results/Login',
      }),
      use: { ...devices['Desktop Chrome'],
      viewport: { width: 1280, height: 720 } },
      timeout: 120000,
    },
    {
      name: 'DigitalExperience',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/DigitalExperience/*.feature'],
        require: ['src/tests/ui/step_definitions/digitalExperience/*_steps.ts',
                  'src/tests/ui/pages/digitalExperience/*_page.ts'],
        outputDir: 'results/DE',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 120000,
    },
    {
      name: 'Cybersecurity',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/Cybersecurity/*.feature'],
        require: ['src/tests/ui/step_definitions/Cybersecurity/*_steps.ts',
                  'src/tests/ui/pages/Cybersecurity/*_page.ts'],
        outputDir: 'results/CS',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 150000,
    },
    {
      name: 'Networking',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/Networking/*.feature'],
        require: ['src/tests/ui/step_definitions/Networking/*_steps.ts',
                  'src/tests/ui/pages/Networking/*_page.ts'],
        outputDir: 'results/Net',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 120000,
    },
    {
      name: 'GlobalNavigation',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/GlobalNavigation/*.feature'],
        require: ['src/tests/ui/step_definitions/**/*_steps.ts',
                  'src/tests/ui/pages/**/*_page.ts'],
        outputDir: 'results/GN',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 600000,
    },
    {
      name: 'Operational',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/Operational/*.feature'],
        require: ['src/tests/ui/step_definitions/**/*_steps.ts',
                  'src/tests/ui/step_definitions/*_steps.ts',
                  'src/tests/ui/pages/**/*_page.ts'],
        outputDir: 'results/OP',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
       },
      dependencies: ['Login'],
      timeout: 100000,
    },
    {
      name: 'DarkThemeSetup',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/DarkTheme/darkTheme.feature'],
        require: [
          'src/tests/ui/step_definitions/**/*_steps.ts',
          'src/tests/ui/pages/**/*_page.ts',
          'resources/utils/hooks.ts',
        ],
        outputDir: 'results/DT_Setup',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 120000,
    },
    {
      name: 'DarkTheme',
      testDir: defineBddConfig({
        paths: [
          'src/tests/ui/features/DarkTheme/*.feature',
          '!src/tests/ui/features/DarkTheme/darkTheme.feature'
        ],
        require: [
          'src/tests/ui/step_definitions/**/*_steps.ts',
          'src/tests/ui/pages/**/*_page.ts',
          'resources/utils/hooks.ts',
        ],
        outputDir: 'results/DT',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['DarkThemeSetup'],
      timeout: 200000,
    },
    {
      name: 'TopNav',
      testDir: defineBddConfig({
        paths: [
          'src/tests/ui/features/TopNav/*.feature',
        ],
        require: [
          'src/tests/ui/step_definitions/TopNav/*_steps.ts',
          'src/tests/ui/pages/TopNav/*_page.ts',
        ],
        outputDir: 'results/TN',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 100000,
    },
    {
      name: 'UnifiedLocations',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/UnifiedLocations/*.feature'],
        require: ['src/tests/ui/step_definitions/**/*_steps.ts',
                  'src/tests/ui/pages/**/*_page.ts'],
        outputDir: 'results/UL',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 100000,
    },
    {
      name: 'UnifiedSubscriptions',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/UnifiedSubscriptions/*.feature'],
        require: ['src/tests/ui/step_definitions/**/*_steps.ts',
                  'src/tests/ui/pages/**/*_page.ts'],
        outputDir: 'results/US',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 100000,
    },
    {
      name: 'RBAC',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/RBAC/*.feature'],
        require: ['src/tests/ui/step_definitions/**/*_steps.ts',
          'src/tests/ui/pages/**/*_page.ts'],
        outputDir: 'results/RBAC',
      }),
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
       },
       timeout: 260000,
    },
    {
      name: 'Home',
      testDir: defineBddConfig({
        paths: [
          'src/tests/ui/features/Home/*.feature',
        ],
        require: [
          'src/tests/ui/step_definitions/Home/*_steps.ts',
          'src/tests/ui/pages/Home/*_page.ts',
        ],
        outputDir: 'results/Home',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 180000,
    },
    {
      name: 'DataProtection',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/DataProtection/*.feature'],
        require: ['src/tests/ui/step_definitions/DataProtection/*_steps.ts',
                  'src/tests/ui/pages/DataProtection/*_page.ts'],
        outputDir: 'results/DP',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['Login'],
      timeout: 120000,
    },
    {
      name: 'ZIA_Standalone_Login',
      testDir: defineBddConfig({
        paths: ['src/tests/zia/features/*.feature'],   
        require: ['src/tests/zia/step_definitions/*_steps.ts',
                  'src/tests/zia/pages/*_page.ts'
                ],
        outputDir: 'results/ZIALogin',
      }),
      use: { ...devices['Desktop Chrome'],
      viewport: { width: 1280, height: 720 } },
      timeout: 120000,
    },
    {
      name: 'ZIA_OneUI_Login',
      testDir: defineBddConfig({
        paths: ['src/tests/ui/features/*.feature'],   
        require: ['src/tests/ui/step_definitions/*_steps.ts',
                  'src/tests/ui/pages/*_page.ts'
                ],
        outputDir: 'results/ZIA_OneUI',
      }),
      use: { ...devices['Desktop Chrome'],
      viewport: { width: 1280, height: 720 } },
      timeout: 120000,
    },
    {
      name: 'ZIA Alerts',
      testDir: defineBddConfig({
        paths: ['src/tests/zia/features/Alerts/*.feature'],   
        require: ['src/tests/zia/step_definitions/Alerts/*_steps.ts',
                  'src/tests/zia/pages/Alerts/*_page.ts'
                ],
        outputDir: 'results/ZIA_Alerts',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
       },
      dependencies: ['ZIA_Standalone_Login'],
      timeout: 100000,
    },
    {
      name: 'ZIA MyProfile',
      testDir: defineBddConfig({
        paths: ['src/tests/zia/features/MyProfile/*.feature'],   
        require: ['src/tests/zia/step_definitions/MyProfile/*_steps.ts',
                  'src/tests/zia/pages/MyProfile/*_page.ts'
                ],
        outputDir: 'results/ZIA_MyProfile',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
       },
      dependencies: ['ZIA_Standalone_Login'],
      timeout: 100000,
    },
    {
      name: 'ZIA_MalwarePolicy',
      testDir: defineBddConfig({
        paths: ['src/tests/zia/features/MalwarePolicy/*.feature'],   
        require: ['src/tests/zia/step_definitions/**/*_steps.ts',
                  'src/tests/zia/pages/**/*_page.ts'
                ],
        outputDir: 'results/ZIA_MP',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
       },
      dependencies: ['ZIA_Standalone_Login'],
      timeout: 200000,
    },

    {
      name: 'ZIA_SecureBrowsing',
      testDir: defineBddConfig({
        paths: ['src/tests/zia/features/SecureBrowsing/*.feature'],   
        require: ['src/tests/zia/step_definitions/**/*_steps.ts',
                  'src/tests/zia/pages/**/*_page.ts'
                ],
        outputDir: 'results/ZIA_SB',
      }),
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'playwright/.auth/user.json',
        viewport: { width: 1280, height: 720 },
       },
      dependencies: ['ZIA_Standalone_Login'],
      timeout: 200000,
    },


    // {
    //   name: 'New Auth Flow',
    //   testDir: defineBddConfig({
    //     paths: [
    //       'src/tests/ui/features/AuthFlow/*.feature',
    //     ],
    //     require: [
    //       'src/tests/ui/step_definitions/AuthFlow/*_steps.ts',
    //       'src/tests/ui/pages/AuthFlow/*_page.ts',
    //     ],
    //     outputDir: 'results/NAF',
    //   }),
    //   use: {
    //     ...devices['Desktop Chrome'],
    //     viewport: { width: 1280, height: 720 },
    //   },
    //   timeout: 3800000,
    // },
  ],
});