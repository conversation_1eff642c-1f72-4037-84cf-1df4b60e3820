@RBAC @xc
Feature: To verify the ZIA only user role based access
# XC-7821
Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZIA Only User" username and password
    Then User login to the console successfully
    Then user clicks on Analytics

# # XC-7823
#    Scenario: To verify Connector Activity is locked
#      Given User verify the Connector Activity is disabled, locked icon and text on hover for ZIA

Scenario: To verify Digital Experience, Connector Activity and Opertional menus are not visible
  Then User verify the "Digital Experience" menu is not visible
  Then User verify the "Operational" menu is not visible
  Then User verify the "Connector Activity" submenu under "Networking" is not visible

# XC-5905
@networking
  Scenario: Verify filters in Networking Page
    When User is in Networking Page
    Then Verify the filters in Networking Page

# XC-5906, XC-5907
@networking
  Scenario: Verify Traffic in my Organization
    When User is in Networking Page
    Then Verify the title "Traffic in my Organization" in Networking Page
    Then Ensure that you are in "Internet & SaaS" tab in Networking Page
    Then Verify the label and value of "Number of Transactions" under "Internet & SaaS" in Networking Page
    Then Ensure that "Number of Transactions" graph canvas of type "trend-line-chart" is present under "Internet & SaaS" in Networking Page
    Then Verify the label and value of "Traffic Volume" under "Internet & SaaS" in Networking Page
    Then Ensure that "Traffic Volume" graph canvas of type "trend-line-chart" is present under "Internet & SaaS" in Networking Page
    Then Ensure that "Traffic in my Organization" graph canvas of type "multiline-chart" is present under "Internet & SaaS" in Networking Page
    Then Ensure that you are in "Private" tab in Networking Page
    Then Verify the label and value of "Number of Transactions" under "Private" in Networking Page
    Then Ensure that "Number of Transactions" graph canvas of type "trend-line-chart" is present under "Private" in Networking Page
    Then Verify the label and value of "Traffic Volume" under "Private" in Networking Page
    Then Ensure that "Traffic Volume" graph canvas of type "trend-line-chart" is present under "Private" in Networking Page
    Then Ensure that "Traffic in my Organization" graph canvas of type "multiline-chart" is present under "Private" in Networking Page

# XC-5911
@networking
Scenario: Verify Internet Traffic Distribution
  When User is in Networking Page 
  Then Verify the title "Internet Traffic Distribution" in Networking Page
  Then Ensure that "Internet Traffic Distribution" graph canvas of type "donut-chart" is present in Networking Page

# XC-5920
@networking
Scenario: Verify Top Locations sending Internet Traffic to Zscaler
  When User is in Networking Page 
  Then Verify the title "Top Locations sending Internet Traffic to Zscaler" in Networking Page
  Then Ensure that "Top Locations sending Internet Traffic to Zscaler" graph canvas of type "horizontal-bar-chart" is present in Networking Page

# XC-5912
@networking
Scenario: Verify Data Centers
  When User is in Networking Page 
  Then Ensure that you are in "Data Centers" tab in Networking Page
  Then Verify the title "Top Zscaler Data Centers Used" in Networking Page
  Then Ensure that "Top Zscaler Data Centers Used" graph canvas of type "world-bubbles-chart" is present in Networking Page

# XC-5912
@networking
Scenario: Verify App Connectors
  When User is in Networking Page 
  Then Ensure that you are in "App Connectors" tab in Networking Page
  Then Verify the title "Locations with Zscaler Connectors" in Networking Page
  Then Ensure that "Locations with Zscaler Connectors" graph canvas of type "world-icon-chart" is present in Networking Page

# XC-5912
@networking
Scenario: Verify Branch & Cloud Connectors
  When User is in Networking Page 
  Then Ensure that you are in "Branch & Cloud Connectors" tab in Networking Page
  Then Verify the title "Branch & Cloud Connectors" in Networking Page
  Then Ensure that "Branch & Cloud Connectors" graph canvas of type "world-pie-chart" is present in Networking Page
  Then Ensure User can navigate to "Connector Activity" Page while clicking on footer under "Branch & Cloud Connectors" in Networking Page


# XC-7821
   Scenario: To verify the Administration global navigation for ZIA Only user
     Given User capture the screenshot for ZIA "Administration" to "Account Management"
     Then Verify the difference between the screenshots for ZIA
     
# XC-7821
   Scenario: To verify the ZIA Administration for Admin Management
    Given User capture the screenshot for ZIA "Administration" to "Admin Management"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
 Scenario: To verify the ZIA Administration -> Admin Management navigation for Role Based Access Control
    Given User capture the screenshot for ZIA "Administration" to "Admin Management" then "Role Based Access Control"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Administration -> Admin Management navigation for Administrator Management
    Given User capture the screenshot for ZIA "Administration" to "Admin Management" then "Administrator Management"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Administration -> Admin Management navigation for Audit Logs
    Given User capture the screenshot for ZIA "Administration" to "Admin Management" then "Audit Logs"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Administration -> Identity navigation for ZIdentity
    Given User capture the screenshot for ZIA "Administration" to "Identity" then "ZIdentity"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Administration -> Identity navigation for Internet & SaaS
    Given User capture the screenshot for ZIA "Administration" to "Identity" then "Internet & SaaS"
    Then Verify the difference between the screenshots for ZIA


# XC-7821
   Scenario: To verify the ZIA Administration for API Configuration
    Given User capture the screenshot for ZIA "Administration" to "API Configuration" then "Legacy API"
    Then Verify the difference between the screenshots for ZIA


# XC-7821
   Scenario: To verify the ZIA Administration for Alerts
    Given User capture the screenshot for ZIA "Administration" to "Alerts"
    Then Verify the difference between the screenshots for ZIA


# XC-7821
   Scenario: To verify the ZIA Administration for Backup and Restore
    Given User capture the screenshot for ZIA "Administration" to "Backup & Restore"
    Then Verify the difference between the screenshots for ZIA

#  Scenario: To verify the ZIA Policies -> Access Control navigation for Internet & SaaS
#     Given User capture the screenshot for ZIA "Policies" to "Access Control" then "Internet & SaaS"
#     Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Access Control navigation for Firewall
    Given User capture the screenshot for ZIA "Policies" to "Access Control" then "Firewall"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Cybersecurity navigation for Inline Security 
    Given User capture the screenshot for ZIA "Policies" to "Cybersecurity" then "Inline Security"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Cybersecurity navigation for SaaS Security API
    Given User capture the screenshot for ZIA "Policies" to "Cybersecurity" then "SaaS Security API"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Cybersecurity navigation for Partner Integrations
    Given User capture the screenshot for ZIA "Policies" to "Cybersecurity" then "Partner Integrations"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Data Protection navigation for Policy
    Given User capture the screenshot for ZIA "Policies" to "Data Protection" then "Policy"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Data Protection navigation for Common Resources
    Given User capture the screenshot for ZIA "Policies" to "Data Protection" then "Common Resources"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Common Configuration navigation for Out-of-Band CASB
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "Out-of-Band CASB"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Common Configuration navigation for SSL/TLS Inspection
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "SSL/TLS Inspection"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Common Configuration navigation for Resources
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "Resources"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Policies -> Common Configuration navigation for Advanced
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "Advanced"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Infrastructure -> Internet & SaaS navigation for Traffic Forwarding
    Given User capture the screenshot for ZIA "Infrastructure" to "Internet & SaaS" then "Traffic Forwarding"
    Then Verify the difference between the screenshots for ZIA

 Scenario: To verify the ZIA Infrastructure -> Internet & SaaS navigation for Network Policies
    Given User capture the screenshot for ZIA "Infrastructure" to "Internet & SaaS" then "Network Policies"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
   Scenario: To verify the ZIA Infrastructure for Locations
    Given User capture the screenshot for ZIA "Infrastructure" to "Locations"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
   Scenario: To verify the ZIA Logs for Insights
    Given User capture the screenshot for ZIA "Logs" to "Insights"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
   Scenario: To verify the ZIA Logs for Log Streaming
    Given User capture the screenshot for ZIA "Logs" to "Log Streaming"
    Then Verify the difference between the screenshots for ZIA

