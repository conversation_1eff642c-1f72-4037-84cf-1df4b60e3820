@RBAC @xc
Feature: To verify the ZID only user role based access

# XC-7824
  Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZID Only User" username and password
    Then User login to the console successfully
    Then user clicks on Analytics


  Scenario: To verify  Networking, Digital Experience, Cybersecurity and Operational menus are not visible
     Then User verify the "Networking" menu is not visible
     Then User verify the "Digital Experience" menu is not visible
     Then User verify the "Cybersecurity" menu is not visible
     Then User verify the "Operational" menu is not visible

# XC-7824
   Scenario: To verify the user should not be able to view any widgets in networking dashboard
      Given User is able to view the widgets heading "<widgets>"
      When User verify the "Access Restricted" text along with "<widgets>"
      Examples:
       |            widgets          |
       | Traffic in my Organization  |
       |Internet Traffic Distribution|
       |Top Locations sending Internet Traffic to Zscaler|
       |Top Zscaler Data Centers Used|
       |Devices Discovered           | 

# # XC-7824
#    Scenario: To verify Connector Activity, Digital Experience, Cybersecurity and Operational are locked
#      Given User verify the Connector Activity is disabled, locked icon and text on hover
#      When User verify the Digital Experience is disabled, locked icon and text on hover
#      And User verify the Cybersecurity is disabled, locked icon and text on hover
#      Then User verify the Operational is disabled, locked icon and text on hover

# XC-7824
   Scenario: To verify the Administration -> Account Management global navigation
     Given User capture the screenshot for "Administration" to "Account Management" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
   Scenario: To verify the Administration -> Admin Management global navigation for Administrator Management
     Given User capture the screenshot for "Administration" to "Admin Management" then "Administrator Management" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
   Scenario: To verify the Administration -> Admin Management global navigation for Role Based Access Control
     Given User capture the screenshot for "Administration" to "Admin Management" then "Role Based Access Control" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
   Scenario: To verify the Administration -> Admin Management global navigation for Audit Logs
     Given User capture the screenshot for "Administration" to "Admin Management" then "Audit Logs" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
     Given User capture the screenshot for "Administration" to "Identity" then "ZIdentity" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
   Scenario: To verify the Administration -> API Configuration global navigation for OneAPI
     Given User capture the screenshot for "Administration" to "API Configuration" then "OneAPI" in ZID
     Then Verify the difference between the screenshots for ZID user

## XC-7824
#   Scenario: To verify the Infrastructure -> Locations global navigation
#     Given User capture the screenshot for "Infrastructure" to "Locations" in ZID
#     Then Verify the difference between the screenshots for ZID user