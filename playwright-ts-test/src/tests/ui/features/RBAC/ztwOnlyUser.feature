@RBAC @xc
Feature: To verify the ZTW only user role based access

# XC-7825
  Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZTW Only User" username and password
    Then User login to the console successfully
    Then user clicks on Analytics

# # XC-7825
#    Scenario: To verify the user should not be able to view any widgets in networking dashboard
#     Given User is able to view the widgets heading "<widgets>"
#     When User verify the "Access Restricted" text along with "<widgets>"
#     Examples:
#        |            widgets          |
#        | Traffic in my Organization  |
#        |Internet Traffic Distribution|
#        |Top Locations sending Internet Traffic to Zscaler|
#        |Top Zscaler Data Centers Used|
#        |Devices Discovered           | 


Scenario: To verify Digital Experience, Cybersecurity and Devices menus are not visible
     Then User verify the "Digital Experience" menu is not visible
     Then User verify the "Cybersecurity" menu is not visible
     Then User verify the "Devices" submenu under "Operational" is not visible

# XC-7005, XC-7006, XC-7007
@connectoractivity 
  Scenario: Verify filters in Connector Activity Page - Branch Connectors
    When User is in Connector Activity Page
    Then Ensure that you are in "Branch Connectors" tab in Connector Activity Page
    Then Verify the filters under "branch-connector" in Connector Activity Page

# XC-7008
@connectoractivity 
  Scenario: Verify Traffic Volume Across Service - Branch Connectors
    When User is in Connector Activity Page
    Then Ensure that you are in "Branch Connectors" tab in Connector Activity Page
    Then Verify the title "Traffic Volume Across Service" under "Branch Connectors" in Connector Activity Page
    Then Ensure that "Traffic Volume Across Service" graph canvas of type "donut-chart" is present under "Branch Connectors" in Connector Activity Page

# # XC-7009
@connectoractivity 
  Scenario: Verify Session Across Service - Branch Connectors
    When User is in Connector Activity Page 
    Then Ensure that you are in "Branch Connectors" tab in Connector Activity Page
    Then Verify the title "Session Across Service" under "Branch Connectors" in Connector Activity Page
    Then Ensure that "Session Across Service" graph canvas of type "donut-chart" is present under "Branch Connectors" in Connector Activity Page

# # XC-7010, XC-7011, XC-7012
@connectoractivity 
  Scenario: Verify Connectors Table - Branch Connectors
    When User is in Connector Activity Page 
    Then Ensure that you are in "Branch Connectors" tab in Connector Activity Page
    Then Verify the title "Connectors" under "Branch Connectors" in Connector Activity Page
    Then Ensure that "Connectors" table along with Searchbar is present under "Branch Connectors" in Connector Activity Page
 
# # XC-7021, XC-7022, XC-7023
@connectoractivity 
  Scenario: Verify filters in Connector Activity Page - Cloud Connectors
    When User is in Connector Activity Page
    Then Ensure that you are in "Cloud Connectors" tab in Connector Activity Page
    Then Verify the filters under "cloud-connector" in Connector Activity Page

# # XC-7024
@connectoractivity 
  Scenario: Verify Traffic Volume Across Service - Cloud Connectors
    When User is in Connector Activity Page
    Then Ensure that you are in "Cloud Connectors" tab in Connector Activity Page
    Then Verify the title "Traffic Volume Across Service" under "Cloud Connectors" in Connector Activity Page
    Then Ensure that "Traffic Volume Across Service" graph canvas of type "donut-chart" is present under "Cloud Connectors" in Connector Activity Page

# # XC-7025
@connectoractivity 
  Scenario: Verify Session Across Service - Cloud Connectors
    When User is in Connector Activity Page 
    Then Ensure that you are in "Cloud Connectors" tab in Connector Activity Page
    Then Verify the title "Session Across Service" under "Cloud Connectors" in Connector Activity Page
    Then Ensure that "Session Across Service" graph canvas of type "donut-chart" is present under "Cloud Connectors" in Connector Activity Page

# # XC-7026, XC-7027, XC-7028
@connectoractivity 
  Scenario: Verify Connectors Table - Cloud Connectors
    When User is in Connector Activity Page 
    Then Ensure that you are in "Cloud Connectors" tab in Connector Activity Page
    Then Verify the title "Connectors" under "Cloud Connectors" in Connector Activity Page
    Then Ensure that "Connectors" table along with Searchbar is present under "Cloud Connectors" in Connector Activity Page

# # XC-7013 
# # XC-7026, XC-7027, XC-7028
@connectoractivity 
   Scenario: Verify timestamp
    When User is in Connector Activity Page 
    Then Ensure that you are in "Branch Connectors" tab in Connector Activity Page
    Then Verify the timestamp in Connector Activity Page

# # XC-7029
# # XC-7026, XC-7027, XC-7028
@connectoractivity 
   Scenario: Verify timestamp in Cloud Connector
    When User is in Connector Activity Page 
    Then Ensure that you are in "Cloud Connectors" tab in Connector Activity Page
    Then Verify the timestamp in Connector Activity Page

# XC-7825
   Scenario: To verify Deployment Status widget in the Appliances tab
    Given User open the URL
    When User navigates to the Appliances tab
    And User verify the title "Deployment Status"
    Then Verify the "Deployment Status Graph" graph canvas of type "donut-chart" is present in Appliances

# XC-7825
   Scenario: To verify Active Status widget in the Appliances tab
    Given User open the URL
    When User navigates to the Appliances tab
    And User verify the title "Active Status"
    Then Verify the "Active Status Graph" graph canvas of type "donut-chart" is present in Appliances

# XC-7825
   Scenario: To verify Deployed Connector table in the Appliances tab
    Given User open the URL
    When User navigates to the Appliances tab
    Then Verify the "Deployed Connectors" table in Appliances


# XC-7825
   Scenario: To verify the Administration -> Account Management global navigation
    Given User capture the screenshot for "Administration" to "Account Management" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Administration -> Admin Management global navigation for Administrator Management
    Given User capture the screenshot for "Administration" to "Admin Management" then "Administrator Management" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Administration -> Admin Management global navigation for Role Based Access Control
    Given User capture the screenshot for "Administration" to "Admin Management" then "Role Based Access Control" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Administration -> Admin Management global navigation for Audit Logs
    Given User capture the screenshot for "Administration" to "Admin Management" then "Audit Logs" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
    Given User capture the screenshot for "Administration" to "Identity" then "ZIdentity" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Administration -> API Configuration global navigation for Legacy API
    Given User capture the screenshot for "Administration" to "API Configuration" then "Legacy API" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Locations global navigation
    Given User capture the screenshot for "Infrastructure" to "Locations" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Connectors global navigation for Client
    Given User capture the screenshot for "Infrastructure" to "Connectors" then "Client" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Connectors global navigation for Edge
    Given User capture the screenshot for "Infrastructure" to "Connectors" then "Edge" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Connectors global navigation for Cloud
    Given User capture the screenshot for "Infrastructure" to "Connectors" then "Cloud" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Common Resources global navigation for Gateways
    Given User capture the screenshot for "Infrastructure" to "Common Resources" then "Gateways" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Common Resources global navigation for Application
    Given User capture the screenshot for "Infrastructure" to "Common Resources" then "Application" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Infrastructure -> Common Resources global navigation for Deployment
    Given User capture the screenshot for "Infrastructure" to "Common Resources" then "Deployment" in ZTW
    Then Verify the difference between the screenshots for ZTW user

# XC-7825
   Scenario: To verify the Logs -> Insights global navigation
    Given User capture the screenshot for "Logs" to "Insights" in ZTW
    Then Verify the difference between the screenshots for ZTW user