@RBAC @xc
Feature: To verify the ZTDS only user role based access

# XC-7826
 Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZTDS Only User" username and password
    Then User login to the console successfully
    Then user clicks on Analytics

# XC-7826
   Scenario: To verify the user should not be able to view any widgets in networking dashboard
      Given User is able to view the widgets heading "<widgets>"
      When User verify the "Access Restricted" text along with "<widgets>"
      Examples:
       |            widgets          |
       | Traffic in my Organization  |
       |Internet Traffic Distribution|
       |Top Locations sending Internet Traffic to Zscaler|
       |Top Zscaler Data Centers Used|
       |Devices Discovered           | 

Scenario: To verify  Networking, Digital Experience, Cybersecurity and Operational menus are not visible
     Then User verify the "Networking" menu is not visible
     Then User verify the "Digital Experience" menu is not visible
     Then User verify the "Cybersecurity" menu is not visible
     Then User verify the "Operational" menu is not visible

# XC-7826
   Scenario: To verify the Administration -> Account Management global navigation
    Given User capture the screenshot for "Administration" to "Account Management" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
    Given User capture the screenshot for "Administration" to "Identity" then "ZIdentity" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
   Scenario: To verify the Administration -> API Configuration global navigation
    Given User capture the screenshot for "Administration" to "API Configuration" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
   Scenario: To verify the Administration -> Alerts global navigation
    Given User capture the screenshot for "Administration" to "Alerts" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# # XC-7826
#    Scenario: To verify the Policies -> Access Control global navigation for Internet & SaaS
#     Given User capture the screenshot for "Policies" to "Access Control" then "Internet & SaaS" in ZTDS
#     Then Verify the difference between the screenshots for ZTDS user

# XC-7826
   Scenario: To verify the Policies -> Access Control global navigation for Segmentation
    Given User capture the screenshot for "Policies" to "Access Control" then "Segmentation" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user
    
# # XC-7826
#    Scenario: To verify the Infrastructure -> Locations global navigation
#     Given User capture the screenshot for "Infrastructure" to "Locations" in ZTDS
#     Then Verify the difference between the screenshots for ZTDS user

# XC-7826   
   Scenario: To verify the Infrastructure -> Connectors global navigation for Edge
    Given User capture the screenshot for "Infrastructure" to "Connectors" then "Edge" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
   Scenario: To verify the Logs -> Insights global navigation
    Given User capture the screenshot for "Logs" to "Insights" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user