@RBAC @xc
Feature: To verify the ZDX only user role based access

# XC-7820
Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZDX Only User" username and password
    Then User login to the console successfully
    Then user clicks on Analytics

# XC-7823
Scenario: To verify  Networking, Cybersecurity and Operational menus are not visible
  Then User verify the "Networking" menu is not visible
  Then User verify the "Cybersecurity" menu is not visible
  Then User verify the "Operational" menu is not visible

# #### Digital Experience ######

# XC-5963
@activity
Scenario: Verify filters in Activity Page 
  When User is in Activity Page
  Then Verify the filters in Activity page
  Then Verify the date dropdown in Activity page

# XC-5962
@activity
Scenario: Verify Active Users
  When User is in Activity Page
  Then Verify the title "Active Users" in Activity Page
  Then Verify the value of "Active Users" in Activity Page
  Then Ensure that "Active Users" graph canvas of type "trend-line-chart" is present in Activity Page

# XC-6291
@activity
Scenario: Verify Active Devices 
  When User is in Activity Page
  Then Verify the title "Active Devices" in Activity Page
  Then Verify the value of "Active Devices" in Activity Page
  Then Ensure that "Active Devices" graph canvas of type "trend-line-chart" is present in Activity Page
  
# XC-6292
@activity
Scenario: Verify User Distribution by Experience Score 
  When User is in Activity Page 
  Then Verify the title "User Distribution by Experience Score" in Activity Page 
  Then Verify the "User Distribution by Experience Score" users count in Activity Page

# XC-5964
@activity
Scenario: Verify Top 5 Applications with Lowest Experience Score
  When User is in Activity Page
  Then Verify the title "Top 5 Applications with Lowest Experience Score" in Activity Page
  Then Ensure that "Top 5 Applications with Lowest Experience Score" graph canvas of type "multiline-chart" is present in Activity Page

# XC-5965
@activity
Scenario: Verify Regions by Average Application Experience Score
  When User is in Activity Page
  Then Verify the title "Regions by Average Application Experience Score" in Activity Page
  Then Ensure that "Regions by Average Application Experience Score" graph canvas of type "world-bubbles-chart" is present in Activity Page

# XC-5966
@activity
Scenario: Verify Wi-Fi Performance
  When User is in Activity Page
  Then Verify the title "Wi-Fi Performance" in Activity Page
  Then Verify the value of "Wi-Fi Performance" in Activity Page
  Then Ensure that "Wi-Fi Performance" graph canvas of type "stacked-bar-chart" is present in Activity Page

 
# XC-5967
@application
Scenario: Verify Date filter in Application Page 
  When User is in Application Page
  Then Verify the date dropdown in Application page

# XC-5968
@application
Scenario: Verify filters in Application Page 
  When User is in Application Page
  Then Verify the filters in Application page

# XC-5969
@application
Scenario: Verify Application Experience Trend 
  When User is in Application Page 
  Then Verify the title "Application Experience Trend" in Application Page 
  Then Ensure that "Application Experience Trend" graph canvas of type "vertical-bar-chart" is present in Application Page
  Then Ensure that "Application" table is present in Application page

# XC-5939
@dedashboard
Scenario: Verify Date filter in Digital Experience 
  When User is in digital experience dashboard
  Then Verify the date dropdown in DE page
  Then Verify the filters in DE page

# XC-5940, XC-5943
@dedashboard
Scenario: Verify How is my overall Digital Experience 
  When User is in digital experience dashboard
  Then Verify the title "How is my overall Digital Experience?" in DE page
  Then Ensure that "How is my overall Digital Experience?" graph canvas of type "vertical-bar-chart" is present in DE page
  Then Ensure User can navigate to "Activity" Page while clicking on footer under "How is my overall Digital Experience?" in DE Page

# XC-5944, XC-5945
@dedashboard
Scenario: Verify the Network Latency Geoview
  When User is in digital experience dashboard
  Then Verify the title "Network Latency Geoview" in DE page
  Then Ensure that you are in "Zscaler" tab in DE page
  Then Ensure that "Network Latency Geoview" graph canvas of type "world-bubbles-chart" is present in DE page
  Then Ensure that you are in "DNS Resolution" tab in DE page
  Then Ensure that "Network Latency Geoview" graph canvas of type "world-bubbles-chart" is present in DE page

# XC-5948, XC-5952
@dedashboard
Scenario: Verify the Application Experience
  When User is in digital experience dashboard
  Then Verify the title "Application Experience" in DE page
  Then Verify the "Application Experience" apps count in DE page
  Then Ensure that "Application Experience" table is present in DE page
  Then Ensure User can navigate to "Application" Page while clicking on footer under "Application Experience" in DE Page

# XC-5951, XC-5953
@dedashboard
Scenario: Verify the Unified Communication Experience
  When User is in digital experience dashboard
  Then Verify the title "Unified Communication Experience" in DE page
  Then Ensure that "Unified Communication Experience" graph canvas of type "donut-chart" is present in DE page
  Then Ensure User can navigate to "Meetings" Page while clicking on footer under "Unified Communication Experience" in DE Page

# XC-5955, XC-5956, XC-5957, XC-5959
@dedashboard
Scenario: Verify What is impacting my User Experience
  When User is in digital experience dashboard
  Then Verify the title "What is impacting my User Experience?" in DE page
  Then Verify the Total Incidents and Impacted users under "What is impacting my User Experience?" in DE page
  Then Ensure the "Device", "Networking" and "Application" cards are present under "What is impacting my User Experience?" in DE page
  Then Ensure User can navigate to "Incidents" Page while clicking on footer under "Application" in "What is impacting my User Experience?" in DE Page

# XC-5961
@dedashboard
Scenario: Verify the End Point Self Service
  When User is in digital experience dashboard
  Then Verify the title "End Point Self Service" in DE page
  Then Ensure that "End Point Self Service" graph canvas of type "stacked-bar-chart" is present in DE page
  Then Ensure User can navigate to "Self Service" Page while clicking on footer under "End Point Self Service" in DE Page

# XC-5982
@incidents
Scenario: Verify filters in Incidents Page 
  When User is in Incidents Page
  Then Verify the Type filter in Incidents page
  Then Verify the date dropdown in Incidents page

# XC-5983, XC-5984
@incidents
Scenario: Verify Total Incidents and Impacted Users
  When User is in Incidents Page 
  Then Verify the title "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list title "Total Incidents" under "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list value "Total Incidents Value" under "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list title "Impacted Users" under "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list value "Impacted Users Value" under "Incidents Across Key Areas" in Incidents Page 

# XC-5987
@incidents
Scenario: Verify Incidents Over Time 
  When User is in Incidents Page 
  Then Verify the title "Incidents Over Time" in Incidents Page 
  Then Ensure that "Incidents Over Time" graph canvas of type "vertical-bar-chart" is present in Incidents Page

# XC-5988
@incidents
Scenario: Verify Impacted Users Over Time 
  When User is in Incidents Page 
  Then Verify the title "Impacted Users Over Time" in Incidents Page 
  Then Ensure that "Impacted Users Over Time" graph canvas of type "vertical-bar-chart" is present in Incidents Page

# XC-5989
@incidents
Scenario: Verify Incidents by Epicenters 
  When User is in Incidents Page 
  Then Verify the title "Incidents by Epicenters" in Incidents Page 
  Then Ensure that "Incidents by Epicenters" graph canvas of type "world-icon-chart" is present in Incidents Page

# XC-5991, XC-5992
@incidents
Scenario: Verify Incidents Table 
  When User is in Incidents Page 
  Then Verify the title "Incidents" in Incidents Page
  Then Ensure that "Incidents" Search Bar is present in Incidents page
  Then Ensure that "Incidents" table is present in Incidents page
 
# XC-6014
@meetings
Scenario: Verify filters in Meetings Page 
  When User is in Meetings Page
  Then Verify the filters in Meetings page
  Then Verify the date dropdown in Meetings page

# XC-6015, XC-6016
@meetings
Scenario: Verify Meetings Table 
  When User is in Meetings Page 
  Then Verify the title "Meetings" in Meetings Page 
  Then Ensure that "Meetings" Search Bar is present in Meetings page
  Then Ensure that "Meetings" table is present in Meetings page


# XC-6002
@selfservice
Scenario: Verify filters in Self Service Page 
  When User is in Self Service Page
  Then Verify the filters in Self Service page
  Then Verify the date dropdown in Self Service page

# XC-6004
@selfservice
Scenario: Verify Total Notifications Sent
  When User is in Self Service Page
  Then Verify the title "Total Notifications Sent" in Self Service Page
  Then Verify the value of "Total Notifications Sent" in Self Service Page

# XC-6005
@selfservice
Scenario: Verify Notifications By Type
  When User is in Self Service Page
  Then Verify the title "Notifications By Type" in Self Service Page
  Then Ensure that "Notifications By Type" graph canvas of type "stacked-bar-chart" is present in Self Service Page

# XC-6007
@selfservice
Scenario: Verify Total Users Notified
  When User is in Self Service Page
  Then Verify the title "Total Users Notified" in Self Service Page
  Then Verify the value of "Total Users Notified" in Self Service Page

# XC-6008
@selfservice
Scenario: Verify Users Found Notifications Helpful
  When User is in Self Service Page
  Then Verify the title "Users Found Notifications Helpful" in Self Service Page
  Then Verify the value of "Users Found Notifications Helpful" in Self Service Page

# XC-6009
@selfservice
Scenario: Verify Active Users With Self Service
  When User is in Self Service Page
  Then Verify the title "Active Users With Self Service" in Self Service Page
  Then Verify the value of "Active Users With Self Service" in Self Service Page

# XC-6010
@selfservice
Scenario: Verify Users Who Disabled Notifications
  When User is in Self Service Page
  Then Verify the title "Users Who Disabled Notifications" in Self Service Page
  Then Verify the value of "Users Who Disabled Notifications" in Self Service Page

# XC-6011
@selfservice
Scenario: Verify Notifications Sent Over Time
  When User is in Self Service Page
  Then Verify the title "Notifications Sent Over Time" in Self Service Page
  Then Ensure that "Notifications Sent Over Time" graph canvas of type "vertical-bar-chart" is present in Self Service Page

# XC-6012, XC-6013
@selfservice
Scenario: Verify Notifications Table
  When User is in Self Service Page
  Then Verify the title "Notifications" in Self Service Page
  Then Ensure that "Notifications" Search Bar is present in Self Service page
  Then Ensure that "Notifications" table is present in Self Service page

# XC-7823
   Scenario: To verify the Administration global navigation for ZDX Only user
     Given User capture the screenshot for ZDX "Administration" to "Account Management"
     Then Verify the difference between the screenshots

# XC-7823
   Scenario: To verify the Administration -> Admin Management global navigation for Role Based Access Control
     Given User capture the screenshot for ZDX "Administration" to "Admin Management" then "Role Based Access Control"
     Then Verify the difference between the screenshots

# XC-7823
   Scenario: To verify the Administration -> Admin Management global navigation for Audit Logs
     Given User capture the screenshot for ZDX "Administration" to "Admin Management" then "Audit Logs"
     Then Verify the difference between the screenshots

# XC-7823
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
     Given User capture the screenshot for ZDX "Administration" to "Identity" then "ZIdentity"
     Then Verify the difference between the screenshots

# XC-7823
   Scenario: To verify the Administration -> API Configuration navigation for Legacy API
     Given User capture the screenshot for ZDX "Administration" to "API Configuration" then "Legacy API"
     Then Verify the difference between the screenshots     

# XC-7823
   Scenario: To verify the Policies global navigation for ZDX Only user
     Given User capture the screenshot for ZDX "Policies" to "Digital Experience Monitoring"
     Then Verify the difference between the screenshots