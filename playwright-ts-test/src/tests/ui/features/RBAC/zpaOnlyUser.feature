@RBAC @xc
Feature: To verify the ZPA only user role based access
# XC-7822
Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZPA Only User" username and password
    Then User login to the console successfully
    Then user clicks on Analytics

Scenario: To verify Connector Activity, Digital Experience, Cybersecurity and Operational menus are not visible
     Then User verify the "Connector Activity" submenu under "Networking" is not visible
     Then User verify the "Digital Experience" menu is not visible
     Then User verify the "Cybersecurity" menu is not visible
     Then User verify the "Operational" menu is not visible

Scenario: To verify the user should not be able to view any widgets in networking dashboard
      Given User is able to view the widgets heading "<widgets>"
      When User verify the "Access Restricted" text along with "<widgets>"
      Examples:
       |            widgets          |
       |Internet Traffic Distribution|
       |Top Locations sending Internet Traffic to Zscaler|
       |Top Zscaler Data Centers Used|
       |Devices Discovered           | 

# XC-7821
   Scenario: To verify the ZPA Administration for Account Management
    Given User capture the screenshot for ZPA "Administration" to "Account Management"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
 Scenario: To verify the ZPA Administration -> Admin Management navigation for Role Based Access Control
    Given User capture the screenshot for ZPA "Administration" to "Admin Management" then "Role Based Access Control"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
 Scenario: To verify the ZPA Administration -> Admin Management navigation for Audit Logs
    Given User capture the screenshot for ZPA "Administration" to "Admin Management" then "Audit Logs"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
 Scenario: To verify the ZPA Administration -> Identity navigation for ZIdentity
    Given User capture the screenshot for ZPA "Administration" to "Identity" then "ZIdentity"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
 Scenario: To verify the ZPA Administration -> Identity navigation for Private Access
    Given User capture the screenshot for ZPA "Administration" to "Identity" then "Private Access"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
 Scenario: To verify the ZPA Administration -> API Configuration navigation for Legacy API
    Given User capture the screenshot for ZPA "Administration" to "API Configuration" then "Legacy API"
    Then Verify the difference between the screenshots for ZPA
    
# XC-7821
   Scenario: To verify the ZPA Administration for Alerts
    Given User capture the screenshot for ZPA "Administration" to "Alerts"
    Then Verify the difference between the screenshots for ZPA
        
# XC-7821
   Scenario: To verify the ZPA Administration for Backup & Restore
    Given User capture the screenshot for ZPA "Administration" to "Backup & Restore"
    Then Verify the difference between the screenshots for ZPA
  
# XC-7821
 Scenario: To verify the ZPA Policies -> Access Control navigation for Private Applications
    Given User capture the screenshot for ZPA "Policies" to "Access Control" then "Private Applications"
    Then Verify the difference between the screenshots for ZPA

  # XC-7821
 Scenario: To verify the ZPA Policies -> Access Control navigation for Clientless
    Given User capture the screenshot for ZPA "Policies" to "Access Control" then "Clientless"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
 Scenario: To verify the ZPA Policies -> Policies navigation for Inline Security
    Given User capture the screenshot for ZPA "Policies" to "Cybersecurity" then "Inline Security"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
Scenario: To verify the ZPA Infrastructure -> Private Access navigation for component
    Given User capture the screenshot for ZPA "Infrastructure" to "Private Access" then "Component"
    Then Verify the difference between the screenshots for ZPA
  
      # XC-7821
 Scenario: To verify the ZPA Infrastructure -> Private Access navigation for Business Continuity 
    Given User capture the screenshot for ZPA "Infrastructure" to "Private Access" then "Business Continuity"
    Then Verify the difference between the screenshots for ZPA
  
      # XC-7821
 Scenario: To verify the ZPA Infrastructure -> Private Access navigation for Client Connector Policies
    Given User capture the screenshot for ZPA "Infrastructure" to "Private Access" then "Client Connector Policies"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
   Scenario: To verify the ZPA Logs for Insights
    Given User capture the screenshot for ZPA "Logs" to "Insights"
    Then Verify the difference between the screenshots for ZPA

# XC-7821
   Scenario: To verify the ZPA Logs for Log Streaming
    Given User capture the screenshot for ZPA "Logs" to "Log Streaming"
    Then Verify the difference between the screenshots for ZPA
