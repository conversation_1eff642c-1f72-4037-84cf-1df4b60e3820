@home @xc
Feature: Verify the Home Page

# XC-9034, XC-9043, XC-9013, XC-9010, XC-9009, XC-9033, XC-9012
Scenario: Verify Banner in Home Page 
    When User is in Home Page
    Then Verify the Banner in Home Page

# XC-9018, XC-9041, XC-9017
Scenario: Verify Analytics Cards in Home Page 
    When User is in Home Page
    Then Verify the Analytics Cards in Home Page

Scenario: Verify Recently Viewed Container in Home Page 
    When User is in Home Page
    Then Verify the Recently Viewed Container in Home Page

# XC-9026, XC-9028, XC-9029, XC-9027
Scenario: Verify News Container in Home Page 
    When User is in Home Page
    Then Verify the News Container in Home Page

# XC-9031, XC-9030
Scenario: Verify Learn About Products Container in Home Page 
    When User is in Home Page
    Then Verify the Learn About Products Container in Home Page

# XC-9032
Scenario: Verify Resources Container in Home Page 
    When User is in Home Page
    Then Verify the Resources Container in Home Page

# XC-9040
Scenario: Verify Resize in Home Page 
    When User is in Home Page
    Then Verify the Resize for all Containers in Home Page

# XC-9036
Scenario: Verify Remove in Home Page 
    When User is in Home Page
    Then Verify the Remove for all Containers in Home Page

# XC-9014
Scenario: Verify Banner updated automatically in Home Page 
    When User is in Home Page
    Then Verify the Banner updated automatically in Home Page

# XC-9043
Scenario: Verify Back button functionality in Home Page 
    When User is in Home Page
    Then Verify the back button functionality in Home Page

# XC-9039
Scenario: Verify Cancel button functionality in Home Page
    When User is in Home Page
    Then Verify the cancel button functionality in Home Page

# XC-9020, XC-9021, XC-9035, XC-9038
Scenario: Verify Customize button functionality in Home Page
    When User is in Home Page
    Then Verify the Customize button functionality in Home Page