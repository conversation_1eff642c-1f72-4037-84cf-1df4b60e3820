@xc
Feature: To verify the Devices component in Operational

# XC-7823
   Scenario: To verify the user devices graph
     Given User open the URL
     Given User navigates to the Operational "Devices" tab
     When User verify the "User Devices" title, tooltip and user devices count
     Then Verify the "User Devices Graph" graph canvas of type "stacked-bar-chart"

# XC-7823
   Scenario: To verify the user devices distribution tab
     Given User open the URL
     Given User navigates to the Operational "Devices" tab
     Then User clicks on the graph and verify the tab details

# XC-7823
   Scenario: To verify the version distribution graph
     Given User open the URL
     Given User navigates to the Operational "Devices" tab
     When User verify the "Version Distribution" title in devices tab
     Then Verify the "Version Distribution Graph" graph canvas of type "donut-chart"

# XC-7823
   Scenario: To verify the device operating system graph
     Given User open the URL
     Given User navigates to the Operational "Devices" tab
     When User verify the "Device Operating System" title in devices tab
     Then Verify the "Device Operating System Graph" graph canvas of type "horizontal-bar-chart"

# XC-7823
   Scenario: To verify the Device Model graph
     Given User open the URL
     Given User navigates to the Operational "Devices" tab
     When User verify the "Device Model" title in devices tab
     Then Verify the "Device Model Graph" graph canvas of type "horizontal-bar-chart"

# XC-7823
   Scenario: To verify the Device State graph
     Given User open the URL
     Given User navigates to the Operational "Devices" tab
     When User verify the "Device State" title in devices tab
     Then Verify the "Device State Graph" graph canvas of type "donut-chart"