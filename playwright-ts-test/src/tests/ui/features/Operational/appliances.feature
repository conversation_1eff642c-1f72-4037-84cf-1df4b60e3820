@xc
Feature: To verify the Appliances component in Operational

# XC-7825
   Scenario: Verify filters in Appliances page
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    Then Verify the filters in Operational Appliances page

# XC-7825
   Scenario: To verify Deployment Status widget in the Appliances tab
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    And User verify the title "Deployment Status"
    Then Verify the "Deployment Status Graph" graph canvas of type "donut-chart" is present in Appliances

# XC-7825
   Scenario: To verify Active Status widget in the Appliances tab
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    And User verify the title "Active Status"
    Then Verify the "Active Status Graph" graph canvas of type "donut-chart" is present in Appliances

# XC-7825
   Scenario: To verify Deployed Connector table in the Appliances tab
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    Then Verify the "Deployed Connectors" table in Appliances
