@UnifiedLocations
Feature: Verify the unified locations functionality
# XC-7134
  Scenario: To verify the search functionality in the locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "America" in the search bar
    Then Verify the details in the locations
# XC-7089
  Scenario: To verify search results according to type of locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "America" in the search bar
    And Clicks on the "Edge" tab
    And Verify the details in the locations
    Then Clicks on the "Cloud" tab
    And Verify the No result found message
# XC-8137
  Scenario: To verify the Overview in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "America" in the search bar
    And Clicks on the name data "America"
    Then Verify the "Overview" with data "America" of the locations
# XC-7099 XC-7096
  Scenario: To verify the Connection Types in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "America" in the search bar
    And Clicks on the connection type data
    Then Verify the "IPsec/GRE" with data "IPsec/GRE" of the locations
# XC-8137
  Scenario: To verify the Sublocations in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "America" in the search bar
    And Clicks on the Sublocations data
    Then Verify the "Sublocations" with data "Sublocations" of the locations
# XC-7150 XC-7865 XC-8042
  Scenario: To verify the Add IPSec Location functionality with traffic type as Corporate user traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location Corporate user traffic"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Corporate user traffic"
    And Delete the location
# XC-8047 XC-7866 XC-8040
  Scenario: To verify the Add IPSec Location functionality with traffic type as Guest Wi-Fi traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location Guest Wi-Fi traffic"
    And User select the "Exclude from Dynamic Location Groups" checkbox
    Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Guest Wi-Fi traffic"
    And Delete the location
# XC-8043 XC-7869
  Scenario: To verify the Add IPSec Location functionality with traffic type as IoT traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location IoT traffic"
    And User select the "Exclude from Manual Location Groups" checkbox
    And User select the "Exclude from Dynamic Location Groups" checkbox
    Then User fill in the traffic type as "IoT traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location IoT traffic"
    And Delete the location
# XC-8041 XC-8045 XC-7870 XC-809
  Scenario: To verify the Add IPSec Location functionality with traffic type as Server traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location Server traffic"
    Then User fill in the traffic type as "Server traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Server traffic"
    And Delete the location

  Scenario: To verify Add Edge Location functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add Edge Location" button
    Then Verify the user is redirected to the branch provisioning

  Scenario: To verify Add Cloud Location functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add Cloud Location" button
    Then Verify the user is redirected to the provisioning

  Scenario: To verify the pagination of locations screen
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify the rows per page and select the "100"

  Scenario: To verify the Sync locations button is clickable
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify the sync locations button is clickable

  # Scenario: To verify the add location gets an error for a duplicate location name
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User clicks on the "Add IPSec Location" button
  #   And User enter the mandatory values
  #   And User select the "Exclude from Manual Location Groups" checkbox
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   And User clicks on the "Add IPSec Location" button
  #   And User enter the mandatory values
  #   And User select the "Exclude from Manual Location Groups" checkbox
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   And Verify the duplicate name error message
  #   Then Verify the IPSec created
  #   And Delete the location

  Scenario: To verify the edit Overview functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    And User search "IPSec Location" in the search bar
    And Clicks on the name data "IPSec Location"
    Then Verify the "Overview" with data "IPSec Location" of the locations
    And Verify the details mentioned in the Overview
    And User clicks on the edit and fill in all the details
    Then User save the changes and verify the details
    And User search "Location Edit" in the search bar
    Then Delete the location

  Scenario: To verify the edit Connection Types functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location Edit IPSec/GRE"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    And User search "IPSec Location" in the search bar
    And Clicks on the connection type data
    Then Verify the "IPsec/GRE" with data "IPsec/GRE" of the locations
    And User edit the IPSec GRE in locations
    Then Verify the updated changes
    And User search "IPSec Location Edit IPSec/GRE" in the search bar
    Then Delete the location

  Scenario: To verify the empty state in Appliances
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location Appliances"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    And User search "IPSec Location Appliances" in the search bar
    And Clicks on the name data "IPSec Location Appliances"
    And Switch to "Appliances" tab
    Then Verify the empty state in Appliances

  Scenario: To verify the edit Connection Options functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec Location" button
    And User enter the mandatory values with name "IPSec Location Appliances"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    And User search "IPSec Location Appliances" in the search bar
    And Clicks on the name data "IPSec Location Appliances"