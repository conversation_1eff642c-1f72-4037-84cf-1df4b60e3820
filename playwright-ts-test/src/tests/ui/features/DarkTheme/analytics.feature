@darkTheme @xc
Feature: Verify the networking component

@networking
Scenario: Verify Dark Theme in Networking Page
  When User is in Networking Page
  Then User fetches the background theme color
  And Verify the theme is "Dark"

@connectoractivity
Scenario: Verify Dark Theme in Connector Activity Page
    When User is in Connector Activity Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@dedashboard
Scenario: Verify Dark Theme in digital experience dashboard Page    
    When User is in digital experience dashboard
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@activity
Scenario: Verify Dark Theme in Activity Page    
    When User is in Activity Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@application
Scenario: Verify Dark Theme in Application Page    
    When User is in Application Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@incidents
Scenario: Verify Dark Theme in Incidents Page    
    When User is in Incidents Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@meetings
Scenario: Verify Dark Theme in Meetings Page    
    When User is in Meetings Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@selfservice
Scenario: Verify Dark Theme in Self Service Page    
    When User is in Self Service Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Cybersecurity Page    
    Given User navigates to the cybersecurity
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Transactional Activity Page    
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Advanced Threats Page    
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Advanced Threats >" button in the "Advanced Threat Categories"
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Sandbox Threats Page    
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User navigates to the Sandbox threat tab
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Threat Locations Page    
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Threat Locations >" button in "Top Threat Locations" Threat Locations
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in SSL/TLS Inspection Page    
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on "View SSL/TLS Inspection >" button in "Your SSL/TLS Inspection Review"
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Operational devices Page    
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    Then User fetches the background theme color
    And Verify the theme is "Dark"

Scenario: Verify Dark Theme in Operational Appliances Page    
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    Then User fetches the background theme color
    And Verify the theme is "Dark"