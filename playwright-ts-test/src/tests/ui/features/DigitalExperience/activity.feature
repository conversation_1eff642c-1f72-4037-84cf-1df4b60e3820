@activity @xc
Feature: Check Digital Experience > Activity

# XC-5963
Scenario: Verify filters in Activity Page 
  When User is in Activity Page
  Then Verify the filters in Activity page
  Then Verify the date dropdown in Activity page

# XC-5962
Scenario: Verify Active Users
  When User is in Activity Page
  Then Verify the title "Active Users" in Activity Page
  Then Verify the value of "Active Users" in Activity Page
  Then Ensure that "Active Users" graph canvas of type "trend-line-chart" is present in Activity Page

# XC-6291
Scenario: Verify Active Devices 
  When User is in Activity Page
  Then Verify the title "Active Devices" in Activity Page
  Then Verify the value of "Active Devices" in Activity Page
  Then Ensure that "Active Devices" graph canvas of type "trend-line-chart" is present in Activity Page
  
# XC-6292
Scenario: Verify User Distribution by Experience Score 
  When User is in Activity Page 
  Then Verify the title "User Distribution by Experience Score" in Activity Page 
  Then Verify the "User Distribution by Experience Score" users count in Activity Page

# XC-5964
Scenario: Verify Top 5 Applications with Lowest Experience Score
  When User is in Activity Page
  Then Verify the title "Top 5 Applications with Lowest Experience Score" in Activity Page
  Then Ensure that "Top 5 Applications with Lowest Experience Score" graph canvas of type "multiline-chart" is present in Activity Page

# XC-5965
Scenario: Verify Regions by Average Application Experience Score
  When User is in Activity Page
  Then Verify the title "Regions by Average Application Experience Score" in Activity Page
  Then Ensure that "Regions by Average Application Experience Score" graph canvas of type "world-bubbles-chart" is present in Activity Page

# XC-5966
Scenario: Verify Wi-Fi Performance
  When User is in Activity Page
  Then Verify the title "Wi-Fi Performance" in Activity Page
  Then Verify the value of "Wi-Fi Performance" in Activity Page
  Then Ensure that "Wi-Fi Performance" graph canvas of type "stacked-bar-chart" is present in Activity Page