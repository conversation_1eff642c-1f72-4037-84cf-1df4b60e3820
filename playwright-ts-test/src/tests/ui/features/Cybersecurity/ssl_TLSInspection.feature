@xc
Feature: Verify the SSL TLS Inspection component

# XC-4814
   # Background: Login to the Zscaler Console with valid credentails
   #  Given User open the URL
   #  When User enter the valid "One UI User" username and password
   #  Then User login to the console successfully

# XC-6169 XC-6196
   Scenario: To verify the Threats Blocked by SSL TLS Inspection
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on "View SSL/TLS Inspection >" button in "Your SSL/TLS Inspection Review"
    Then Verify the "Threats Blocked by SSL/TLS Inspection" graph canvas of type "vertical-bar-chart" is present in SSL TLS Inspection
    Then Verify the "Threats Blocked by SSL/TLS Inspection" graph canvas of type "donut-chart" is present in SSL TLS Inspection

# XC-6198
   Scenario: To verify the Top High Volume Applications bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on "View SSL/TLS Inspection >" button in "Your SSL/TLS Inspection Review"
    Then Verify the "Top High Volume Applications" graph canvas of type "horizontal-bar-chart" is present in SSL TLS Inspection

# XC-6199
   Scenario: To verify the Top Locations graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on "View SSL/TLS Inspection >" button in "Your SSL/TLS Inspection Review"
    Then Verify the "Top Locations" graph canvas of type "horizontal-bar-chart" is present in SSL TLS Inspection

# XC-6200
   Scenario: To verify the Top URL Categories bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on "View SSL/TLS Inspection >" button in "Your SSL/TLS Inspection Review"
    Then Verify the "Top URL Categories" graph canvas of type "horizontal-bar-chart" is present in SSL TLS Inspection