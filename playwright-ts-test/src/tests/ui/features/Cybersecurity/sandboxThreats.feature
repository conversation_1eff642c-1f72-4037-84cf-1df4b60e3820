@xc
Feature: Verify the sandbox threats dashboard

# XC-4814
  # Background: Login to the Zscaler Console with valid credentails
  #   Given User open the URL
  #   When User enter the valid "One UI User" username and password
  #   Then User login to the console successfully

   Scenario: To verify the Policy Actions & Verdicts for Files Known by Cloud Effect widget
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User navigates to the Sandbox threat tab
    Then Verify the "Policy Actions & Verdicts for Files Known by Cloud Effect" graph canvas of type "multiline-chart" is present in sandbox threats

   Scenario: To verify the Policy Actions & Verdicts for Unknown Files widget
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User navigates to the Sandbox threat tab
    Then Verify the "Policy Actions & Verdicts for Unknown Files" graph canvas of type "multiline-chart" is present in sandbox threats

# XC-6186
   Scenario: To verify the Top Users Generating Sandbox Threats widget
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User navigates to the Sandbox threat tab
    Then Verify the "Top Users Generating Sandbox Threats" graph canvas of type "multiline-chart" is present in sandbox threats

# XC-6187
   Scenario: To verify the Top Sandbox Incidents widget
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User navigates to the Sandbox threat tab
    Then Verify the "Top Sandbox Incidents" table in sandbox threats