@xc
Feature: Verify the Threat Locations component

# XC-4814
   # Background: Login to the Zscaler Console with valid credentails
   #  Given User open the URL
   #  When User enter the valid "One UI User" username and password
   #  Then User login to the console successfully
    
# XC-6161 XC-6190 
   Scenario: To verify the Threat Locations widgets
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Threat Locations >" button in "Top Threat Locations" Threat Locations

# XC-6189
   Scenario: To verify the Total Threats Trend graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Threat Locations >" button in "Top Threat Locations" Threat Locations
    Then Verify the "Total Threats Trend" graph canvas of type "multiline-chart" is present in threat locations

# XC-6191 XC-6192
   Scenario: To verify the Top Threat Categories graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Threat Locations >" button in "Top Threat Locations" Threat Locations
    Then Verify the "Top Threat Categories" graph canvas of type "bubble-chart" is present in threat locations

# XC-6193
   Scenario: To verify the Top Users Generating Threats bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Threat Locations >" button in "Top Threat Locations" Threat Locations
    Then Verify the "Top Users Generating Threats" graph canvas of type "horizontal-bar-chart" is present in threat locations

# XC-6194
   Scenario: To verify the Top Applications Generating Threats bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View All Threat Locations >" button in "Top Threat Locations" Threat Locations
    Then Verify the "Top Applications Generating Threats" graph canvas of type "horizontal-bar-chart" is present in threat locations