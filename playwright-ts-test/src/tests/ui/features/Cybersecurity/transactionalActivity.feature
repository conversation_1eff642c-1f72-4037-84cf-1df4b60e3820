@xc
Feature: Verify the Cybersecurity Transactional Activity component

# XC-4814
#   Background: Login to the Zscaler Console with valid credentails
#     Given User open the URL
#     When User enter the valid "One UI User" username and password
#     Then User login to the console successfully

# XC-6153
   Scenario: To verify the Transactional Activity tab
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity

# XC-6170 XC-6172
   Scenario: To verify the All Threats graph  
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    Then Verify the "All Threats" graph canvas of type "multiline-chart" is present in transactional activity

   Scenario: To verify the Top Users Generating Threats horizontal bar graph
    Given User navigates to the cybersecurity  
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    Then Verify the "Top Users Generating Threats" graph canvas of type "horizontal-bar-chart" is present in transactional activity

# XC-6173
   Scenario: To verify the Top Departments Generating Threats horizontal bar graph
    Given User navigates to the cybersecurity  
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    Then Verify the "Top Departments Generating Threats" graph canvas of type "horizontal-bar-chart" is present in transactional activity

# XC-6174
   Scenario: To verify the Your Policy Blocks graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    And User selects the "Policy Blocks" activity tab
    Then Verify the "Your Policy Blocks" graph canvas of type "multiline-chart" is present in transactional activity

   Scenario: To verify the Top Application Class bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    And User selects the "Policy Blocks" activity tab
    Then Verify the "Top Application Class" graph canvas of type "horizontal-bar-chart" is present in transactional activity

   Scenario: To verify the Top URL Categories Blocked bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    And User selects the "Policy Blocks" activity tab
    Then Verify the "Top URL Categories Blocked" graph canvas of type "horizontal-bar-chart" is present in transactional activity

# XC-6177
   Scenario: To verify the Top Cloud Applications Blocked bubble graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    And User selects the "Policy Blocks" activity tab
    Then Verify the "Top Cloud Applications Blocked" graph canvas of type "bubbles-chart" is present in transactional activity

# XC-6178
   Scenario: To verify the Top Blocked File Types no results found
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User clicks on the "View Transaction Activity >" button in "Malicious Transactions" Transactional activity
    And User selects the "Policy Blocks" activity tab
    Then Verify the "Top Blocked File Types" graph canvas of type "horizontal-bar-chart" is present in transactional activity