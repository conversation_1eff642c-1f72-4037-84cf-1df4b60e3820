@xc
Feature: Verify the Cybersecurity dashboard

# # XC-4814
#   Background: Login to the Zscaler Console with valid credentails
#     Given User open the URL
#     When User enter the valid "One UI User" username and password
#     Then User login to the console successfully

   Scenario: To verify the cybersecurity dashboard
    Given User navigates to the cybersecurity
    When User verify the widget present in the dashboard
    
# XC-6149
   Scenario Outline: To verify the date dropdown
    Given User navigates to the cybersecurity
    Then User clicks on the date dropdown "<days>" and verify the selection
    Examples:
       | days  |
       |1 Day  |
       |7 Days |
       |30 Days|
       |90 Days|

   Scenario: To verify the transactions widget in Cybersecurity for malicious transactions
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User verify the "Total Threats Blocked"
    Then Verify the "Total Threats Blocked Graph" graph canvas of type "multiline-chart" is present in cybersecurity dashboard

# XC-6150
   Scenario: To verify the transactions widget in Cybersecurity for Policy Blocks
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    And User toggles to the "Policy Blocks" label
    And User verify the "Total Policy Blocks"
    Then Verify the "Total Policy Blocks Graph" graph canvas of type "multiline-chart" is present in cybersecurity dashboard

# XC-6154 
   Scenario: To verify the Advanced threats Categories bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    Then Verify the "Advanced Threat Categories" graph canvas of type "horizontal-bar-chart" is present in cybersecurity dashboard

   Scenario: To verify the Top Threat Locations bar graph
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    Then Verify the "Top Threat Locations" graph canvas of type "horizontal-bar-chart" is present in cybersecurity dashboard

# XC-6166
   Scenario: To verify the SSL/TLS Inspection Review bar line chart
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    Then Verify the "Your SSL/TLS Inspection Review" graph canvas of type "bar-line-chart" is present in cybersecurity dashboard

# XC-6162
   Scenario: To verify the Sandbox Threats widget
    Given User navigates to the cybersecurity
    When User verify the "Your Cybersecurity Transactions" title
    Then Verify the "Sandbox Threats" graph canvas of type "horizontal-bar-chart" is present in cybersecurity dashboard