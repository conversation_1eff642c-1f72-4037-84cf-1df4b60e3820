import { expect, <PERSON> } from "@playwright/test";
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config } from "dotenv";
import { SelectorsType, TableRowType } from "../../../../../resources/types/types";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Dashboard {
  private page: Page;
  private readonly deDateFilter: string[];
  private readonly deFilters: string[];
  private readonly Selectors: SelectorsType;
  private readonly tabSelectors: SelectorsType;
  private readonly routes: SelectorsType;
  private TableRows: TableRowType;
  
  constructor(page: Page) {
      this.page = page;

      this.routes = {
          "Activity": "analytics/digital-experience/activity",
          "Application": "analytics/digital-experience/applications",
          "Incidents": "analytics/digital-experience/incidents",
          "Self Service": "analytics/digital-experience/self-service",
          "Meetings": "analytics/digital-experience/meetings",
      }
      
      this.deDateFilter = [
          "2 Hours",
          "4 Hours",
          "6 Hours",
          "12 Hours",
          "24 Hours",
          "48 Hours",
          "custom",
      ];
      
      this.deFilters= [
          "Departments",
          "Zscaler Locations",
          "User Groups",
          "Geolocations",
        ];
      
      this.Selectors = {
          "How is my overall Digital Experience?": "digital-experience-overall-DE",
          "Network Latency Geoview": "digital-experience-net-latency-geo-view",
          "Application Experience": "digital-experience-application-experience",
          "Unified Communication Experience": "digital-experience-unified-communication-experience",
          "What is impacting my User Experience?": "digital-experience-user-experience",
          "End Point Self Service": "digital-experience-end-point-self-service",
          "Device": "impacting-user-experience-card-user-exp-0",
          "Networking": "impacting-user-experience-card-user-exp-1",
          "Application": "impacting-user-experience-card-user-exp-2",
          "What is impacting my User Experience? - Application": "user-experience-application",
      };
      
      this.tabSelectors = {
          "Zscaler": "digital-experience-net-latency-geo-view-segment-control-0",
          "DNS Resolution": "digital-experience-net-latency-geo-view-segment-control-1"
      };
      
      this.TableRows = {
          "Application Experience": {
              "app_id_name": "Application Name",
              "users": "Users",
              "upm_app_score": "Score",
          },
          "Networking": {
              "incidentType":"Incident Type",
              "incidents":"Incidents",
              "usersImpacted":"Total Users Impacted",
              "incidentDuration":"Average Incident Duration",
          },
          "Application": {
              "incidentDate":"Incident Date",
              "applicationName":"Application Name",
              "usersImpacted":"Users Impacted",
              "incidentDuration":"Incident Duration",
              "location":"Location",
          },
      }
  }

  async navigateToDE(): Promise<void> {
      if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
      await this.page.goto(url);
      await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
      await this.page.getByTestId("nav-pills-tab-0").click();
      await this.page.waitForTimeout(10000);
      await PlaywrightActions.closePopup(this.page);
      await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-button").click();
      await this.page.waitForTimeout(10000);
  }

  async checkDeDateFilter(): Promise<void> {
      await PlaywrightActions.checkDateFilter(this.page, "digital-experience", this.deDateFilter);
  }

  async checkDeFilters(): Promise<void> {
      await PlaywrightActions.checkFilters(this.page, "digital-experience", this.deFilters);
  }
  
  async checkFooterNavigaion(navigationPage: string, title: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
      if(!needData)
      {
          await PlaywrightActions.footerNavigaion(this.page, `${this.Selectors[title]}-footer-navLink-z-button-link`, url+this.routes[navigationPage]);
      }
  }

  async checkPageUrl(): Promise<void> {
      await PlaywrightActions.checkUrl(this.page, url+"analytics/digital-experience");
  }

  async checkContainerTitle(title: string): Promise<void> {
      await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
  }

  async checkGraph(title: string, grapType: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
      if(!needData)
      {
          await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${grapType}`);
      }
  }

  async switchToTab(tabName: string): Promise<void> {
      await PlaywrightActions.checkSwitchableTabs(this.page, this.tabSelectors[tabName]);
  }

  async checkTable(tableName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[tableName]);
      if(!needData)
      {
          let hover: boolean= true;
          await PlaywrightActions.checkTable(this.page, `${this.Selectors[tableName]}-z-table`, this.TableRows[tableName], hover);
      }
  }

  async checkAppsCount(containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
      if(!needData)
      {
          let poorApps = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-z-list-value-0`);
          let okayApps = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-z-list-value-1`);
          let goodApps = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-z-list-value-2`);
          await expect(poorApps+okayApps+goodApps).toBeGreaterThanOrEqual(0);
      }
  }

  async checkIncidentsAndUsers(containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
      if(!needData)
      {
          let totalIncidents = await PlaywrightActions.getValue(this.page, `user-experience-z-list-value-0`);
          let impactedUsers = await PlaywrightActions.getValue(this.page, `user-experience-z-list-value-1`);
          await expect(totalIncidents).toBeGreaterThanOrEqual(0);
          await expect(impactedUsers).toBeGreaterThanOrEqual(0);
      }
  }

  async checkCards(cardName: string, containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
      if(!needData)
      {
          let hover: boolean= true;
          await PlaywrightActions.checkCards(this.page, cardName, this.Selectors[cardName], this.TableRows[cardName], hover);
      }
  }

  async expandCardFooterNavigation(navigationPage: string, cardName: string, containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
      if(!needData)
      {
          await expect(this.page.getByTestId(`${this.Selectors[cardName]}-disabled`)).toBeVisible();
          const text = await this.page.locator(`(//div[@data-testid='${this.Selectors[cardName]}']/div/div)[1]`).textContent();
          await expect(text).toBe(cardName);
          const incidents: string | null = await this.page.locator(`(//div[@data-testid='${this.Selectors[cardName]}']/div/ul)`).textContent();
          await this.page.locator(`(//div[@data-testid='${this.Selectors[cardName]}']/div/div)[1]`).click();
          if(incidents && Number(incidents[0]) > 0 )
          {
              let hover: boolean= false;
              await expect(this.page.getByTestId(`${this.Selectors[cardName]}-expanded`)).toBeVisible();
              const tableName = cardName.toLowerCase();
              await PlaywrightActions.checkTable(this.page, `${tableName}-table-z-table`, this.TableRows[cardName], hover);
              await this.checkFooterNavigaion(navigationPage, `${containerName} - ${cardName}`)
          }
      }
  }

}

export default Dashboard;
