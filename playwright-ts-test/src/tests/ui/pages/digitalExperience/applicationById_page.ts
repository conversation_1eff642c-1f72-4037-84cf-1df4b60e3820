import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { SelectorsType, TableRowType } from "../../../../../resources/types/types";

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class ApplicationByIdPage {
    private page: Page;
    private readonly applicationsByIdDateFilter: string[];
    private readonly applicationsByIdFilters: string[];
    private readonly Selectors: SelectorsType;
    private readonly TableSelectors: SelectorsType;
    private TableRows: TableRowType;
    
    constructor(page: Page) {
        this.page = page;

        this.applicationsByIdDateFilter = [
            "2 Hours",
            "4 Hours",
            "6 Hours",
            "12 Hours",
            "24 Hours",
            "48 Hours",
            "custom",
          ];
        
        this.applicationsByIdFilters= [
            "Departments",
            "Zscaler Locations",
            "User Groups",
            "Geolocations",
            "Location Groups",
            "Last Mile ISPs",
            "Operating System",
          ];
        
        this.Selectors = {
            "ZDX Score": "app-by-id-zdx-score",    
            "Page Fetch Time": "app-by-id-page-fetch-time",
            "Regions by ZDX Score": "app-by-id-regions-zdx-score",
            "Impacted Departments": "app-by-id-impacted-departments",
            "Impacted Regions": "app-by-id-impacted-regions",
            "Impacted Zscaler Locations": "app-by-id-impacted-locations",
            "Probe Statuses": "app-by-id-probe-sts",
        };
        
        this.TableSelectors = {
            "Probe Statuses": "app-by-id-probe-sts",   
            "Application": "applications-app-table",  
        };
        
        this.TableRows = {
            "Probe Statuses": {
                "metric": "Metric",
                "min": "Min",
                "avg": "Avg",
                "max": "Max",
            }
        }
    }

    async navigateToApplicationById(): Promise<void> {
        if (!url) {
            throw new Error('ONE_UI_BASE_URL environment variable is not set');
          }
        await this.page.goto(url);
        await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
        await this.page.getByTestId("nav-pills-tab-0").click();
        await this.page.waitForTimeout(10000);
        await PlaywrightActions.closePopup(this.page);
        await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-expansion-expansion-button").click();
        await this.page.waitForTimeout(5000);
        await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_de_applications-button").click();
        await this.page.waitForTimeout(10000);
        await this.page.getByTestId(`${this.TableSelectors["Application"]}-z-table-z-list-list-item-item-0`).hover();
        await this.page.getByTestId(`${this.TableSelectors["Application"]}-z-table-z-list-list-item-item-0`).click();
        await this.page.waitForTimeout(10000);
    }

    async checkPageUrl(): Promise<void> {
        const baseUrl = process.env.ONE_UI_BASE_URL?.replace(/\/$/, "");
        const url = new URL(this.page.url());

        await expect(url.origin).toBe(baseUrl);
        await expect(url.pathname).toBe("/analytics/digital-experience/applications/details");

        const id = url.searchParams.get("id");
        const name = url.searchParams.get("name");

        await expect(id && /^\d+$/.test(id)).toBe(true);
        await expect(name).toBeTruthy();
    }

    async checkApplicationByIdDateFilter(): Promise<void> {
        await PlaywrightActions.checkDateFilter(this.page, "digital-experience-applications", this.applicationsByIdDateFilter);
    }

    async checkApplicationByIdFilters(): Promise<void> {
        await PlaywrightActions.checkFilters(this.page, "analytics-layout", this.applicationsByIdFilters);
    }

    async checkContainerTitle(title: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
    }
    
    async checkGraph(title: string, grapType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${grapType}`);
        }
    }

    async checkProgressContainer(progressCntName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[progressCntName]);
        if(!needData)
        {
            await PlaywrightActions.checkProgressContainer(this.page, this.Selectors[progressCntName]);
        }
    }

    async checkProbesStatus(probeCntName:string): Promise<void> {
        let probe = await this.page.getByTestId(`${this.TableSelectors[probeCntName]}-tab-0-item`).isVisible();
        if (probe)
        {
            let hover = false;
            await this.page.getByTestId(`${this.TableSelectors[probeCntName]}-tab-0-item`).click();
            await expect(this.page.getByTestId(`${this.TableSelectors[probeCntName]}-tab-0-expanded`)).toBeVisible();
            await PlaywrightActions.checkTable(this.page, `${this.TableSelectors[probeCntName]}-z-table`, this.TableRows[probeCntName], hover)
        }
    }
}

export default ApplicationByIdPage;