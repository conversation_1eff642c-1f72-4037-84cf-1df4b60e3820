import { Page } from "@playwright/test";
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config } from "dotenv";
import { SelectorsType, TableRowType } from "../../../../../resources/types/types";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Meetings {
  private page: Page;
  private readonly meetingsDateFilter: string[];
  private readonly meetingsFilters: string[];
  private readonly TableSelectors: SelectorsType;
  private TableRows: TableRowType;
  constructor(page: Page) {
      this.page = page;

      this.meetingsDateFilter = [
          "2 Hours",
          "4 Hours",
          "6 Hours",
          "12 Hours",
          "24 Hours",
          "48 Hours",
          "custom",
        ];
      
      this.meetingsFilters= [
          "Application",
          "Departments",
          "Zscaler Locations",
          "User Groups",
          "Location Groups",
          "Geolocations",
          "Last Mile ISPs",
          "Operating System",
        ];
      
      this.TableSelectors = {
          "Meetings": "meetings-meeting-table",    
      };
      
      this.TableRows = {
          "Meetings": {
              "meet_id": "Meeting ID",
              "meet_host_id_name": "Application Name",
              "meet_mos": "MOS Score",
              "host": "Host",
              "meet_started_on": "Start Time",
              "duration": "Duration",
              "meet_participants_count": "Active Participants",
          }
      }
  }

  async navigateToMeetings(): Promise<void> {
    if (!url) {
      throw new Error('ONE_UI_BASE_URL environment variable is not set');
    }
      await this.page.goto(url);
      await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
      await this.page.getByTestId("nav-pills-tab-0").click();
      await this.page.waitForTimeout(10000);
      await PlaywrightActions.closePopup(this.page);
      await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-expansion-expansion-button").click();
      await this.page.waitForTimeout(5000);
      await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_de_meetings-button").click();
      await this.page.waitForTimeout(10000);
  }

  async checkPageUrl(): Promise<void> {
      await PlaywrightActions.checkUrl(this.page, url+"analytics/digital-experience/meetings");
  }

  async checkMeetingsDateFilter(): Promise<void> {
      await PlaywrightActions.checkDateFilter(this.page, "digital-experience-meetings", this.meetingsDateFilter);
  }

  async checkMeetingsFilters(): Promise<void> {
      await PlaywrightActions.checkFilters(this.page, "analytics-layout", this.meetingsFilters);
  }

  async checkContainerTitle(title: string): Promise<void> {
      await PlaywrightActions.checkHeaderByTestId(this.page, `${this.TableSelectors[title]}-card-title`, title);
  }

  async checkTable(tableName: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
      if(!needData)
      {
          let hover = true;
          await PlaywrightActions.checkTable(this.page, `${this.TableSelectors[tableName]}-z-table`, this.TableRows[tableName], hover);
      }
  }

  async checkSearchBar(tableName: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
      if(!needData)
      {
          await PlaywrightActions.checkSearchBar(this.page, this.TableSelectors[tableName]);
      }
  }
}

export default Meetings;
