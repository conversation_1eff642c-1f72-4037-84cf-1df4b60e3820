import { expect, Page } from "@playwright/test";
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config } from "dotenv";
import { SelectorsType, TableRowType } from "../../../../../resources/types/types";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class SelfService {

  private page: Page;
  private readonly selfserviceDateFilter: string[];
  private readonly selfserviceFilters: string[];
  private readonly Selectors: SelectorsType;
  private readonly TableSelectors: SelectorsType;
  private TableRows: TableRowType;
  constructor(page: Page) {
      this.page = page;

      this.selfserviceDateFilter = [
          "2 Hours",
          "4 Hours",
          "6 Hours",
          "12 Hours",
          "24 Hours",
          "48 Hours",
          "custom",
        ];
      
      this.selfserviceFilters= [
          "Departments",
          "Zscaler Locations",
          "Geolocations",
          "Notification Type",
        ];
      
      this.Selectors = {
          "Total Notifications Sent": "self-service-total-sent",
          "Notifications By Type": "self-service-notification-by-type",
          "Total Users Notified": "self-service-total-user-notified",
          "Users Found Notifications Helpful": "self-service-helpful",
          "Active Users With Self Service": "self-service-active-users",
          "Users Who Disabled Notifications": "self-service-users-who-disabled",
          "Notifications Sent Over Time": "self-service-notification-over-time",
          "Notifications": "self-service-notification-table",
      };
      
      this.TableSelectors = {
          "Notifications": "self-service-notification-table",
      };
      
      this.TableRows = {
          "Notifications": {
              "user_id_name": "User",
              "device_id_name": "Device",
              "evt_name": "Notification Type",
              "evt_feedback": "Found it Helpful?",
              "evt_stime": "Timestamp",
          }
      }
  }

  async navigateToSelfService(): Promise<void> {
    if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
      await this.page.goto(url);
      await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
      await this.page.getByTestId("nav-pills-tab-0").click();
      await this.page.waitForTimeout(10000);
      await PlaywrightActions.closePopup(this.page);
      await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-expansion-expansion-button").click();
      await this.page.waitForTimeout(5000);
      await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_de_self_svc-button").click();
      await this.page.waitForTimeout(10000);
  }

  async checkSelfServiceDateFilter(): Promise<void> {
      await PlaywrightActions.checkDateFilter(this.page, "digital-experience-self-service", this.selfserviceDateFilter);
  }

  async checkSelfServiceFilters(): Promise<void> {
      await PlaywrightActions.checkFilters(this.page, "analytics-layout", this.selfserviceFilters);
  }

  async checkPageUrl(): Promise<void> {
      await PlaywrightActions.checkUrl(this.page, url+"analytics/digital-experience/self-service");
  }

  async checkContainerTitle(title: string): Promise<void> {
      await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
  }
  
  async checkValue(containerName: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
      if(!needData)
      {
          let value = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-value`);
          await expect(value).toBeGreaterThanOrEqual(0);
      }
  }

  async checkGraph(title: string, grapType: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
      if(!needData)
      {
          await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${grapType}`);
      }
  }

  async checkTable(tableName: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
      if(!needData)
      {
          let hover = true;
          await PlaywrightActions.checkTable(this.page, `${this.TableSelectors[tableName]}-z-table`, this.TableRows[tableName], hover);
      }
  }
  
  async checkSearchBar(tableName: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
      if(!needData)
      {
          await PlaywrightActions.checkSearchBar(this.page, this.TableSelectors[tableName]);
      }
  }

}

export default SelfService;
