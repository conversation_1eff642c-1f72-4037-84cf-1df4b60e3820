import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { SelectorsType } from "../../../../../resources/types/types";

config(); 


const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Activity {
    private page: Page;
    private readonly activityDateFilter: string[];
    private readonly activityFilters: string[];
    private readonly Selectors: SelectorsType;

    constructor(page: Page) {
        this.page= page;
      
        this.activityDateFilter = [
          "2 Hours",
          "4 Hours",
          "6 Hours",
          "12 Hours",
          "24 Hours",
          "48 Hours",
          "custom",
        ];
      
        this.activityFilters= [
          "Departments",
          "Zscaler Locations",
          "User Groups",
          "Geolocations",
        ];

        this.Selectors = {
          "Active Users": "activity-active-users",
          "Active Devices": "activity-active-devices",
          "User Distribution by Experience Score": "activity-user-distribution-experience",
          "Top 5 Applications with Lowest Experience Score": "activity-top-used-apps",
          "Regions by Average Application Experience Score": "activity-region-avg-app-exp-score",
          "Wi-Fi Performance": "activity-wifi-performance",
        };
    }

    async navigateToActivity(): Promise<void> {
        if (!url) {
          throw new Error('ONE_UI_BASE_URL environment variable is not set');
        }
        await this.page.goto(url);
        await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
        await this.page.getByTestId("nav-pills-tab-0").click();
        await this.page.waitForTimeout(10000);
        await PlaywrightActions.closePopup(this.page);
        await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-expansion-expansion-button").click();
        await this.page.waitForTimeout(5000);
        await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_de_activity-button").click();
        await this.page.waitForTimeout(10000);
      }
      
    async checkActivityDateFilter(): Promise<void> {
        await PlaywrightActions.checkDateFilter(this.page, "digital-experience-activity", this.activityDateFilter);
    }

    async checkActivityFilters(): Promise<void> {
        await PlaywrightActions.checkFilters(this.page, "analytics-layout", this.activityFilters);
    }

    async checkPageUrl(): Promise<void> {
        await PlaywrightActions.checkUrl(this.page, url+"analytics/digital-experience/activity");
    }

    async checkSectionTitle(title: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
    }

    async checkContainerTitle(title: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
    }
    
    async checkValue(containerName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
        if(!needData)
        {
            let value = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-value`);
            await expect(value).toBeGreaterThanOrEqual(0);
        }
    }

    async checkGraph(title: string, grapType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${grapType}`);
        }
    }

    async checkUserDistributionCount(containerName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
        if(!needData)
        {
            let poorApps = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-z-list-value-0`);
            let okayApps = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-z-list-value-1`);
            let goodApps = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-z-list-value-2`);
            await expect(poorApps+okayApps+goodApps).toBeGreaterThanOrEqual(0);
        }
    }

}

export default Activity;