import { Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { SelectorsType, TableRowType } from "../../../../../resources/types/types";

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Application {
  private page: Page;
  private readonly applicationsDateFilter: string[];
  private readonly applicationsFilters: string[];
  private readonly Selectors: SelectorsType;
  private readonly TableSelectors: SelectorsType;
  private TableRows: TableRowType;
  constructor(page: Page) {
      this.page = page;

      this.applicationsDateFilter = [
          "2 Hours",
          "4 Hours",
          "6 Hours",
          "12 Hours",
          "24 Hours",
          "48 Hours",
          "custom",
        ];
      
      this.applicationsFilters= [
          "Application",
          "Departments",
          "Zscaler Locations",
          "User Groups",
          "Geolocations",
          "Location Groups",
          "Last Mile ISPs",
          "Operating System",
        ];
      
      this.Selectors = {
          "Application Experience Trend": "applications-app-exp-trend",    
      };
      
      this.TableSelectors = {
          "Application": "applications-app-table",    
      };
      
      this.TableRows = {
          "Application": {
              "app_id_name": "Application Name",
              "upm_app_score": "Score",
              "departments": "Departments",
              "regions": "Regions",
              "locations": "Locations",
          },
      };
  }

  async navigateToApplication(): Promise<void> {
      if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
      await this.page.goto(url);
      await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
        await this.page.getByTestId("nav-pills-tab-0").click();
      await this.page.waitForTimeout(10000);
      await PlaywrightActions.closePopup(this.page);
      await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-expansion-expansion-button").click();
      await this.page.waitForTimeout(5000);
      await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_de_applications-button").click();
      await this.page.waitForTimeout(10000);
  }

  async checkPageUrl(): Promise<void> {
      await PlaywrightActions.checkUrl(this.page, url+"analytics/digital-experience/applications");
  }

  async checkApplicationDateFilter(): Promise<void> {
      await PlaywrightActions.checkDateFilter(this.page, "digital-experience-applications", this.applicationsDateFilter);
  }

  async checkApplicationFilters(): Promise<void> {
      await PlaywrightActions.checkFilters(this.page, "analytics-layout", this.applicationsFilters);
  }

  async checkContainerTitle(title: string): Promise<void> {
      await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
  }
  
  async checkGraph(title: string, grapType: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
      if(!needData)
      {
          await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${grapType}`);
      }
  }

  async checkTable(tableName: string): Promise<void> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
      if(!needData)
      {
        let hover: boolean= true;
          await PlaywrightActions.checkTable(this.page, `${this.TableSelectors[tableName]}-z-table`, this.TableRows[tableName], hover);
      }
  }

  async checkTableData(tableName: string): Promise<boolean> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
      if(!needData)
      {
          return await PlaywrightActions.isDataInTable(this.page, `${this.TableSelectors[tableName]}-z-table`);
      }
      return false;
  }
}

export default Application;
