import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { SelectorsType, TableRowType } from "../../../../../resources/types/types";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Incidents {
    private page: Page;
    private readonly incidentsDateFilter: string[];
    private readonly typeFilterData: string[];
    private readonly Selectors: SelectorsType;
    private readonly TableSelectors: SelectorsType;
    private TableRows: TableRowType;

    constructor(page: Page) {
        this.page = page;

        this.incidentsDateFilter = [
            "2 Hours",
            "4 Hours",
            "6 Hours",
            "12 Hours",
            "24 Hours",
            "48 Hours",
            "custom",
          ];
        
        this.Selectors = {
            "Incidents Across Key Areas": "incidents-inc-across-key-areas",   
            "Incidents Over Time": "incidents-inc-over-time",
            "Impacted Users Over Time": "incidents-imp-users-over-time",
            "Incidents by Epicenters": "incidents-inc-by-epicenter",
            "Incidents": "incidents-inc-table",
            "Total Incidents": "incidents-inc-across-key-areas-z-list-0",
            "Impacted Users": "incidents-inc-across-key-areas-z-list-1",
            "Total Incidents Value": "incidents-inc-across-key-areas-z-list-value-0",
            "Impacted Users Value": "incidents-inc-across-key-areas-z-list-value-1",
        };
        
        this.TableSelectors = {
            "Incidents": "incidents-inc-table",
        };
        
        this.TableRows = {
            "Incidents": {
                "inc_type": "Incident Type",
                "inc_epicenter_city": "Epicenter",
                "inc_impacted_user_count": "Users Impacted",
                "incident_duration_mins": "Duration",
                "inc_stime": "Start Time",
                "inc_etime": "End Time",
            }
        }

        this.typeFilterData = [
            "Device",
            "Wi-Fi",
            "DNS",
            "Last Mile ISP",
            "Intermediate ISP",
            "ZIA Public Service Edge",
            "ZPA Public Service Edge",
            "Application",
        ];

    }

    async navigateToIncidents(): Promise<void> {
        if (!url) {
            throw new Error('ONE_UI_BASE_URL environment variable is not set');
        }
        await this.page.goto(url);
        await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
        await this.page.getByTestId("nav-pills-tab-0").click();
        await this.page.waitForTimeout(10000);
        await PlaywrightActions.closePopup(this.page);
        await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_de_de-expansion-expansion-button").click();
        await this.page.waitForTimeout(5000);
        await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_de_incidents-button").click();
        await this.page.waitForTimeout(10000);
    }

    async checkPageUrl(): Promise<void> {
        await PlaywrightActions.checkUrl(this.page, url+"analytics/digital-experience/incidents");
    }

    async checkIncidentsDateFilter(): Promise<void> {
        await PlaywrightActions.checkDateFilter(this.page, "digital-experience-incidents", this.incidentsDateFilter);
    }
    
    async checkTypeFilter(): Promise<void> {
        await PlaywrightActions.checkfilterByType(this.page, "analytics-layout-modal-pill-grp-0-zselect-container", this.typeFilterData);;
    }

    async checkContainerTitle(title: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
    }
    
    async checkGraph(title: string, graphType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${graphType}`);
        }
    }

    async checkTable(tableName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
        if(!needData)
        {
            let hover = true;
            await PlaywrightActions.checkTable(this.page, `${this.TableSelectors[tableName]}-z-table`, this.TableRows[tableName], hover);
        }
    }

    async checkSearchBar(tableName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[tableName]);
        if(!needData)
        {
            await PlaywrightActions.checkSearchBar(this.page, this.TableSelectors[tableName]);
        }
    }

    async checklistHeader(title: string, containerName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
        if(!needData)
        {
            await PlaywrightActions.checkHeader(this.page, `(//div[@data-testid="${this.Selectors[title]}"]/div[@class='flex items-center']/div)`, title);
        }
    }

    async checklistValue(title: string, containerName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
        if(!needData)
        {
            let data = await PlaywrightActions.getValue(this.page, this.Selectors[title]);
            await expect(data).toBeGreaterThanOrEqual(0);
        }
    }

}

export default Incidents;
