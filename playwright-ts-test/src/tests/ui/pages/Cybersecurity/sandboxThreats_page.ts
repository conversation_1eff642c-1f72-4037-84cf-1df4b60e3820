import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

const Selectors: Record<string, string> = {
  "Policy Actions & Verdicts for Files Known by Cloud Effect": "sandbox-threats-policy-summary-for-known-files",
  "Policy Actions & Verdicts for Unknown Files": "sandbox-threats-policy-summary-for-unknown-files",
  "Top Users Generating Sandbox Threats": "sandbox-threats-top-users-gen-sand-thr",
};

const TableSelectors: Record<string, string> = {
  "Top Sandbox Incidents": "sandbox-threats-top-sandbox-incidents",
};

const TableRows: Record<string, Record<string, string>> = {
  "Top Sandbox Incidents": {
    "threatKey": "Incident Name",
    "threatCategory": "Category",
    "impactedSystems": "Impacted Systems",
    "status": "Status",
    "lastKnownAttempt": "Last Known Date",
    "firstKnownAttempt": "First Known Date"
  }
};

class SandboxThreats {
  fields: {
    sandboxThreatsTab: string;
  };

  constructor() {
    this.fields = {
      sandboxThreatsTab: 'left-nav-collapsible-nav-item-sub-menu-lm_analytics_cs_sbox_threats-button',
    };
  }

  async clickSandoxThreatsTab(page: Page): Promise<void> {
    await PlaywrightActions.closePopup(page);
    await PlaywrightActions.waitAndClickByTestId(page, "left-nav-collapsible-nav-item-lm_analytics_cs_cs-expansion-expansion-button");
    await PlaywrightActions.waitAndClickByTestId(page, this.fields.sandboxThreatsTab);
    await page.waitForTimeout(6000);
    const sandboxTab = await page.getByTestId('analytics-layout-breadcrumb').getByText('Sandbox Threats');
    expect(sandboxTab).toHaveText('Sandbox Threats');
  }

  async verifyGraph(page: Page, title: string, graphType: string): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-${graphType}`);
      }
  }

  async verifyTopSandboxTable(page: Page, title: string, hoverable: boolean = true): Promise<void>{
      await page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(page, TableSelectors[title]);
      if(!needData)
      {
          await PlaywrightActions.checkTable(page, `${TableSelectors[title]}-z-table`, TableRows[title], hoverable);
      }
  }
}

export default new SandboxThreats();