import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config as dotenvConfig } from 'dotenv';

dotenvConfig(); // Load environment variables from .env file

const baseUrl = process.env.ONE_UI_BASE_URL; // Provide a default URL

const Selectors: Record<string, string> = {
  "Malicious Transactions": "cybersecurity-your-cs-trans-malicious-transactions",
  "All Threats": "trans-activity-malicious-trans-your-mal-trans",
  "Top Users Generating Threats": "trans-activity-malicious-trans-top-users-gen-threat",
  "Top Departments Generating Threats": "trans-activity-malicious-trans-top-dept-gen-threats",
  "Your Policy Blocks": "trans-activity-policy-blocks-your-policy-blocks",
  "Top Application Class": "trans-activity-policy-blocks-top-app-class",
  "Top URL Categories Blocked": "trans-activity-policy-blocks-top-url-categories-blocked",
  "Top Cloud Applications Blocked": "trans-activity-policy-blocks-top-cloud-apps-blocked",
  "Top Blocked File Types": "trans-activity-policy-blocks-top-blocked-file-types"
};

class TransactionalActivity {
  fields: {
    transactionActivityTab: string;
    policyBlockTab: string;
  };

  constructor() {
    this.fields = {
      transactionActivityTab: 'left-nav-collapsible-nav-item-sub-menu-lm_analytics_cs_trans_activity-button',
      policyBlockTab: 'trans-activity-tabs-tabs-tab-label-1',
    };
  }

   async checkPageUrl(page: Page): Promise<void> {
      await page.waitForTimeout(2000);
      await PlaywrightActions.checkUrl(page, baseUrl + "analytics/cybersecurity/transactional-activity");
  }

  async clickOnTransactionActivity(page: Page, s: string, title: string): Promise<void> {
      await page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
        if (!needData) {
          await PlaywrightActions.checkGraph(page, `${Selectors[title]}-multiline-chart`);
          await PlaywrightActions.footerNavigaion(page, `${Selectors[title]}-footer-navLink-z-button-link`, `${baseUrl}analytics/cybersecurity/transactional-activity`)
        }else{
          await PlaywrightActions.closePopup(page);
          await PlaywrightActions.waitAndClickByTestId(page, "left-nav-collapsible-nav-item-lm_analytics_cs_cs-expansion-expansion-button");
          await PlaywrightActions.waitAndClickByTestId(page, this.fields.transactionActivityTab);
        }
  }

  async selectPolicyBlockTab(page: Page, s: string): Promise<void> {
    await PlaywrightActions.waitAndClickByTestId(page, this.fields.policyBlockTab);
    const policyBlocks = await PlaywrightActions.waitAndGetByTestId(page, this.fields.policyBlockTab);
    expect(policyBlocks).toHaveText(s);
  }

  async verifyTransactionalActivityGraph(page: Page, title: string, graphType: string): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-${graphType}`);
      }
  }
}

export default new TransactionalActivity();