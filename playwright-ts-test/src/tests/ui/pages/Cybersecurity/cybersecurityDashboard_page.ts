import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

const Selectors: Record<string, string> = {
  "Total Threats Blocked Graph": "cybersecurity-your-cs-trans-malicious-transactions",
  "Total Policy Blocks Graph": "cybersecurity-your-cs-trans-policy-blocks",
  "Advanced Threat Categories": "cybersecurity-advanced-threats-categories",
  "Top Threat Locations": "cybersecurity-top-threat-locations",
  "Your SSL/TLS Inspection Review": "cybersecurity-your-ssl-inspection-review",
  "Sandbox Threats": "cybersecurity-sandbox-threats",
  "Total Threats Blocked": "malicious-transactions",
  "Total Policy Blocks": "policy-blocks"
};

class CybersecurityDashboard {
  fields: {
    cybersecurityDataTestId: string;
    dayDropdownDataTestId: string;
    dayLocator: (days: string) => string;
    cardContentDataTestId: string;
    cardTitleDataTestId: string;
    policyToggleDataTestId: string;
    cybersecurityIcon:string;
  };

  constructor() {
    this.fields = {
      cybersecurityIcon: 'left-nav-collapsible-nav-item-lm_analytics_cs_cs-button',
      cybersecurityDataTestId: 'Cybersecurity-page-header',
      dayDropdownDataTestId: 'date-time-range-dropdown-cybersecurity-zselect-container-collapsed-view',
      dayLocator: (days) => `//span[contains(text(),'${days}')]`,
      cardContentDataTestId: 'card-content',
      cardTitleDataTestId: 'card-title',
      policyToggleDataTestId: 'cybersecurity-your-cs-trans-segment-control-1'
    };
  }

  async navigatesToCybersecurity(page: Page): Promise<void> {
    await page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
    await page.getByTestId("nav-pills-tab-0").click();
    await page.waitForTimeout(5000);
    await PlaywrightActions.closePopup(page);
    await PlaywrightActions.waitAndClickByTestId(page, this.fields.cybersecurityIcon);
    const cybersecurity = await PlaywrightActions.waitAndGetByTestId(page, this.fields.cybersecurityDataTestId);
    await expect(cybersecurity).toHaveText('Cybersecurity');
  }

  async verifyWidgetsInCybersecurity(page: Page): Promise<void> {
    // const cybersecurityTransactionWidget = await PlaywrightActions.getByRole(page, 'heading', 'Your Cybersecurity');
    const cybersecurityTransactionWidget = await page.getByTestId("cybersecurity-your-cs-trans-card-title");
    await expect(cybersecurityTransactionWidget).toContainText('Your Cybersecurity Transactions');

    // const cybersecurityATC = await PlaywrightActions.getByRole(page, 'heading', 'Advanced Threat Categories');
    const cybersecurityATC = await page.getByTestId("cybersecurity-advanced-threats-categories-card-title");
    await expect(cybersecurityATC).toHaveText('Advanced Threat Categories');

    const cybersecurityTTL = await page.getByTestId("cybersecurity-top-threat-locations-card-title");
    await expect(cybersecurityTTL).toHaveText('Top Threat LocationsA breakdown of threats stopped by predefined categories.');

    const cybersecuritySandboxThreats = await page.getByTestId("cybersecurity-sandbox-threats-card-title");
    await expect(cybersecuritySandboxThreats).toHaveText('Sandbox Threats');

    const cybersecuritySSL = await page.getByTestId("cybersecurity-your-ssl-inspection-review-card-title");
    await expect(cybersecuritySSL).toHaveText('Your SSL/TLS Inspection Review');
  }

  async verifyDateDropdown(page: Page, days: string): Promise<void> {
    await PlaywrightActions.getByRoleAndClick(page, 'button', 'Days ');
    await PlaywrightActions.getByTextAndClick(page, days);
    const dayDropdown = await PlaywrightActions.waitAndGetByTestId(page, this.fields.dayDropdownDataTestId);
    await expect(dayDropdown).toHaveText(days);
    const title = await PlaywrightActions.waitAndVisibleLocator(page, this.fields.dayLocator(days));
    await expect(title).toContainText(days);
  }

  async verifyCybersecurityTitle(page: Page, s: string): Promise<void> {
    const cybersecurityTransactionWidget = await PlaywrightActions.waitAndGetByTestId(page, 'cybersecurity-your-cs-trans-card-title');
    await expect(cybersecurityTransactionWidget).toContainText(s);
  }

  async toggleToPolicyBlocks(page: Page, s: string): Promise<void> {
    await PlaywrightActions.waitAndClickByTestId(page, this.fields.policyToggleDataTestId);
    const policyLabel = await PlaywrightActions.waitAndGetByTestId(page, this.fields.policyToggleDataTestId);
    expect(policyLabel).toHaveText(s);
  }

  async verifyTotalCount(page: Page, title: string): Promise<void> {
    await page.waitForTimeout(10000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        const policyBlocks = await page.getByTestId(this.fields.cardContentDataTestId).getByTestId(this.fields.cardTitleDataTestId);
        await expect(policyBlocks).toHaveText(title);
      
        const policyCount = await PlaywrightActions.waitAndGetByTestId(page, `cybersecurity-your-cs-trans-${Selectors[title]}-total-count`);
        const valueText = (await policyCount.textContent()) ?? "";
        console.log('Number of Transactions:', valueText);

        CommonFunctions.multiplierBasedOnSuffix(valueText);
    }
  }

  async verifyGraph(page: Page, title: string, graphType: string): Promise<void> {
        await page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
        if (!needData) {
          await PlaywrightActions.checkGraph(page, `${Selectors[title]}-${graphType}`);
        }
  }
}

export default new CybersecurityDashboard();