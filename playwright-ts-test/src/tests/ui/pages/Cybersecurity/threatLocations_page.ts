import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config as dotenvConfig } from 'dotenv';

dotenvConfig(); // Load environment variables from .env file

const baseUrl = process.env.ONE_UI_BASE_URL; // Provide a default URL

const Selectors: Record<string, string> = {
  "Top Threat Locations": "cybersecurity-top-threat-locations",
  "Total Threats Trend": "threat-location-top-threats-trend",
  "Top Threat Categories": "threat-location-top-threat-categories",
  "Top Users Generating Threats": "threat-location-top-users-generating-threats",
  "Top Applications Generating Threats": "threat-location-top-applications-generating-threats"
};

class ThreatLocations {
  fields: {
    topThreatLocationTab: string;
  };

  constructor() {
    this.fields = {
      topThreatLocationTab: 'left-nav-collapsible-nav-item-sub-menu-lm_analytics_cs_threats_locs-button',
    };
  }

  async checkPageUrl(page: Page): Promise<void> {
      await page.waitForTimeout(2000);
      await PlaywrightActions.checkUrl(page, baseUrl + "analytics/cybersecurity/threat-location");
  }

  async clickThreatLocationsButton(page: Page, s: string, title:string): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-horizontal-bar-chart`);
        await PlaywrightActions.footerNavigaion(page, `${Selectors[title]}-footer-navLink-z-button-link`, `${baseUrl}analytics/cybersecurity/threat-location`)
      }else{
        await PlaywrightActions.closePopup(page);
        await PlaywrightActions.waitAndClickByTestId(page, "left-nav-collapsible-nav-item-lm_analytics_cs_cs-expansion-expansion-button");
        await PlaywrightActions.waitAndClickByTestId(page, this.fields.topThreatLocationTab);
      }
  }

  async verifyThreatLocationsGraph(page: Page, title: string, graphType: string): Promise<void> {
      await page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
        if (!needData) {
          await PlaywrightActions.checkGraph(page, `${Selectors[title]}-${graphType}`);
        }
  }

}

export default new ThreatLocations();