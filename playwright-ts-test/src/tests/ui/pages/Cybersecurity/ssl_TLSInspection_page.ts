import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config as dotenvConfig } from 'dotenv';

dotenvConfig(); // Load environment variables from .env file

const baseUrl = process.env.ONE_UI_BASE_URL; // Provide a default URL

const Selectors: Record<string, string> = {
  "Your SSL/TLS Inspection Review": "cybersecurity-your-ssl-inspection-review",
  "Threats Blocked by SSL/TLS Inspection": "ssl-inspection-threat-blocked-ssl",
  "Top High Volume Applications": "ssl-inspection-top-high-vol-app",
  "Top Locations": "ssl-inspection-top-locations",
  "Top URL Categories": "ssl-inspection-top-url-cats"
};

class SSL_TLSInspection {
  fields: {
    sslTLSInspectionTab: string;
  };

  constructor() { 
    this.fields = {
     sslTLSInspectionTab: 'left-nav-collapsible-nav-item-sub-menu-lm_analytics_cs_tls_inspect-button'
    };
  }

  async checkPageUrl(page: Page): Promise<void> {
      await page.waitForTimeout(2000);
      await PlaywrightActions.checkUrl(page, baseUrl + "analytics/cybersecurity/ssl-inspection");
  }

  async clickSSL_TLSButton(page: Page, s: string, title: string): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-bar-line-chart`);
        await PlaywrightActions.footerNavigaion(page, `${Selectors[title]}-footer-navLink-z-button-link`, `${baseUrl}analytics/cybersecurity/ssl-inspection`)
      }else{
        await PlaywrightActions.closePopup(page);
        await PlaywrightActions.waitAndClickByTestId(page, "left-nav-collapsible-nav-item-lm_analytics_cs_cs-expansion-expansion-button");
        await PlaywrightActions.waitAndClickByTestId(page, this.fields.sslTLSInspectionTab);
      }
  }

  async verifySSLTLSInspectionraph(page: Page, title: string, graphType: string): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-${graphType}`);
      }
  }
}

export default new SSL_TLSInspection();