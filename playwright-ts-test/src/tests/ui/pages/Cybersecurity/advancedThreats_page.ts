import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config as dotenvConfig } from 'dotenv';

dotenvConfig(); // Load environment variables from .env file

const baseUrl = process.env.ONE_UI_BASE_URL; // Provide a default URL

const Selectors: Record<string, string> = {
  "Advanced Threat Categories": "cybersecurity-advanced-threats-categories",
  "Incoming Real Time Threats": "advanced-threats-incoming-real-time-threats"
};

const TableSelectors: Record<string, string> = {
  "Advanced Threat Incidents": "advanced-threats-adv-th-inc",
};

const TableRows: Record<string, Record<string, string>> = {
  "Advanced Threat Incidents": {
    "threatKey": "Incident Name",
    "threatCategory": "Category",
    "impactedSystems": "Impacted Systems",
    "status": "Status",
    "lastKnownAttempt": "Last Known Date",
    "firstKnownAttempt": "First Known Date"
  }
};

class AdvancedThreats {
  fields: {
    advancedThreatsTab: string;
  };

  constructor() {
    this.fields = {
      advancedThreatsTab: "left-nav-collapsible-nav-item-sub-menu-lm_analytics_cs_adv_threats-button",
    };
  }

  async checkPageUrl(page: Page): Promise<void> {
      await page.waitForTimeout(2000);
      await PlaywrightActions.checkUrl(page, baseUrl + "analytics/cybersecurity/advanced-threat");
  }

  async clickAdvancedThreatButton(page: Page, s: string, title: string): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-horizontal-bar-chart`);
        await PlaywrightActions.footerNavigaion(page, `${Selectors[title]}-footer-navLink-z-button-link`, `${baseUrl}analytics/cybersecurity/advanced-threat`)
      }else{
        await PlaywrightActions.closePopup(page);
        await PlaywrightActions.waitAndClickByTestId(page, "left-nav-collapsible-nav-item-lm_analytics_cs_cs-expansion-expansion-button");
        await PlaywrightActions.waitAndClickByTestId(page, this.fields.advancedThreatsTab);
      }
  }

  async verifyAdvancedThreatTable(page: Page, title: string, hoverable: boolean = true): Promise<void>{
    await page.waitForTimeout(5000);
    await PlaywrightActions.checkTable(page, `${TableSelectors[title]}-z-table`, TableRows[title], hoverable);
  }

  async verifyIncomeThreatGraph(page: Page, title: string, graphType: string): Promise<void> {
      await page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(page, Selectors[title]);
      if (!needData) {
        await PlaywrightActions.checkGraph(page, `${Selectors[title]}-${graphType}`);
      }
  }
}

export default new AdvancedThreats();
