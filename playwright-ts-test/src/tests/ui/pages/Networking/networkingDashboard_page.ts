import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { SelectorsType } from "../../../../../resources/types/types";

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Networking {
    private page: Page;
    private readonly networkingDateFilter: string[];
    private readonly Selectors: SelectorsType;
    private readonly routes: SelectorsType;
    private readonly tabSelectors: SelectorsType;

    constructor(page: Page) {
         this.page = page;

         this.routes = {
          "Connector Activity": "analytics/networking/connector-activity",
       }

         this.networkingDateFilter = [
          "1 Day",
          "7 Days",
          "14 Days",
        ];

        this.Selectors = {
          "Traffic in my Organization": "networking-traf-in-org",
          "Number of Transactions-Internet & SaaS":"networking-traf-in-org-traffic-sum-internet-no-of-tra",
          "Traffic Volume-Internet & SaaS":"networking-traf-in-org-traffic-sum-internet-traffic-vol-int",
          "Traffic in my Organization-Internet & SaaS":"networking-traf-in-org-traffic-sum-internet",
          "Number of Transactions-Private":"networking-traf-in-org-traffic-sum-pri-number-of-trans",
          "Traffic Volume-Private":"networking-traf-in-org-traffic-sum-pri-traffic-vol-pri",
          "Traffic in my Organization-Private":"networking-traf-in-org-traffic-sum-pri",
          "Internet Traffic Distribution":"networking-traffic-distribution",
          "Top Locations sending Internet Traffic to Zscaler":"networking-primary-traffic-sources",
          "Top Zscaler Data Centers Used":"networking-connectivity-center-data-centers",
          "Locations with Zscaler Connectors":"networking-connectivity-center-app-connector",
          "Branch & Cloud Connectors":"networking-connectivity-center-branch-cloud-connector",
        };

        this.tabSelectors = {
          "Internet & SaaS": "networking-traf-in-org-segment-control-0",
          "Private": "networking-traf-in-org-segment-control-1",
          "Data Centers": "networking-connectivity-center-tabs-tab-label-0",
          "App Connectors": "networking-connectivity-center-tabs-tab-label-1",
          "Branch & Cloud Connectors": "networking-connectivity-center-tabs-tab-label-2",
      };
    }

    async navigateToNetworking(): Promise<void> {
        if (!url) {
          throw new Error('ONE_UI_BASE_URL environment variable is not set');
        }
        await this.page.goto(url);
        await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
        await this.page.getByTestId("nav-pills-tab-0").click();
        await this.page.waitForTimeout(10000);
        await PlaywrightActions.closePopup(this.page);
        await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_networking-button").click();
        await this.page.waitForTimeout(10000);
    }

    async checkNetworkingDateFilter(): Promise<void> {
        await PlaywrightActions.checkDateFilter(this.page, "networking", this.networkingDateFilter);
    }

    async checkPageUrl(): Promise<void> {
        await PlaywrightActions.checkUrl(this.page, url+"analytics/networking");
    }

    async checkContainerTitle(title: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[title]}-card-title`, title);
    }

    async checkLabelWithValue(title: string, tabName: string, valueType: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[`Traffic in my Organization-${tabName}`]);
        if(!needData)
        {
            await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[`${title}-${tabName}`]}-label`, title);
            await PlaywrightActions.getValue(this.page, `${this.Selectors[`${title}-${tabName}`]}-value`, valueType);
        }
    }

    async checkValue(containerName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[containerName]);
        if(!needData)
        {
            let value = await PlaywrightActions.getValue(this.page, `${this.Selectors[containerName]}-value`);
            await expect(value).toBeGreaterThanOrEqual(0);
        }
    }

      async switchToTab(tabName: string): Promise<void> {
          await PlaywrightActions.checkSwitchableTabs(this.page, this.tabSelectors[tabName]);
      }

    async checkLabelGraph(title: string, tabName: string, grapType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[`Traffic in my Organization-${tabName}`]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.Selectors[`${title}-${tabName}`]}-${grapType}`);
        }
    }
    async checkGraph(title: string, grapType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.Selectors[title]}-${grapType}`);
        }
    }

    async checkFooterNavigaion(navigationPage: string, title: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[title]);
      if(!needData)
      {
          await PlaywrightActions.footerNavigaion(this.page, `${this.Selectors[title]}-footer-navLink-z-button-link`, url+this.routes[navigationPage]);
      }
  }

}

export default Networking;