import { Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { FiltersType, SelectorsType, TableRowType } from "../../../../../resources/types/types";

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class ConnectorActivity {
    private page: Page;
    private readonly connectorActivityFilters: FiltersType;
    private readonly Selectors: SelectorsType;
    private readonly tabSelectors: SelectorsType;
    private readonly TableSelectors: SelectorsType;
    private TableRows: TableRowType;

    constructor(page: Page) {
        this.page = page;
    
        this.connectorActivityFilters = {
            "branch-connector": [
                "Geolocation",
                "Status",
                "Connector Type",
                "Connector Location",
            ],
            "cloud-connector": [
                "Geolocation",
                "Status",
                "Connector Location",
            ],
        };

        this.Selectors = {
            "Traffic Volume Across Service-Branch Connectors": "connector-activity-branch-connector-traffic-volume",
            "Session Across Service-Branch Connectors":"connector-activity-branch-connector-session",
            "Connectors-Branch Connectors":"connector-activity-branch-connector-geo-table",
            "Traffic Volume Across Service-Cloud Connectors": "connector-activity-cloud-connector-traffic-volume",
            "Session Across Service-Cloud Connectors":"connector-activity-cloud-connector-session",
            "Connectors-Cloud Connectors":"connector-activity-cloud-connector-geo-table",
        };

        this.tabSelectors = {
            "Branch Connectors": "connector-activity-tabs-tab-label-0",
            "Cloud Connectors": "connector-activity-tabs-tab-label-1",
        };

        this.TableSelectors = {
            "Connectors-Branch Connectors":"connector-activity-branch-connector-geo-table",
            "Connectors-Cloud Connectors":"connector-activity-cloud-connector-geo-table",    
        };
    
        this.TableRows = {
            "Connectors-Branch Connectors": {
                "ecName": "Name",
                "group": "Group",
                "location": "Location",
                "geoLocation": "Geolocation",
                "autoScale": "Auto Scaling",
                "status": "Status",
                "haStatus": "HA Status",
                "model": "Model",
            },
            "Connectors-Cloud Connectors": {
                "ecName": "Name",
                "group": "Group",
                "location": "Location",
                "geoLocation": "Geolocation",
                "autoScale": "Auto Scaling",
                "status": "Status",
                "vmSize": "VM Size",
            },
        };
    }

    async navigateToConnectorActivity(): Promise<void> {
        if (!url) {
          throw new Error('ONE_UI_BASE_URL environment variable is not set');
        }
        await this.page.goto(url);
        await this.page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
        await this.page.getByTestId("nav-pills-tab-0").click();
        await this.page.waitForTimeout(10000);
        await PlaywrightActions.closePopup(this.page);
        await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_networking-expansion-expansion-button").click();
        await this.page.waitForTimeout(2000);
        await this.page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_networking_connector_activity-button").click();
        await this.page.waitForTimeout(10000);
    }

    async checkPageUrl(): Promise<void> {
        await PlaywrightActions.checkUrl(this.page, url+"analytics/networking/connector-activity");
    }

    async checkContainerTitle(title: string, tabName: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.Selectors[`${title}-${tabName}`]}-card-title`, title);
    }

    async checkConnectorActivityFilters(tabName: string): Promise<void> {
        await PlaywrightActions.checkConnectorFilters(this.page, `connector-activity-${tabName}`, this.connectorActivityFilters[tabName]);
    }

    async switchToTab(tabName: string): Promise<void> {
        await PlaywrightActions.checkSwitchableTabs(this.page, this.tabSelectors[tabName]);
    }

    async checkGraph(title: string, tabName: string, grapType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.Selectors[`${title}-${tabName}`]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.Selectors[`${title}-${tabName}`]}-${grapType}`);
        }
    }

    async checkTableWithSearchbar(tableName: string, tabName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.TableSelectors[`${tableName}-${tabName}`]);
        if(!needData)
        {
            let hover: boolean= true;
            await PlaywrightActions.checkTable(this.page, `${this.TableSelectors[`${tableName}-${tabName}`]}-z-table`, this.TableRows[`${tableName}-${tabName}`], hover);
            await PlaywrightActions.checkSearchBar(this.page, this.TableSelectors[`${tableName}-${tabName}`]);
        }
    }

    async checkTimestamp(): Promise<void> {
        await PlaywrightActions.checkTimeStamp(this.page, "networking-connector-activity-last-updated-time");
    }

}

export default ConnectorActivity;