import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

class Subscription {
    private menuIds: Record<string, string>;
    fields: {
        searchTab: string;
        tableData: string;
      };

    constructor() {
        this.menuIds = { 
            Administration: "nav-pills-tab-1", 
            Policies: "nav-pills-tab-2", 
            Infrastructure: "nav-pills-tab-3", 
            Logs: "nav-pills-tab-4",
        };

        this.fields = {
            searchTab: "subscription-list-search-bar-input",
            tableData: "unified-subscription-tooltip",
        };
    }

    async locations(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Subscriptions", "mega-menu-columns-group-0-link-3", "(//div[text()='Subscriptions'])");
    }

    async searchSubscription(page: Page, s: string): Promise<void>{
        await page.waitForTimeout(4000);
        await PlaywrightActions.checkSearchBar(page, 'subscription-list');
        await page.getByTestId(this.fields.searchTab).fill(s);
        await page.waitForTimeout(4000);
        await page.locator(`//div[@data-testid='${this.fields.tableData}']/..//span[contains(text(),'${s}')]`).isVisible();
    }

    async verifySubscriptionColumnData(page: Page): Promise<void>{
        await page.waitForTimeout(5000);
        const name = await page.getByTestId('name-header-cell').getByText('Name');
        expect(name).toHaveText('Name');

        const sku = await page.getByText('SKU');
        expect(sku).toHaveText('SKU');

        const status = await page.getByTestId('status-header-cell').getByText('Status');
        expect(status).toHaveText('Status');

        const Usage = await page.getByText('Usage');
        expect(Usage).toHaveText('Usage');

        const license = await page.getByText('Licenses');
        expect(license).toHaveText('Licenses');

        const startDate = await page.getByText('Start Date');
        expect(startDate).toHaveText('Start Date');

        const endDate = await page.getByText('End Date');
        expect(endDate).toHaveText('End Date');
    }
}

export default new Subscription();