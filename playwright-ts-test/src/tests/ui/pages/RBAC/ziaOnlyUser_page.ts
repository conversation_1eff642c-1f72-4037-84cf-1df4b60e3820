import { expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

  

const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-1", 
    Policies: "nav-pills-tab-2", 
    Infrastructure: "nav-pills-tab-3", 
    Logs : "nav-pills-tab-4"
};

const SubMenuIds: Record<string, string> = { 
    "Account Management": "mm-tabs-tab-0",
    "Admin Management": "mm-tabs-tab-1", 
    "Identity": "mm-tabs-tab-2", 
    "API Configuration": "mm-tabs-tab-3", 
    "Alerts" : "mm-tabs-tab-4",
    "Backup & Restore":"mm-tabs-tab-5",

    "Access Control": "mm-tabs-tab-0",
    "Cybersecurity": "mm-tabs-tab-1", 
    "Data Protection": "mm-tabs-tab-2", 
    "Common Configuration": "mm-tabs-tab-3", 

    "Internet & SaaS":"mm-tabs-tab-0",
    "Locations": "mm-tabs-tab-1",

    "Insights": "mm-tabs-tab-0",
    "Log Streaming": "mm-tabs-tab-1"
};

const VerticalMenuIds: Record<string, string> = { 
    "Administrator Management" :"mega-menu-tabs-vertical-tab-0",
    "Role Based Access Control": "mega-menu-tabs-vertical-tab-1",
    "Audit Logs": "mega-menu-tabs-vertical-tab-2",

    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "Internet & SaaS": "mega-menu-tabs-vertical-tab-1",

    "Legacy API": "mega-menu-tabs-vertical-tab-0",

    // "Internet & Saa":"mega-menu-tabs-vertical-tab-0",
    "Firewall":"mega-menu-tabs-vertical-tab-1",

    "Inline Security":"mega-menu-tabs-vertical-tab-0",
    "SaaS Security API":"mega-menu-tabs-vertical-tab-1",
    "Partner Integrations":"mega-menu-tabs-vertical-tab-2",

    "Policy":"mega-menu-tabs-vertical-tab-0",
    "Common Resources":"mega-menu-tabs-vertical-tab-1",

    "Out-of-Band CASB":"mega-menu-tabs-vertical-tab-0",
    "SSL/TLS Inspection":"mega-menu-tabs-vertical-tab-1",
    "Resources":"mega-menu-tabs-vertical-tab-2",
    "Advanced":"mega-menu-tabs-vertical-tab-3",

    "Traffic Forwarding":"mega-menu-tabs-vertical-tab-0",
    "Network Policies":"mega-menu-tabs-vertical-tab-1",
};

class ZiaOnlyUser{

   async captureScreenshot(page: any, menu:string, tab: string): Promise<void> {
       await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
       await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);

       await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `ziaActualScreenshots/Actual_${tab}.png`, `ziaExpectedScreenshots/Expected_${tab}.png`, `ziaActualScreenshots/diff${tab}.png`);
   }

   async captureScreenshotWithVerticalMenu(page: any, menu: string, tab: string, verticalMenu: string){
        await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
        await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
        await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);

        await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `ziaActualScreenshots/Actual_${verticalMenu}.png`, `ziaExpectedScreenshots/Expected_${verticalMenu}.png`, `ziaActualScreenshots/diff${verticalMenu}.png`);
   }

    async verifyScreenshot(): Promise<void> {
      await PlaywrightActions.verifyScreenshot();
    }

    async verifyConnectorActivityTab(page: any): Promise<void> {
        // await page.getByTestId('left-nav-collapsible-nav-item-0-sub-menu-0-lm_analytics_networking_connector_activity-button').isDisabled();
        await page.waitForTimeout(2000);

        await page.locator('(//i[@aria-label="Disabled icon"])[1]').click();
        await page.waitForTimeout(2000);
        const hoverText = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
        expect(hoverText).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
    }

}
export default new ZiaOnlyUser();