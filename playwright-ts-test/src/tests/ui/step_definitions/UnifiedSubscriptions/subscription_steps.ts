import { expect } from '@playwright/test';
import { createBdd } from "playwright-bdd";
import Subscription from '../../pages/UnifiedSubscriptions/subscription_page';
const { Given, When, Then } = createBdd();

When('User clicks on the subscriptions link', async({page}) => {
   await Subscription.locations(page);
})
When('User searches the {string} in subscription', async({page}, s: string) => {
   await Subscription.searchSubscription(page, s);
})

Then('Verify the details in the subscription', async({page}) => {
  
})

Then('Verify the columns in the subscription table', async({page}) => {
   await Subscription.verifySubscriptionColumnData(page);
})
