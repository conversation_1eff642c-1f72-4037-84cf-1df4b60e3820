
import { expect } from '@playwright/test';
import { createBdd } from "playwright-bdd";
import Locations from '../../pages/UnifiedLocations/locations_page';
const { Given, When, Then } = createBdd();

When('User clicks on the locations link', async({page}) => {
   await Locations.locations(page);
})

When('User search {string} in the search bar', async({page}, s: string) => {
   await page.waitForTimeout(6000);
   await Locations.searchLocations(page, s);
})

Then('Verify the details in the locations', async({page}) => {
   await Locations.verifyLocationsData(page);
})

When('Clicks on the {string} tab', async({page}, tab: string) => {
   await Locations.toggleTab(page, tab);
})

Then('Verify the No result found message', async({page}) => {
   await Locations.verifyNoResultFound(page);
})

When('Clicks on the name data {string}', async({page}, name: string) => {
   await Locations.clickName(page, name);
})

Then('Verify the {string} with data {string} of the locations', async({page}, verticalTab: string, data: string) => {
   await Locations.verifyTabs(page, verticalTab, data);
})

When('Clicks on the connection type data', async({page}) => {
   await Locations.clickConnectionType(page);
})

When('Clicks on the Sublocations data', async({page}) => {
   await Locations.clickSublocations(page);
})

When('User clicks on the {string} button', async({page}, add: string) => {
   await page.waitForTimeout(6000);
   await Locations.clickAddLocation(page, add);
})

When('User enter the mandatory values with name {string}', async({page}, name: string) => {
   await Locations.fillMandatoryValues(page, name);
})

When('User select the {string} checkbox', async({page}, locationGroup: string) => {
   await Locations.checkLocationGroups(page, locationGroup);
})

Then('User fill in the traffic type as {string} and other details', async({page}, trafficType: string) => {
   await Locations.selectTrafficType(page, trafficType);
})

Then('Clicks on the Add button', async({page}) => {
   await Locations.clickAddButton(page); 
})

Then('Verify the user is redirected to the branch provisioning', async({page}) => {
   await Locations.verifyRedirectionToBC(page);
})

Then('Verify the user is redirected to the provisioning', async({page}) => {
   await Locations.verifyRedirectionToCC(page);
})

Then('Verify the IPSec created {string}', async({page}, name: string) => {
   await Locations.verifyLocation(page, name);
})

Then('Delete the location', async({page}) => {
   await Locations.deleteLocation(page);
})

Then('Verify the rows per page and select the {string}', async({page}, row: string) => {
   await Locations.verifyPagination(page, row);
})

Then('Verify the sync locations button is clickable', async({page}) => {
   await Locations.clickSyncLocation(page);
})

Then('Verify the duplicate name error message', async({page}) => {
   await page.waitForTimeout(8000);
   await Locations.verifyDuplicateMessage(page);
})

Then('Verify the details mentioned in the Overview', async({page}) => {
  await Locations.verifyDetails(page);
})

Then('User clicks on the edit and fill in all the details', async({page}) => {
   await Locations.editFunctionality(page);
})
 
Then('User save the changes and verify the details', async({page}) => {
   await Locations.verifySaveChanges(page);
})

Then('User edit the IPSec GRE in locations', async({page}) => {
  await Locations.editIPSecFunctionality(page);
})

Then('Verify the updated changes', async({page}) => {
  await Locations.verifyIPSecDetails(page);
})

Then('Switch to {string} tab', ({page}, tab: string) => {
   
})

Then('Verify the empty state in Appliances', async({page}) => {
  
})
