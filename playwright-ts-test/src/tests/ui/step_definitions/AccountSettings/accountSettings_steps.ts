import { expect } from '@playwright/test';
import { createBdd } from "playwright-bdd";
import AccountSettings from '../../pages/AccountSettings/accountSettings_page';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
const { Before, Given, When, Then } = createBdd();
import { Page } from "@playwright/test";
let accountSettings: AccountSettings;

Before({ tags: "@AccountSettings" }, async ({ page }: { page: Page }) => {
    accountSettings = new AccountSettings(page);
});

Given('User navigates to the {string} option', async ({}, accountSetting:string) => {
    await accountSettings.navigateToAccountSettings(accountSetting);
});

Then('verify the label {string}', async ({}, labelName:string) => {
    await accountSettings.verifyLabelName(labelName);
  });

Then('User verify the {string} header', async ({  }, headerName:string) => {
    await accountSettings.verifyHeaderName(headerName);
});

Then('User verify the {string} page header', async ({  }, pageHeaderName:string) => {
    await accountSettings.verifyPageHeaderName(pageHeaderName);
})

Then('user click on cancel button', async ({}) => {
    await accountSettings.cancelButton();
})

When('After login user is in Networking Page',  async ({}) => {
    await accountSettings.navigateToNetworking();
    await accountSettings.checkPageUrl();
})

Then('Verify the Zscaler Cloud {string} with user profile API', async ({ }, ZscalerCloud: string) => {
    await accountSettings.verifyZscalerCloud(ZscalerCloud);
});

Then('Verify the Login ID with user profile API', async () => {
    await accountSettings.verifyLoginID();
  });

  Then('Verify the Organization ID with user profile API', async () => {
    await accountSettings.verifyOrganizationID();
  });

Then('Verify the Language preference with user profile API', async () => {
    await accountSettings.verifyLanguagePreference();
  });

  Then('Verify the Time Zone preference with user profile API', async () => {
    await accountSettings.verifyTimeZonePreference();
  });
  
  Then('Verify the Theme dark mode preference with user profile API', async () => {
    await accountSettings.verifyDarkModePreference();
  });