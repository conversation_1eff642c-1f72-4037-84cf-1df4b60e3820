@ZIA_admin @SecurityAlerts
Feature: To verify the Security & EUBA Alerts functionality

# XC-9682
   Scenario: To verify Ongoing Alerts options
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User verify the Ongoing Alerts and "Overview"
    And User verify the hour dropdown
    And User verify the Ongoing Alerts and "Alert Summary"
    Then Verify the Download CSV button

# XC-9683
   Scenario: To verify Alert Summary filters
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User verify the Ongoing Alerts and "Alert Summary"
    Then User add and apply the filters
    Then Verify the Reset of the filters

# XC-9684
   Scenario: To verify the Sort By in Alert Summary  
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User verify the Ongoing Alerts and "Alert Summary"
    Then User sort the summary section from "Most Impacted Systems" to "Last Known Attempt"
    Then User sort the summary section from "Last Known Attempt" to "Most allowed Transactions"
    Then User sort the summary section from "Most allowed Transactions" to "Most Impacted Systems"

# XC-9685
   Scenario: To verify the Hide filters functionality
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User verify the Ongoing Alerts and "Alert Summary"
    Then Verify the filters are hidden when user clicks on the Hide filters
     | Event Type |
     |    User    |   
    Then Verify the filters are shown after clicking on show filters
     | Event Type |
     |    User    |

# XC-9686
   Scenario: To verify the Alert History default values
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User switch to "Alert History" option
    Then Verify the alert history default values

# XC-9687
   Scenario: To verify the Alert History -> Customize view as UEBA
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User switch to "Alert History" option
    And User selects the customize view as "UEBA"
    Then Verify the filters are hidden when user clicks on the Hide filters
     | Event Type |
     | Alert Type |
     |    User    |    
    Then Verify the filters are shown after clicking on show filters
     | Event Type |
     | Alert Type |
     |    User    |   
    And User selects value in all the filters and clicks on Apply
    Then User reset the filters and verify the default values

# XC-9688
   Scenario: To verify the Alert History -> Customize view as Security
    Given User navigates to the Security and EUBA Alerts screen
    When Verify the "Alerts" heading and label "ALERTS"
    And User switch to "Alert History" option
    And User selects the customize view as "Security"
    Then Verify the filters are hidden when user clicks on the Hide filters
     | Event Type |
     |    User    |    
    Then Verify the filters are shown after clicking on show filters
     | Event Type |
     |    User    |
    And User select the values in filters and click on apply
    Then Verify the values are default after clicking on Reset

# XC-9689
   Scenario: To verify the Alert Rules -> Add Alert Rule
    Given User navigates to the Security and EUBA Alerts screen
    And User switch to "Alert Rules" option
    When Verify the "Alert Rules" heading and label "ALERT_RULES"
    And User clicks on the Add Alert Rule button and fill the mandatory details
    Then User save the changes and verify the alert name
    And User clicks on the Edit button and edit the details
    Then Verify the user deletes the alert

# XC-9690
   Scenario: To verify the sorting in Alert Rules
    Given User navigates to the Security and EUBA Alerts screen
    And User switch to "Alert Rules" option
    When Verify the "Alert Rules" heading and label "ALERT_RULES"
    Then Verify all fields sorting

# XC-9691
   Scenario: To verify selection in widgets of Alert Rules
    Given User navigates to the Security and EUBA Alerts screen
    And User switch to "Alert Rules" option
    When Verify the "Alert Rules" heading and label "ALERT_RULES"
    Then Verify the widget "Top 5 Security Alerts by Rule Name" and make a selection
    Then Verify the widget "Top 5 UEBA Alerts by Rule Name" and make a selection

# XC-9692
   Scenario: To verify Add Webhook
    Given User navigates to the Security and EUBA Alerts screen
    And User switch to "Webhooks" option
    When Verify the "Webhooks" heading and label "WEBHOOKS"
    And User clicks on the Add Webhook and fill the mandatory details
    Then User save the changes and searches the webhook to verify the name
    And User edit some changes and verify
    Then Verify the webhook is deleted

# XC-9693
   Scenario: To verify the Webhook Cancel and sorting
    Given User navigates to the Security and EUBA Alerts screen
    And User switch to "Webhooks" option
    When Verify the "Webhooks" heading and label "WEBHOOKS"
    And User clicks on add Webhook and then Cancel
    Then Verify the sorting of all fields