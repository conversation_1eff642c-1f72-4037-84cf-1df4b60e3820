import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions.js';
import { config } from 'dotenv';

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class SecurityAndEUBAAlerts {
    fields: {
        headings: (label: string, heading: string) => string;
        subHeading: (subHeadings: string) => string;
    };

    constructor() {
        this.fields = {
          headings: (label, heading) => `//li[@data-label='${label}']//span[text()='${heading}']`,
          subHeading: (subHeadings) => `//span[text()='${subHeadings}']`,
        };
    }

    async navigateToSecurityAndEUBAAlerts(page: Page): Promise<void>{
          console.log("The Url used is : - "+url);
            if (!url) {
              throw new Error('ONE_UI_BASE_URL environment variable is not set');
            }
            if (url.includes("console")) {
              await page.goto(url+"internet-saas#administration/alerts");
              await page.waitForTimeout(15000);
              }
            else{
              await page.goto(url+"#alerts");
              await page.waitForTimeout(15000);
              await page.getByRole('button', { name: 'Start Configuration ' }).click();
              await page.getByRole('tab', { name: 'Alerts' }).locator('span').click();
              await page.getByRole('alert').locator('path').click();
            }
    }

    async verifyHeading(page: Page, heading: string, label: string): Promise<void>{
        const alert = await page.locator(this.fields.headings(label, heading));
        expect(alert).toHaveText(heading);
    }

    async verifyOngoingAlert(page: Page, button: string): Promise<void>{
        const ongoingAlert = await page.locator(this.fields.headings('ONGOING_ALERTS','Ongoing Alerts'));
        expect(ongoingAlert).toHaveText('Ongoing Alerts');

        const overview = await page.locator(this.fields.subHeading(button));
        expect(overview).toHaveText(button);
        await page.getByRole('button', { name: `${button}` }).click();
        await page.getByRole('button', { name: `${button}` }).click();
    }

    async verifyHourDropdown(page: Page): Promise<void>{
        await page.getByRole('button', { name: '1 hour' }).click();
        for (const label of [
            '2 hours',
            '4 hours',
            '6 hours',
            '12 hours',
            '24 hours',
            '48 hours',
            '7 days',
            '14 days'
          ]) {
            await expect(page.getByText(label, {exact: true})).toBeVisible();
        }
    }

    async verifyDownloadButton(page: Page): Promise<void>{
        await page.getByRole('button', { name: ' Download CSV' }).click();
        await page.waitForTimeout(5000);
    }

    async addAndApplyFilter(page: Page): Promise<void>{
        const event = await page.getByText('Event Type').nth(1);
        expect(event).toHaveText('Event Type');
        await page.locator('span').filter({ hasText: 'Event TypeAll' }).getByRole('button').click();
        await page.getByRole('checkbox', { name: 'Advanced Threat Protection' }).check();
        await page.getByText('Done').click();

        const user = await page.getByText('User', { exact: true });
        expect(user).toHaveText('User');
        await page.getByRole('button', { name: 'All ' }).click();
        await page.getByRole('checkbox', { name: '11.1111' }).check();
        await page.getByText('Done').click();

        await page.getByRole('button', { name: ' Add Filter ' }).click();
        await page.getByRole('button', { name: 'Alert Rule Name' }).click();
        const alertRuleName = await page.getByText('Alert Rule Name');
        expect(alertRuleName).toHaveText('Alert Rule Name');
        
        await page.getByRole('button', { name: ' Add Filter ' }).click();
        await page.getByRole('textbox', { name: 'Search...' }).click();
        await page.getByRole('textbox', { name: 'Search...' }).fill('Location');
        await page.getByRole('button', { name: 'Location' }).click();
        const location = await page.getByText('Location', { exact: true });
        expect(location).toHaveText('Location');
        await page.getByRole('button', { name: 'All ' }).nth(1).click();
        await page.getByRole('textbox', { name: 'Search...' }).click();
        await page.getByRole('textbox', { name: 'Search...' }).fill('Road Warrior');
        await page.getByRole('checkbox', { name: 'Road Warrior' }).check();
        await page.getByText('Done').click();

        await page.getByRole('button', { name: 'Apply' }).click();
        await page.waitForTimeout(8000);
    }

    async resetFilter(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Reset' }).click();
        await page.getByText('Event Type').nth(1).isVisible({timeout: 5000});
        await page.locator('span').filter({ hasText: 'Event TypeAll' }).getByRole('button').isVisible();
        await page.getByText('User', { exact: true }).isVisible();
        await page.locator('span').filter({ hasText: 'UserAll' }).getByRole('button').isVisible();
        await page.getByText('Alert Rule Name').isHidden();
        await page.getByText('Location', { exact: true }).isHidden();
    }

    async sortByAlertSummary(page: Page, selected: string, sortBy: string): Promise<void>{
        await page.getByRole('button', { name: `${selected} ` }).click();
        await page.getByRole('option', { name: `${sortBy}` }).click();
        const sorting = await page.getByRole('button', { name: `${sortBy}` });
        expect(sorting).toHaveText(sortBy);
        await page.waitForTimeout(6000);
    }
    
    async hideFilters(page: Page, elements: string[]): Promise<void>{
        await page.getByRole('button', { name: ' Hide Filters' }).click();
        for(const label of elements){
            await page.getByText(label).first().isHidden();
            await page.locator('span').filter({ hasText: `${label}All` }).getByRole('button').isHidden();
        }
        await page.getByRole('button', { name: 'Apply' }).isHidden();
        await page.getByRole('button', { name: 'Reset' }).isHidden();
    }

    async showFilters(page: Page, elements: string[]): Promise<void>{
        await page.getByRole('button', { name: ' Show Filters' }).click();
        for(const label of elements){
            await page.getByText(label).first().isHidden();
            await page.locator('span').filter({ hasText: `${label}All` }).getByRole('button').isHidden();
        }
        await page.getByRole('button', { name: 'Apply' }).isVisible();
        await page.getByRole('button', { name: 'Reset' }).isVisible();
    }

    async verifyDefaultValuesAlertHistory(page: Page): Promise<void>{
        await page.getByTestId('accordion-item').click();
        await page.getByTestId('accordion-item').click();
        await page.getByText('Customize View:').isVisible();
        await page.getByRole('button', { name: 'All Alert Classes ' }).isVisible();
        await page.getByRole('button', { name: 'hour ' }).isVisible();
        const download1Promise = page.waitForEvent('download');
        await page.getByRole('button', { name: ' Download CSV' }).click();
        const download1 = await download1Promise;
    }

    async selectCustomizeView(page: Page, selection: string): Promise<void>{
        const alertSummary = await page.locator(this.fields.subHeading('Alert Summary'));
        expect(alertSummary).toHaveText('Alert Summary');
        await page.getByText('Customize View:').isVisible();
        await page.getByRole('button', { name: 'All Alert Classes ' }).click();
        await page.getByRole('option', { name: selection }).click();
        const value = await page.getByRole('button', { name: `${selection} ` });
        expect(value).toHaveText(selection);
    }

    async selectValuesApply(page: Page): Promise<void>{
        await page.getByText('Event Type').first().isVisible();
        await page.locator('span').filter({ hasText: 'Event TypeAll' }).getByRole('button').click();
        await page.getByRole('checkbox', { name: 'Access' }).check();
        await page.getByText('Done').click();
        await page.getByText('Alert Type').first().isVisible();
        await page.locator('span').filter({ hasText: 'Alert TypeAll' }).getByRole('button').click();
        await page.getByRole('checkbox', { name: 'Bulk activity' }).check();
        await page.getByText('Done').click();
        await page.getByText('User').nth(2).isVisible();
        await page.getByRole('button', { name: 'All ' }).click();
        await page.getByRole('checkbox', { name: '11.1111' }).check();
        await page.getByText('Done').click();
        await page.getByRole('button', { name: 'Apply' }).click();
    }

    async resetAlertHistory(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Reset' }).click();
        await page.locator('span').filter({ hasText: 'Event TypeAll' }).getByRole('button').isVisible();
        await page.locator('span').filter({ hasText: 'Alert TypeAll' }).getByRole('button').isVisible();
        await page.locator('span').filter({ hasText: 'UserAll' }).getByRole('button').isVisible();
    }

    async applySecurity(page: Page): Promise<void>{
        const eventType = await page.getByText('Event Type').first();
        expect(eventType).toHaveText('Event Type');
        await page.locator('span').filter({ hasText: 'Event TypeAll' }).getByRole('button').click();
        await page.getByRole('checkbox', { name: 'Advanced Security' }).check();
        await page.getByText('Done').click();

        const user = await page.getByText('User', { exact: true });
        expect(user).toHaveText('User');
        await page.getByRole('button', { name: 'All ' }).click();
        await page.getByRole('checkbox', { name: '11.1111' }).check();
        await page.getByText('Done').click();
        await page.getByRole('button', { name: 'Apply' }).click();
    }

    async resetSecurity(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Reset' }).click();
        await page.locator('span').filter({ hasText: 'Event TypeAll' }).getByRole('button').isVisible();
        await page.locator('span').filter({ hasText: 'UserAll' }).getByRole('button').isVisible();
    }

    async addAlertRule(page: Page): Promise<void>{
        await page.getByRole('button', { name: ' Add Alert Rule' }).click();
        await page.getByRole('textbox', { name: 'alertName' }).click();
        await page.getByRole('textbox', { name: 'alertName' }).fill('Test Alert');
        await page.getByRole('button', { name: 'Security ' }).click();
        await page.getByRole('option', { name: 'Security' }).click();
        await page.getByRole('button', { name: 'Enabled ' }).isVisible();
        await page.getByTestId('multi-dropdown').getByRole('button', { name: 'None ' }).click();
        await page.locator('label').filter({ hasText: 'Advanced Threat Protection' }).click();
        await page.getByTestId('multi-dropdown').getByText('Done').click();
        await page.getByRole('button', { name: 'mins ' }).click();
        await page.getByRole('option', { name: 'hour' }).click();
        await page.getByRole('button', { name: ' Add Filter ' }).click();
        await page.getByRole('button', { name: 'Location' }).click();
        await page.getByRole('button', { name: 'None ' }).first().click();
        await page.getByRole('textbox', { name: 'Search...' }).click();
        await page.getByRole('textbox', { name: 'Search...' }).fill('road warrior');
        await page.getByRole('checkbox', { name: 'Road Warrior' }).check();
        await page.locator('span').filter({ hasText: 'Done' }).click();
        await page.getByRole('button', { name: ' Add Filter ' }).click();
        await page.getByRole('button', { name: 'Systems Impacted' }).click();
        await page.getByRole('textbox', { name: 'numSystemImpacted' }).click();
        await page.getByRole('textbox', { name: 'numSystemImpacted' }).fill('2');
        await page.getByRole('button', { name: ' Add Filter ' }).click();
        await page.getByRole('button', { name: 'Users' }).click();
        await page.getByRole('button', { name: 'None ' }).first().click();
        await page.getByRole('checkbox', { name: 'admin (<EMAIL>)' }).check();
        await page.locator('span').filter({ hasText: 'Done' }).click();
        await page.getByRole('checkbox', { name: ' ' }).click();
        await page.getByRole('textbox').nth(2).click();
        await page.getByRole('textbox').nth(2).fill('2');
        await page.getByRole('textbox', { name: 'Add Items' }).click();
        await page.getByRole('textbox', { name: 'Add Items' }).fill('<EMAIL>');
    }

    async verifyAndSaveChanges(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Save' }).click();
        await page.waitForTimeout(5000);
        const alertName = await page.getByText('Test Alert');
        expect(alertName).toContainText('Test Alert');
    }

    async editAlertRule(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Edit' }).click();
        await page.waitForTimeout(3000);
        await page.getByRole('button', { name: 'Enabled ' }).click();
        await page.getByRole('option', { name: 'Disabled' }).click();
        await page.getByRole('button', { name: 'Save' }).click();
        await page.waitForTimeout(3000);
        const alertName = await page.getByText('Test Alert');
        expect(alertName).toContainText('Test Alert');
        const status = await page.getByTestId('cell-status').getByText('Disabled');
        expect(status).toHaveText('Disabled');
    }

    async deleteAlertRule(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Delete' }).click();
        await page.getByText('Please confirm that you want').isVisible();
        await page.locator(this.fields.subHeading('Delete')).click();
        await page.waitForTimeout(7000);
        const noResultFound = await page.getByRole('table').getByText('No matching data found');
        expect(noResultFound).toHaveText('No matching data found');
    }

    async verifyAlertRuleSort(page: Page): Promise<void>{
       await page.getByTestId('columnHeader-alertName').getByRole('button', { name: 'Sort' }).click();
       await page.getByTestId('columnHeader-alertClass').getByRole('button', { name: 'Sort' }).click();
       await page.getByTestId('columnHeader-eventTypes').getByRole('button', { name: 'Sort' }).click();
       await page.getByTestId('columnHeader-alertType').getByRole('button', { name: 'Sort' }).click();
       await page.getByTestId('columnHeader-appliesTo').getByRole('button', { name: 'Sort' }).click();
       await page.getByTestId('columnHeader-recipient').getByRole('button', { name: 'Sort' }).click();
       await page.getByTestId('columnHeader-status').getByRole('button', { name: 'Sort' }).click();
    }

    async widgetTimeSelection(page: Page, widget: string): Promise<void>{
       const widgetName = await page.getByText(widget);
       expect(widgetName).toHaveText(widget);
       await page.getByRole('button', { name: '7 days ' }).first().click();
       await page.getByRole('option', { name: '14 days' }).click();
       const selection = await page.getByRole('button', { name: '14 days ' });
       expect(selection).toHaveText('14 days');
       await page.waitForTimeout(4000);
    }

    async addWebhooks(page: Page): Promise<void>{
        await page.getByRole('button', { name: ' Add Webhook' }).click();
        await page.locator('#Name').getByText('Name', { exact: true }).isVisible();
        await page.getByTestId('textBox-name').getByRole('textbox', { name: 'name' }).click();
        await page.getByTestId('textBox-name').getByRole('textbox', { name: 'name' }).fill('Test Webhook');
        await page.getByRole('textbox', { name: 'urlText' }).click();
        await page.getByRole('textbox', { name: 'urlText' }).fill('https://zscaler.test.com');
        await page.getByRole('textbox', { name: 'userName' }).click();
        await page.getByRole('textbox', { name: 'userName' }).fill('test');
        await page.getByRole('textbox', { name: 'Enter Text' }).click();
        await page.getByRole('textbox', { name: 'Enter Text' }).fill('test');
    }

    async searchWebhook(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Save' }).click();
        await page.getByText('All changes have been saved.').isVisible();
        await page.getByRole('textbox', { name: 'Search...' }).click();
        await page.getByRole('textbox', { name: 'Search...' }).fill('Test Webhook');
        const name = await page.getByText('Test Webhook');
        expect(name).toHaveText('Test Webhook');
        await page.getByRole('button', { name: 'Clear search' }).click();
    }

    async editWebhook(page: Page): Promise<void>{
        await page.waitForTimeout(6000);
        await page.getByRole('button', { name: 'Edit' }).click();
        await page.getByRole('button', { name: 'Disabled' }).click();
        await page.getByRole('textbox', { name: 'Enter Text' }).click();
        await page.getByRole('textbox', { name: 'Enter Text' }).fill('test');
        await page.getByRole('button', { name: 'Save' }).click();
        await page.getByText('All changes have been saved.').isVisible();
        
        const status = await page.getByText('Disabled');
        expect(status).toHaveText('Disabled');
    }

    async deleteWebhook(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Delete' }).click();
        await page.getByText('Please confirm that you want').isVisible();
        await page.locator(this.fields.subHeading('Delete')).click();
        await page.getByText('The item has been deleted.').isVisible();
        await page.waitForTimeout(7000);
        const noResultFound = await page.getByText('No matching data found');
        expect(noResultFound).toHaveText('No matching data found');
    }

    async cancelWebhook(page: Page): Promise<void>{
        await page.getByRole('button', { name: ' Add Webhook' }).click();
        await page.getByTestId('textBox-name').getByRole('textbox', { name: 'name' }).click();
        await page.getByTestId('textBox-name').getByRole('textbox', { name: 'name' }).fill('test');
        await page.getByRole('button', { name: 'Cancel' }).click();
        await page.waitForTimeout(2000);
        const cancel = await page.getByText('All changes have been canceled.');
        expect(cancel).toHaveText('All changes have been canceled.');
    }

    async sortWebhook(page: Page): Promise<void>{
        await page.getByTestId('columnHeader-name').getByRole('button', { name: 'Sort' }).click();
        await page.getByTestId('columnHeader-status').getByRole('button', { name: 'Sort' }).click();
    }

}
  
export default new SecurityAndEUBAAlerts();