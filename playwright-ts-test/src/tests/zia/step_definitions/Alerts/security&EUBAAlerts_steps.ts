import { createBdd } from 'playwright-bdd';
const { Given, When, Then } = createBdd();
import SecurityAndEUBAAlerts from '../../pages/Alerts/security&EUBAAlerts_page';

Given('User navigates to the Security and EUBA Alerts screen', async({page}) => {
    await SecurityAndEUBAAlerts.navigateToSecurityAndEUBAAlerts(page);
})

When('Verify the {string} heading and label {string}', async({page}, heading, label) => {
    await SecurityAndEUBAAlerts.verifyHeading(page, heading, label);
})

When('User verify the Ongoing Alerts and {string}', async({page}, button) => {
    await SecurityAndEUBAAlerts.verifyOngoingAlert(page, button);
})

When('User verify the hour dropdown', async({page}) => {
    await SecurityAndEUBAAlerts.verifyHourDropdown(page);
})

Then('Verify the Download CSV button', async({page}) => {
    await SecurityAndEUBAAlerts.verifyDownloadButton(page);
})

Then('User add and apply the filters', async({page}) => {
    await SecurityAndEUBAAlerts.addAndApplyFilter(page);
})

Then('Verify the Reset of the filters', async({page}) => {
    await SecurityAndEUBAAlerts.resetFilter(page);
})

Then('User sort the summary section from {string} to {string}', async({page}, selected: string, sortBy: string) => {
    await SecurityAndEUBAAlerts.sortByAlertSummary(page, selected, sortBy);
})

Then('Verify the alert history default values', async({page}) => {
   await SecurityAndEUBAAlerts.verifyDefaultValuesAlertHistory(page);
})

When('User selects the customize view as {string}', async({page}, selection: string) => {
   await SecurityAndEUBAAlerts.selectCustomizeView(page, selection);
})

Then('Verify the filters are hidden when user clicks on the Hide filters', async({page}, table) => {
   const elements = table.raw().map((row: string[]) => row[0]);
   await SecurityAndEUBAAlerts.hideFilters(page, elements);
})

Then('Verify the filters are shown after clicking on show filters', async({page}, table) => {
   const elements = table.raw().map((row: string[]) => row[0]);
   await SecurityAndEUBAAlerts.showFilters(page, elements);
})

Then('User selects value in all the filters and clicks on Apply', async({page}) => {
   await SecurityAndEUBAAlerts.selectValuesApply(page);
})

Then('User reset the filters and verify the default values', async({page}) => {
   await SecurityAndEUBAAlerts.resetAlertHistory(page);
})

Then('User select the values in filters and click on apply', async({page}) => {
   await SecurityAndEUBAAlerts.applySecurity(page);
})

Then('Verify the values are default after clicking on Reset', async({page}) => {
   await SecurityAndEUBAAlerts.resetSecurity(page);
})

When('User clicks on the Add Alert Rule button and fill the mandatory details', async({page}) => {
   await SecurityAndEUBAAlerts.addAlertRule(page);
})

Then('User save the changes and verify the alert name', async({page}) => {
   await SecurityAndEUBAAlerts.verifyAndSaveChanges(page);
})

Then('User clicks on the Edit button and edit the details', async({page}) => {
   await SecurityAndEUBAAlerts.editAlertRule(page);
})

Then('Verify the user deletes the alert', async({page}) => {
   await SecurityAndEUBAAlerts.deleteAlertRule(page);
})

Then('Verify all fields sorting', async({page}) => {
   await SecurityAndEUBAAlerts.verifyAlertRuleSort(page);
})

Then('Verify the widget {string} and make a selection', async({page}, widget: string) => {
   await SecurityAndEUBAAlerts.widgetTimeSelection(page, widget);
})

When('User clicks on the Add Webhook and fill the mandatory details', async({page}) => {
   await SecurityAndEUBAAlerts.addWebhooks(page);
})

Then('User save the changes and searches the webhook to verify the name', async({page}) => {
   await SecurityAndEUBAAlerts.searchWebhook(page);
})

Then('User edit some changes and verify', async({page}) => {
   await SecurityAndEUBAAlerts.editWebhook(page);
})

Then('Verify the webhook is deleted', async({page}) => {
   await SecurityAndEUBAAlerts.deleteWebhook(page);
})

When('User clicks on add Webhook and then Cancel', async({page}) => {
   await SecurityAndEUBAAlerts.cancelWebhook(page);
})

Then('Verify the sorting of all fields', async({page}) => {
   await SecurityAndEUBAAlerts.sortWebhook(page);
})
