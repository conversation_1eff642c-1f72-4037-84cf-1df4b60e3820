{"name": "playwright-end-to-end-testing-framework", "description": "A powerful testing framework using Playwright for GraphQL and REST APIs with support for reporting, BDD, and more.", "version": "1.2.0", "main": "index.js", "type": "module", "scripts": {"test:e2e": "npm run clean:folders && npx bddgen && npx playwright test", "test:parallel": "bddgen && playwright test --workers=4", "test:smoke": "bddgen && playwright test --grep @smoke", "test:this": "npm run clean:folders && bddgen && playwright test --grep @k", "test:debug": "bddgen && playwright test --grep @debug --debug", "test:report-serve": "allure serve ./allure-report", "test:report-generate": "allure generate ./allure-results -o ./allure-report --clean", "test:report-open": "allure open ./allure-report", "clean:folders": "rimraf .features-gen allure-results playwright-report ./resources/downloads/visualComparison", "test:analytics": "npx bddgen && npx playwright test --project='Networking' --project='DigitalExperience' --project='Cybersecurity' --project='Operational' --project='TopNav'", "test:globalnav": "npx bddgen && npx playwright test --project='GlobalNavigation'", "test:dark-theme": "npx bddgen && npx playwright test --project='DarkTheme'", "test:RBAC": "npx bddgen && npx playwright test --project='RBAC'"}, "keywords": [], "author": "", "license": "ISC", "config": {"logLevel": "info"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@cucumber/cucumber": "^11.2.0", "@playwright/test": "^1.51.1", "@types/big-integer": "^0.0.30", "@types/node": "^22.13.5", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "allure-commandline": "^2.33.0", "allure-playwright": "^3.2.1", "exceljs": "^4.4.0", "lodash": "^4.17.21", "playwright-bdd": "^7.5.0", "rimraf": "^6.0.1", "typescript": "^5.7.3"}, "dependencies": {"big-integer": "^1.6.52", "dotenv": "^16.4.7", "node-tesseract-ocr": "^2.2.1", "pixelmatch": "^6.0.0", "pngjs": "^7.0.0", "sharp": "^0.34.1", "tesseract.js": "^6.0.0"}}