import { expect, Page, Frame, Locator, Dialog } from '@playwright/test';
import sharp from "sharp";
import { getExpectedTheme } from './hooks';
import * as fs from 'fs';
import pixelmatch from 'pixelmatch';
import { PNG } from 'pngjs';
let numDiffPixels : number;

type ARIARole =
  | 'alert'
  | 'alertdialog'
  | 'application'
  | 'article'
  | 'banner'
  | 'blockquote'
  | 'button'
  | 'caption'
  | 'cell'
  | 'checkbox'
  | 'code'
  | 'columnheader'
  | 'combobox'
  | 'complementary'
  | 'contentinfo'
  | 'definition'
  | 'deletion'
  | 'dialog'
  | 'directory'
  | 'document'
  | 'emphasis'
  | 'feed'
  | 'figure'
  | 'form'
  | 'generic'
  | 'grid'
  | 'gridcell'
  | 'group'
  | 'heading'
  | 'img'
  | 'insertion'
  | 'link'
  | 'list'
  | 'listbox'
  | 'listitem'
  | 'log'
  | 'main'
  | 'marquee'
  | 'math'
  | 'menu'
  | 'menubar'
  | 'menuitem'
  | 'menuitemcheckbox'
  | 'menuitemradio'
  | 'meter'
  | 'navigation'
  | 'none'
  | 'note'
  | 'option'
  | 'paragraph'
  | 'presentation'
  | 'progressbar'
  | 'radio'
  | 'radiogroup'
  | 'region'
  | 'row'
  | 'rowgroup'
  | 'rowheader'
  | 'scrollbar'
  | 'search'
  | 'searchbox'
  | 'separator'
  | 'slider'
  | 'spinbutton'
  | 'status'
  | 'strong'
  | 'subscript'
  | 'superscript'
  | 'switch'
  | 'tab'
  | 'table'
  | 'tablist'
  | 'tabpanel'
  | 'term'
  | 'textbox'
  | 'time'
  | 'timer'
  | 'toolbar'
  | 'tooltip'
  | 'tree'
  | 'treegrid'
  | 'treeitem';

class PlaywrightActions {
  frame: Frame | Page | null;
  setFramePath: string;
  setFrame: boolean;
  verbose: boolean;

  constructor() {
    this.frame = null;
    this.setFramePath = "";
    this.setFrame = false;
    this.verbose = true;
  }



  async captureAndCompareScreenshot(page: Page, dataTestId: string, actualScreenshot: string, expectedScreenshot: string, diffScreenshot: string){
    //capture the current screenshot maActualScreenshots/Actual_AdminRoleBased.png
    await page.locator(dataTestId).screenshot({path: `./resources/downloads/expectedScreenshots/${expectedScreenshot}`});
    await page.locator(dataTestId).screenshot({path: `./resources/downloads/visualComparison/${actualScreenshot}`});

    // Load the baseline expected and newly captured actual screenshots
    const baselinePath =  `./resources/downloads/expectedScreenshots/${expectedScreenshot}`;
    const newscreenshotPath = `./resources/downloads/visualComparison/${actualScreenshot}`;
    const baseline = PNG.sync.read(fs.readFileSync(baselinePath));
    const newScreenshot = PNG.sync.read(fs.readFileSync(newscreenshotPath));
            
    //create an empty image to store the pixel difference
    const diff = new PNG({width: baseline.width, height: baseline.height});
            
    console.log("pixelmatch: ", pixelmatch);
    //compare the two screenshots and count different pixel
    numDiffPixels = pixelmatch(baseline.data, newScreenshot.data, diff.data, baseline.width, baseline.height, {threshold:0.1});
            
    //save the diff image for debugging purposes
    const diffPath = `./resources/downloads/visualComparison/${diffScreenshot}`;
    const parentPath = diffPath.split("/").slice(0,-1).join('/');
    if(!fs.existsSync(parentPath))
    {
      fs.mkdirSync(parentPath);
    }
    fs.writeFileSync(diffPath, PNG.sync.write(diff));
  }

  async verifyScreenshot(){
    // Assert the number of different pixels is below a threshold
    expect(numDiffPixels).toBeLessThan(700);
  }

  async fetchBackgroundColor(page: Page): Promise<string> {
    // Take a screenshot of the body element
    const screenshotBuffer = await page.screenshot({
      clip: await page.evaluate(() => {
        const body = document.querySelector("body");
        const rect = body!.getBoundingClientRect();
        return {
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height,
        };
      }),
    });

    // Use sharp to extract pixel data
    const image = sharp(screenshotBuffer);
    const { data, info } = await image.raw().toBuffer({ resolveWithObject: true });

    // Get the color of the pixel at the center of the body
    const centerX = Math.floor(info.width / 2);
    const centerY = Math.floor(info.height / 2);
    const pixelIndex = (centerY * info.width + centerX) * 3; // 3 bytes per pixel (RGB)

    const r = data[pixelIndex];
    const g = data[pixelIndex + 1];
    const b = data[pixelIndex + 2];

    const backgroundColor = `rgb(${r},${g},${b})`;
    console.log(`Background color fetched: ${backgroundColor}`);
    return backgroundColor;
  }

  //  async fetchBackgroundColor(page: Page): Promise<string> {
  //   const clip = await page.evaluate(() => {
  //   const body = document.querySelector("body");
  //   if (!body) throw new Error("Body element not found");

  //   const rect = body.getBoundingClientRect();
  //   return {
  //     x: rect.left,
  //     y: rect.top,
  //     width: rect.width,
  //     height: rect.height,
  //   };
  // });

  // const screenshotBuffer = await page.screenshot({ clip });

  // // Use sharp to extract pixel data (ensure it's RGB only)
  // const image = sharp(screenshotBuffer).removeAlpha();
  // const { data, info } = await image.raw().toBuffer({ resolveWithObject: true });

  // const centerX = Math.floor(info.width / 2);
  // const centerY = Math.floor(info.height / 2);
  // const sampleRadius = 3; // sample 7x7 area
  // let rTotal = 0, gTotal = 0, bTotal = 0, count = 0;

  // for (let dy = -sampleRadius; dy <= sampleRadius; dy++) {
  //   for (let dx = -sampleRadius; dx <= sampleRadius; dx++) {
  //     const x = Math.min(Math.max(centerX + dx, 0), info.width - 1);
  //     const y = Math.min(Math.max(centerY + dy, 0), info.height - 1);

  //     const index = (y * info.width + x) * info.channels;
  //     rTotal += data[index];
  //     gTotal += data[index + 1];
  //     bTotal += data[index + 2];
  //     count++;
  //   }
  // }

  // const r = Math.round(rTotal / count);
  // const g = Math.round(gTotal / count);
  // const b = Math.round(bTotal / count);

  // const backgroundColor = `rgb(${r},${g},${b})`;
  // console.log(`Background color fetched: ${backgroundColor}`);
  // return backgroundColor;
  // }

  async verifyTheme(page: Page, expectedTheme: string): Promise<void> {
    const backgroundColor = await this.fetchBackgroundColor(page);

    // Convert the background color to RGB values
    const rgbValues = backgroundColor
      .replace("rgb(", "")
      .replace(")", "")
      .split(",")
      .map((value) => parseInt(value.trim()));

    // Calculate the brightness of the color
    const brightness =
      (rgbValues[0] * 299 + rgbValues[1] * 587 + rgbValues[2] * 114) / 1000;

    // Determine if the theme is Dark or Light
    const actualTheme = brightness < 128 ? "Dark" : "Light";

    // Debug logs
    console.log(`Background color fetched: ${backgroundColor}`);
    console.log(`RGB Values: ${rgbValues}`);
    console.log(`Calculated Brightness: ${brightness}`);
    console.log(`Detected Theme: ${actualTheme}`);

    // Assert the theme
    expect(actualTheme).toBe(expectedTheme);
  }

  async getByRoleAndClick(page: Page, role: ARIARole, icon: string): Promise<void> {
    await page.getByRole(role, { name: icon }).isVisible();
    await page.getByRole(role, { name: icon }).click();
  }

  async getByRole(page: Page, role: ARIARole, icon: string): Promise<Locator> {
    await page.waitForLoadState('domcontentloaded');
    const element = page.getByRole(role, { name: icon });
    return element;
  }

  async verifyTextAndClick(page: Page, dataTestId: string, text: string): Promise<void> {
    const element = page.getByTestId(dataTestId);
    await expect(element).toHaveText(text);
    await element.click();
  }

  async closePopup(page: Page): Promise<void> {
    const topnav = await page.getByTestId("mega-menu-container").isVisible();
    if(topnav)
    {
      await page.locator(`(//div[@data-testid="mega-menu-container"]/div/div/button/i)`).click();
    }
  }

  async checkPageRenderWithTheme(
    page: Page, 
    PageName: string, 
    pageLink: string, 
    pageSelector: string
  ): Promise<void> {
    await this.checkPageRender(page, PageName, pageLink, pageSelector);
    if (getExpectedTheme()) {
      await this.verifyTheme(page, "Dark");
    }
  }

  async checkPageRender(
    page: Page, 
    pageName: string, 
    testId: string, 
    pageSelector: string
  ): Promise<void> {
    const Page = page.getByTestId(testId);
    await expect(Page).toHaveText(pageName);
    await Page.click();
    await page.waitForSelector(pageSelector, { timeout: 80000 });
    const NewPage = page.locator(pageSelector);
    await expect(NewPage).toBeVisible();
    
    const popup = await page.locator("(//i[@class='fas fa-times notification-close-icon'])").isVisible();
    if (popup) {
      await page.locator("(//i[@class='fas fa-times notification-close-icon'])").click();
      await page.mouse.move(50, 50);
      await page.waitForTimeout(2000);
    }

    const zbanner = await page.getByTestId("dismiss-banner-btn").isVisible();
    if (zbanner) {
      await page.getByTestId("dismiss-banner-btn").click();
      await page.mouse.move(50, 50);
      await page.waitForTimeout(2000);
    }

    const errorCard = await page.locator(`//div[@class="notification error"]`).isVisible();
    if(errorCard)
    {
      await page.locator(`//span[@class="notification-close-icon"]`).click();
      await page.mouse.move(50, 50);
      await page.waitForTimeout(2000);
    }
    
    const maskPopup = await page.locator(`//div[@class="dialog-mask help-dialog-mask"]`).isVisible();
    if(maskPopup)
    {
      await page.locator("(//i[@class='dialog-header-close fas fa-times -js-ok-button'])").click();
      await page.mouse.move(50, 50);
      await page.waitForTimeout(2000);
    }

    await NewPage.click();
    await page.mouse.move(20, 20);
  }

  async verifyTextAndHover(page: Page, dataTestId: string, text: string): Promise<void> {
    const element = page.getByTestId(dataTestId);
    await expect(element).toHaveText(text);
    await element.hover();
  }

  async waitAndGetByTestId(page: Page, dataTestId: string): Promise<Locator> {
    await page.waitForTimeout(5000);
    await page.getByTestId(dataTestId).isVisible();
    const cybersecurity = page.getByTestId(dataTestId);
    return cybersecurity;
  }

  async waitAndClickByTestId(page: Page, dataTestId: string): Promise<void> {
    await page.waitForTimeout(5000);
    await page.getByTestId(dataTestId).isVisible();
    await page.getByTestId(dataTestId).click();
  }

  async getByTextAndClick(page: Page, text: string): Promise<void> {
    await page.getByText(text).isVisible();
    await page.getByText(text).click();
    await page.waitForTimeout(10000);
  }

  async waitAndVisibleLocator(page: Page, locator: string): Promise<Locator> {
    await page.waitForTimeout(3000);
    await page.locator(locator).isVisible();
    const title = page.locator(locator);
    return title;
  }

  async waitAndClickByLocator(page: Page, locator: string): Promise<void> {
    await page.waitForTimeout(1000);
    await page.locator(locator).isVisible();
    await page.locator(locator).click();
  }

  async waitForTestIdVisiblity(page: Page, testId: string): Promise<void> {
    await expect(page.getByTestId(testId)).toBeVisible();
  }

  async waitForLocatorVisiblity(page: Page, testId: string): Promise<void> {
    await expect(page.locator(testId)).toBeVisible();
  }

  async waitForPlaceHolderVisiblity(page: Page, testId: string): Promise<void> {
    await expect(page.getByPlaceholder(testId)).toBeVisible();
  }

  getCurrentFrameStatus(): { setFramePath: string; setFrame: boolean } {
    return { setFramePath: this.setFramePath, setFrame: this.setFrame };
  }

  async verboseLog(action: string, selector: string, message = ''): Promise<void> {
    if (this.verbose) {
      const timestamp = new Date().toISOString();
      const formattedMessage = `[Playwright Action] - [${timestamp}] - [${action}] - [${selector}]${message ? ` - ${message}` : ''}`;
      console.debug(formattedMessage);
    }
  }

  async switchTo(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Switching to frame', selector, 'Attempting to switch frame context.');

    if (selector && selector.trim() !== '') {
        const frameElement = page.locator(selector);
        await frameElement.waitFor({ state: 'attached' });

        this.setFrame = true;
        this.setFramePath = selector;

        // Get the frame by selector
        const frame = page.frame({ name: selector }) || page.frame({ url: selector });

        if (!frame) {
            throw new Error(`Frame not found for selector: ${selector}`);
        }

        this.frame = frame;
        await this.verboseLog('Switched to frame', selector, 'Frame has been successfully located.');
    } else {
        this.setFrame = false;
        this.frame = page;
        await this.verboseLog('Switch to frame', selector, 'No valid selector provided, defaulting to main page.');
    }
}


async switchFrame(page: Page): Promise<Frame | Page> {
  const currentContext = this.setFrame ? 'iFrame' : 'Main Page';
  await this.verboseLog('Switching frame context', '', `Current context is: ${currentContext}`);

  let result: Frame | Page = page; // Default to the main page

  if (this.setFrame && this.setFramePath) {
      // Use page.frame() to get the actual Frame object
      const frame = page.frame({ name: this.setFramePath }) || page.frame({ url: this.setFramePath });

      if (!frame) {
          throw new Error(`Frame not found for selector: ${this.setFramePath}`);
      }

      result = frame;
  }

  await this.verboseLog('Context switched', '', `Now using context: ${this.setFrame ? 'iFrame' : 'Main Page'}`);
  return result;
}


  async waitAndClick(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Waiting for element to be visible', selector, 'Getting current frame or main page.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await element.waitFor({ state: 'visible' });
    await this.verboseLog('Element visibility', selector, 'Element is now visible.');
    await element.click();
    await this.verboseLog('Clicked element', selector, 'Element has been clicked successfully.');
  }

  async waitAndGetXYCoordinates(page: Page, selector: string): Promise<{ x: number; y: number; box: any }> {
    await this.verboseLog('Waiting for element to be visible', selector, 'Getting current frame or main page.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);

    await element.waitFor({ state: 'visible' });
    await this.verboseLog('Element visibility', selector, 'Element is now visible.');

    const box = await element.boundingBox();
    if (!box) {
      throw new Error(`Element with selector "${selector}" is not visible.`);
    }

    await this.verboseLog('Retrieved coordinates', selector, `Coordinates: X=${box.x}, Y=${box.y}`);
    return { x: box.x, y: box.y, box: box };
  }

  async checkCheckbox(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Checking checkbox', selector, 'Attempting to check checkbox.');
    const frame = await this.switchFrame(page);
    await frame.locator(selector).setChecked(true);
    await expect(frame.locator(selector)).toBeChecked();
    await this.verboseLog('Checked checkbox', selector, 'Checkbox has been checked successfully.');
  }

  async uncheckCheckbox(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Unchecking checkbox', selector, 'Attempting to uncheck checkbox.');
    const frame = await this.switchFrame(page);
    const checkbox = frame.locator(selector);
    await checkbox.waitFor({ state: 'visible' });
    if (await checkbox.isChecked()) {
      await checkbox.setChecked(false);
      await expect(checkbox).not.toBeChecked();
      await this.verboseLog('Unchecked checkbox', selector, 'Checkbox has been unchecked successfully.');
    } else {
      await expect(checkbox).not.toBeChecked();
      await this.verboseLog('Checkbox already unchecked', selector);
    }
  }

  async selectRadioButton(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Selecting radio button', selector, 'Attempting to select radio button.');
    const frame = await this.switchFrame(page);
    const radioButton = frame.locator(selector);
    await radioButton.waitFor({ state: 'visible' });
    await radioButton.setChecked(true);
    await this.verboseLog('Selected radio button', selector, 'Radio button has been selected successfully.');
  }

  async waitAndFillField(page: Page, selector: string, text: string): Promise<void> {
    await this.verboseLog('Filling text', selector, `Attempting to fill text: "${text}".`);
    const frame = await this.switchFrame(page);
    const input = frame.locator(selector);
    await input.waitFor({ state: 'visible' });
    await input.fill(text);
    await this.verboseLog('Filled text', selector, `Filled text: "${text}"`);
  }

  async waitAndFillFieldSequentially(page: Page, selector: string, text: string): Promise<void> {
    await this.verboseLog('Filling text', selector, `Attempting to fill text Sequentially: "${text}".`);
    const frame = await this.switchFrame(page);
    const input = frame.locator(selector);
    await input.waitFor({ state: 'visible' });
    await this.clearText(page, selector);
    await input.pressSequentially(text);
    await this.verboseLog('Filled text', selector, `Filled text Sequentially: "${text}"`);
  }

  async clearText(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Clearing text', selector, 'Attempting to clear text.');
    const frame = await this.switchFrame(page);
    const input = frame.locator(selector);
    await input.fill('');
    await this.verboseLog('Cleared text', selector, 'Text box has been cleared.');
  }

  async getTextFromLocator(page: Page, selector: string): Promise<string | null> {
    await this.verboseLog('Retrieving text from locator', selector, 'Getting text content.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    const text = await element.textContent();
    await this.verboseLog('Retrieved text from locator', selector, `Text: "${text}"`);
    return text;
  }

  async getTextFromAttribute(page: Page, selector: string, attribute: string): Promise<string | null> {
    await this.verboseLog('Retrieving text from attribute', selector, `Getting attribute: "${attribute}".`);
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    const value = await element.getAttribute(attribute);
    await this.verboseLog('Retrieved text from attribute', selector, `Attribute: "${attribute}", Value: "${value}"`);
    return value;
  }

  async getTextFromInnerText(page: Page, selector: string): Promise<string> {
    await this.verboseLog('Retrieving inner text from element', selector, 'Getting inner text.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    const value = await element.innerText();
    await this.verboseLog('Retrieved inner text from element', selector, `Value: "${value}"`);
    return value;
  }

  async getInputValue(page: Page, selector: string): Promise<string> {
    await this.verboseLog('Retrieving value from input element', selector, 'Getting input value.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    const value = await element.inputValue();
    await this.verboseLog('Retrieved value from input element', selector, `Value: "${value}"`);
    return value;
  }

  async validateElementIsEnabled(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Validating element is enabled', selector, 'Checking if element is enabled.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await element.waitFor({ state: 'visible' });
    await expect(element).toBeEnabled();
    await this.verboseLog('Validated element is enabled', selector, 'Element is enabled.');
  }

  async validateElementIsDisabled(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Validating element is disabled', selector, 'Checking if element is disabled.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await expect(element).toBeDisabled();
    await this.verboseLog('Validated element is disabled', selector, 'Element is disabled.');
  }

  async validateElementIsEditable(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Validating if element is editable', selector, 'Checking editability.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await expect(element).toBeEditable();
    await this.verboseLog('Validation result for editable element', selector, `Is editable`);
  }

  async validateElementIsReadOnly(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Validating if element is read-only', selector, 'Checking read-only status.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await expect(element).not.toBeEditable();
    await this.verboseLog('Validation result for read-only element', selector, `Is read-only`);
  }

  async validateIsSelected(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Validating element is selected', selector, 'Checking if element is selected.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await expect(element).toBeChecked();
    await this.verboseLog('Validated element is selected', selector, `Is Selected`);
  }

  async selectDropdownOption(page: Page, dropdownSelector: string, optionSelector: string): Promise<void> {
    await this.verboseLog('Selecting dropdown option', optionSelector, 'Attempting to select dropdown option.');
    const frame = await this.switchFrame(page);
    const dropdown = frame.locator(dropdownSelector);
    await dropdown.selectOption(optionSelector);
    await this.verboseLog('Selected dropdown option', optionSelector, 'Dropdown option has been selected successfully.');
  }

  async acceptAlert(page: Page): Promise<void> {
    await this.verboseLog('Accepting alert', '', 'Waiting for alert dialog.');
    page.once('dialog', async (dialog: Dialog) => { 
        await dialog.accept();
        await this.verboseLog('Accepted alert', '', 'Alert has been accepted.');
    });
}

async dismissAlert(page: Page): Promise<void> {
    await this.verboseLog('Dismissing alert', '', 'Waiting for alert dialog.');
    page.once('dialog', async (dialog: Dialog) => { 
        await dialog.dismiss();
        await this.verboseLog('Dismissed alert', '', 'Alert has been dismissed.');
    });
}


  async waitAndSee(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Waiting for element to be visible', selector, 'Waiting for visibility.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector).first();
    await element.waitFor({ state: 'visible' });
    await this.verboseLog('Waited for element to be visible', selector, 'Element is now visible.');
  }

  async mouseHover(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Waiting for element to be visible', selector, 'Waiting for visibility.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await element.waitFor({ state: 'visible' });
    await element.hover();
    await this.verboseLog('Element is now visible and hovered', selector, 'Hover action completed.');
  }

  async getCurrentUrl(page: Page): Promise<string> {
    const frame = await this.switchFrame(page);
    const url = frame.url();
    await this.verboseLog('Retrieved current URL', '', `Current URL: "${url}"`);
    return url;
  }

  async waitAndVisible(page: Page, selector: string): Promise<boolean> {
    await this.verboseLog('Waiting for element to be visible', selector, 'Waiting for visibility.');
    const frame = await this.switchFrame(page);
    return await frame.locator(selector).waitFor({ state: 'visible', timeout: 5000 })
      .then(() => true)
      .catch(() => false);
  }

  async pressKey(page: Page, key: string): Promise<void> {
    await this.verboseLog('Pressing key', key, 'Attempting to press key.');
    await page.keyboard.press(key);
    await this.verboseLog('Pressed key', key, 'Key has been pressed successfully.');
  }

  async holdKey(page: Page, key: string): Promise<void> {
    await this.verboseLog('Holding key', key, 'Attempting to hold key.');
    await page.keyboard.down(key);
    await this.verboseLog('Held key', key, 'Key is being held down.');
  }

  async releaseKey(page: Page, key: string): Promise<void> {
    await this.verboseLog('Releasing key', key, 'Attempting to release key.');
    await page.keyboard.up(key);
    await this.verboseLog('Released key', key, 'Key has been released successfully.');
  }

  async scrollToElement(page: Page, selector: string): Promise<void> {
    await this.verboseLog('Scrolling to element', selector, 'Attempting to scroll to the element.');
    const frame = await this.switchFrame(page);
    const element = frame.locator(selector);
    await element.scrollIntoViewIfNeeded();
    await this.verboseLog('Scrolled to element', selector, 'Element is now in view.');
  }

  async uploadFile(page: Page, selector: string, filePath: string | string[]): Promise<void> {
    await this.verboseLog('Uploading file(s)', selector, `Attempting to upload file(s): "${Array.isArray(filePath) ? filePath.join(', ') : filePath}".`);
    const frame = await this.switchFrame(page);
    const input = frame.locator(selector);

    if (Array.isArray(filePath)) {
      await input.setInputFiles(filePath);
    } else {
      await input.setInputFiles(filePath);
    }

    await this.verboseLog('Uploaded file(s)', selector, `File(s) uploaded: "${Array.isArray(filePath) ? filePath.join(', ') : filePath}".`);
  }

  async downloadFile(page: Page, selector: string, downloadPath = '../downloads'): Promise<string> {
    await this.verboseLog('Initiating file download', selector, 'Attempting to download file.');
    const downloadPromise = page.waitForEvent('download');
    await this.waitAndClick(page, selector);
    const download = await downloadPromise;
    const suggestedFilePath = `${downloadPath}/${download.suggestedFilename()}`;
    await download.saveAs(suggestedFilePath);
    await this.verboseLog('Downloaded file', selector, `File saved to: "${suggestedFilePath}".`);
    return suggestedFilePath;
  }

  async convert12hTo24h(time12h: string): Promise<string> {
    const [time, period] = time12h.split(" ");
    const [hours, minutes] = time.split(":");
    let hours24h = parseInt(hours);
    if (period === "PM" && hours24h !== 12) {
      hours24h += 12;
    } else if (period === "AM" && hours24h === 12) {
      hours24h = 0;
    }
    return `${hours24h.toString().padStart(2, "0")}:${minutes}`;
  }

  // async getCurrentTime(): Promise<string> {
  //   const date = new Date();
  //   const day = date.getDate().toString().padStart(2, "0");
  //   const month = (date.getMonth() + 1).toString().padStart(2, "0");
  //   const year = date.getFullYear().toString();
  //   const hours = (date.getHours() % 12 === 0 ? 12 : date.getHours() % 12)
  //     .toString()
  //     .padStart(2, "0");
  //   const minutes = date.getMinutes().toString().padStart(2, "0");
  //   const ampm = date.getHours() < 12 ? "AM" : "PM";
  
  //   return `Last Updated: ${month}/${day}/${year} at ${hours}:${minutes} ${ampm} IST`;
  // }

  // async checkTimeStamp(page: Page, testId: string): Promise<void> {
  //     const currentTime = await this.getCurrentTime();
  //     const time = await page.getByTestId(testId).textContent();
  //     await expect(time).toBe(currentTime);
  // }

  async getISTNow(): Promise<Date> {
    const now = new Date();
    const istTimeStr = now.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' });
    return new Date(istTimeStr);
  }

  async parseUIFormattedTime(text: string): Promise<Date> {
    const match = text.match(/Last Updated: (\d{2})\/(\d{2})\/(\d{4}) at (\d{2}):(\d{2}) (AM|PM) IST/);
    if (!match) throw new Error(`Invalid time format: "${text}"`);

    const [, mm, dd, yyyy, hh, min, ampm] = match;
    let hours = parseInt(hh, 10);
    if (ampm === 'PM' && hours !== 12) hours += 12;
    if (ampm === 'AM' && hours === 12) hours = 0;

    const dateStr = `${yyyy}-${mm}-${dd}T${hours.toString().padStart(2, '0')}:${min.padStart(2, '0')}:00+05:30`;
    return new Date(dateStr);
  }

  async checkTimeStamp(page: Page, testId: string): Promise<void> {
    const text = await page.getByTestId(testId).textContent();
    if (!text) throw new Error(`No text found for testId: ${testId}`);
  
    const uiTime = await this.parseUIFormattedTime(text);
    const systemTime = await this.getISTNow();
  
    const diffInMs = Math.abs(uiTime.getTime() - systemTime.getTime());
    const allowedDiff = 60 * 1000;
  
    expect(diffInMs).toBeLessThanOrEqual(allowedDiff);
  }

  async checkDateFilter(page: Page, testId: string, filterData: string[]): Promise<void> {
    for (let idx = 0; idx < filterData.length; idx++) {
      if (filterData[idx] == "custom") {
        await page
          .getByTestId(`date-time-range-dropdown-${testId}-zselect-container`)
          .click();
        await page
          .getByTestId(
            `date-time-range-dropdown-${testId}-zselect-container-z-list-list-item-item-${idx}`,
          )
          .click();
        const startDate =
          (await page.textContent(`[data-testid="start-date-tz-${testId}"]`)) +
          " " +
          (await this.convert12hTo24h(
            await page.textContent(
              `[data-testid="time-picker-container-date-time-range-start-tz-${testId}"]`,
            ) as string
          ));
        const endDate =
          (await page.textContent(`[data-testid="end-date-tz-${testId}"]`)) +
          " " +
          (await this.convert12hTo24h(
            await page.textContent(
              `[data-testid="time-picker-container-date-time-range-end-tz-${testId}"]`,
            ) as string
          ));
        const timeZone = await page.textContent(
          `[data-testid="date-time-range-tz-${testId}"]`,
        );
        await page.getByTestId("z-button-ok").click();
        await expect(
          page.getByTestId(
            `date-time-range-dropdown-${testId}-zselect-container`,
          ),
        ).toHaveText(startDate + endDate + " " + timeZone);
      } else {
        await page
          .getByTestId(`date-time-range-dropdown-${testId}-zselect-container`)
          .click();
        await page
          .getByTestId(
            `date-time-range-dropdown-${testId}-zselect-container-z-list-list-item-item-${idx}`,
          )
          .click();
        await expect(
          page.getByTestId(
            `date-time-range-dropdown-${testId}-zselect-container`,
          ),
        ).toHaveText(filterData[idx]);
      }
    }
  }

  async checkFilters(page: Page, testId: string, filterData: string[]): Promise<void> {
    for (let idx = 0; idx < filterData.length; idx++) {
      await page
        .getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown`)
        .click();
      await expect(
        page.getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown`),
      ).toBeEnabled();
      await this.checkSwitchableTabs(page, `${testId}-modal-pill-grp-${idx}-pill-dropdown-filter-modal-segment-control-0`);
      await this.checkSwitchableTabs(page, `${testId}-modal-pill-grp-${idx}-pill-dropdown-filter-modal-segment-control-1`);
    }
  }

  async checkConnectorFilters(page: Page, testId: string, filterData: string[]): Promise<void> {
    for (let idx = 0; idx < filterData.length; idx++) {
      await page
        .getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown`)
        .click();
      await expect(
        page.getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown`),
      ).toBeEnabled();
      await expect(
        page.getByTestId(`filter-modal-${testId}-modal-pill-grp-${idx}-pill-dropdown`),
      ).toBeVisible();
      await expect(
        page.getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown-filter-modal-filter-modal-search-bar`),
      ).toBeVisible();
      await expect(
        page.getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown-filter-modal-filter-modal-select-list`),
      ).toBeVisible();
      await expect(
        page.getByTestId(`${testId}-modal-pill-grp-${idx}-pill-dropdown-filter-modal-filter-modal-selected-list`),
      ).toBeVisible();
    }
  }

  async checkUrl(page: Page, url: string): Promise<void> {
    await expect(page).toHaveURL(url);
  }

  async checkHeader(page: Page, selector: string, header: string): Promise<void> {
    const actualHeader = await page.locator(selector).textContent();
    await expect(actualHeader).toBe(header);
  }

  async checkHeaderByTestId(page: Page, selector: string, header: string): Promise<void> {
    const actualHeader = await page.getByTestId(selector).textContent();
    await expect(actualHeader).toBe(header);
  }

  async isDataRequired(page: Page, testId: string): Promise<boolean> {
    const emptyCard = await page.getByTestId(`empty-card-analytic-state-${testId}`).isVisible();
    return emptyCard;
  }

  async isSubscriptionRequired(page: Page, testId: string): Promise<boolean> {
    const subscriptionCard = await page.getByTestId(`subscription-required-analytic-state-${testId}`).isVisible();
    return subscriptionCard;
  }

  async isDataAvailable(page: Page, testId: string): Promise<boolean> {
    const emptyCard = await this.isDataRequired(page, testId);
    const subscriptionCard = await this.isSubscriptionRequired(page, testId);
    return emptyCard || subscriptionCard;
  }

  async checkGraph(page: Page, graphId: string): Promise<void> {
    const canvasLocator = page.getByTestId(graphId).locator("canvas").nth(1);

    const canvasWidth = await canvasLocator.evaluate((el: HTMLCanvasElement) => el.width);
    const canvasHeight = await canvasLocator.evaluate((el: HTMLCanvasElement) => el.height);

    console.log("Canvas Width is: ", canvasWidth);
    console.log("Canvas Height is: ", canvasHeight);

    expect(canvasWidth).toBeGreaterThan(0);
    expect(canvasHeight).toBeGreaterThan(0);
  }

  async checkSwitchableTabs(page: Page, tabTestId: string): Promise<void> {
    await page.getByTestId(tabTestId).click();
    await expect(page.getByTestId(tabTestId)).toBeEnabled();
  }

  async getValue(page: Page, selector: string, valueType: string = "number"): Promise<number> {
    const value = await page.getByTestId(selector).textContent();

    if (!value) return -1;

    let text = value.trim().toLowerCase();

    if (/^-/.test(text)) 
    return -1;

    const numberMultipliers: Record<string, number> = {
      k: 1000,
      m: 1000000,
      b: 1000000000,
    };

    const dataMultipliers: Record<string, number> = {
      b: 1,
      kb: 1000,
      mb: 1000000,
      gb: 1000000000,
      tb: 1000000000000,
    };

    if (text.endsWith("b") && valueType === "data" && !text.match(/kb|mb|gb|tb/)) {
      text += "y";
    }

    const multipliers: Record<string, number> = valueType === "data" ? dataMultipliers : numberMultipliers;

    const match = text.match(/^([\d,.]+)\s*([a-z]*)$/);
    if (!match) return 0;

    let [_, numStr, unit] = match;
    unit = unit.replace("by", "b");

    let multiplier = multipliers[unit] || 1;

    return parseFloat(numStr.replace(/,/g, "")) * multiplier;
  }

  async checkTable(page: Page, tableSelector: string, tableRows: Record<string, string>, hoverable: boolean): Promise<void> {
    await expect(page.getByTestId(tableSelector)).toBeVisible();
    await Promise.all(
      Object.entries(tableRows).map(async ([key, expectedValue]) => {
        const header = await page.getByTestId(`${key}-header-cell`).textContent();
        expect(header).toBe(expectedValue);
      })
    );
    if (await this.isDataInTable(page, tableSelector)) {
      if (hoverable) {
        await page.getByTestId(`${tableSelector}-z-list-list-item-item-0`).hover();
      }
    } else {
      await expect(page.getByTestId(`${tableSelector}-z-list-empty`)).toBeVisible();
    }
  }

  async isDataInTable(page: Page, tableSelector: string): Promise<boolean> {
    await expect(page.getByTestId(tableSelector)).toBeVisible();
    const data = await page.getByTestId(`${tableSelector}-z-list-list-item-item-0`).isVisible();
    return data;
  }

  async checkCards(page: Page, cardName: string, cardSelector: string, tableRows: Record<string, string>, hoverable: boolean): Promise<void> {
    await expect(page.getByTestId(`${cardSelector}-disabled`)).toBeVisible();
    const text = await page.locator(`(//div[@data-testid='${cardSelector}']/div/div)[1]`).textContent();
    await expect(text).toBe(cardName);
    const incidents = await page.locator(`(//div[@data-testid='${cardSelector}']/div/ul)`).textContent();
    
    if (incidents === null) {
      throw new Error(`Incidents text content is null for selector: ${cardSelector}`);
    }
  
    await page.locator(`(//div[@data-testid='${cardSelector}']/div/div)[1]`).click();
    if (Number(incidents[0]) > 0) {
      await expect(page.getByTestId(`${cardSelector}-expanded`)).toBeVisible();
      const tableName = cardName.toLowerCase();
      await this.checkTable(page, `${tableName}-table-z-table`, tableRows, hoverable);
      await page.getByTestId(`${cardSelector}-collapse-button`).click();
      await expect(page.getByTestId(`${cardSelector}-disabled`)).toBeVisible();
    }
  }

  async footerNavigaion(page: Page, footer: string, url: string): Promise<void> {
    await page.getByTestId(footer).click();
    await page.waitForTimeout(5000);
    await expect(page).toHaveURL(url);
  }

  async checkfilterByType(page: Page, testId: string, typeFilterData: string[]): Promise<void> {
    for (let idx = 0; idx < typeFilterData.length; idx++) {
      await page.getByTestId(testId).click();
      await page.getByTestId(`${testId}-z-list-list-item-item-${idx}`).click();
      await expect(page.getByTestId(testId)).toContainText(typeFilterData[idx]);
    }
  }

  async checkProgressContainer(page: Page, testId: string): Promise<void> {
    await expect(page.getByTestId(`${testId}-progress-list`)).toBeVisible();
  }

  async checkSearchBar(page: Page, tableName: string): Promise<void> {
    await expect(page.getByTestId(`${tableName}-search-bar-expanded`)).toBeVisible();
    await expect(page.getByTestId(`${tableName}-search-bar-search-icon`)).toBeVisible();
    await expect(page.getByTestId(`${tableName}-search-bar-input`)).toBeEditable();
  }
}

export default new PlaywrightActions();