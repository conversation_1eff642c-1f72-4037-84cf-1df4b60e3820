import { request, APIRequestContext, APIResponse } from '@playwright/test';
import { config } from 'dotenv';
import { envConf } from '../config/envConf';
config();
interface RequestOptions {
  timeout?: number;
  maxRetries: number; // Ensure maxRetries is required
  ignoreHTTPSErrors?: boolean;
  headers?: RequestHeaders; // Add headers property
}
interface RequestHeaders {
  [key: string]: string;
}
const { backend } = envConf;
if (!backend.apiUrl) {
  throw new Error('API URL is not defined in the environment configuration.');
}
if (!backend.headers || !backend.headers.api) {
  throw new Error('API headers are not defined in the environment configuration.');
}
class ApiHelper {
  private setConf: RequestOptions;
  private verbose: boolean;
  private authToken: string | null = null;
  constructor() {
    this.setConf = {
      timeout: 60 * 1000,
      maxRetries: 3,
      ignoreHTTPSErrors: true,
    };
    this.verbose = true;
  }
  setBearerToken(raw: string) {
    this.authToken = raw.replace(/^Bearer\s+/i, '');
    console.debug('Bearer token set:', this.authToken); // Debug log
  }
  validateToken() {
    if (!this.authToken) {
      throw new Error('Bearer token is not set. Please set the token using setBearerToken() before making requests.');
    }
  }
  async verboseLog(url: string, options: RequestOptions, response: APIResponse): Promise<void> {
    if (this.verbose) {
      console.debug('API Request Debug Information:');
      console.debug('URL:', url);
      console.debug('Options:', options);
      console.debug('Headers:', options.headers);
      const contentType = response.headers()['content-type'];
      if (contentType && contentType.includes('application/json')) {
        console.debug('Response:', JSON.stringify(await response.json(), null, 2));
      } else {
        const text = await response.text();
        console.debug('Response (non-JSON):', text);
      }
    }
  }
  async sendRequest(endpoint: string, method: string, body: object = {}, headers: RequestHeaders = {}): Promise<any> {
    this.validateToken();
    const context: APIRequestContext = await request.newContext();
    const baseUrl = backend.apiUrl.endsWith('/') ? backend.apiUrl.slice(0, -1) : backend.apiUrl;
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    const url = `${baseUrl}/${normalizedEndpoint}`;
    const mergedHeaders: RequestHeaders = {
      ...backend.headers.api,
      ...headers,
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.authToken}`,
    };
    const options = {
      method: method,
      body: JSON.stringify(body),
      headers: mergedHeaders,
      timeout: this.setConf.timeout,
      ignoreHTTPSErrors: this.setConf.ignoreHTTPSErrors,
      maxRetries: this.setConf.maxRetries,
    };
    for (let attempt = 0; attempt < (this.setConf.maxRetries || 3); attempt++) {
      try {
        const response: APIResponse = await context.fetch(url, options);
        await this.verboseLog(url, options, response);
        if (response.ok()) {
          const contentType = response.headers()['content-type'];
          if (contentType && contentType.includes('application/json')) {
            return await response.json();
          } else {
            throw new Error('Received non-JSON response');
          }
        } else {
          console.warn(`Attempt ${attempt + 1} failed with status: ${response.status()}`);
        }
      } catch (error) {
        if (attempt === (this.setConf.maxRetries || 3) - 1) {
          console.error(`All ${(this.setConf.maxRetries || 3)} attempts failed.`);
          throw error;
        }
        console.warn(`Retrying request (${attempt + 1}/${this.setConf.maxRetries || 3})...`);
      }
    }
  }
  async sendGetRequest(endpoint: string, body: object = {}, headers: RequestHeaders = {}): Promise<any> {
    return await this.sendRequest(endpoint, 'GET', body, headers);
  }
  async sendPostRequest(endpoint: string, body: object = {}, headers: RequestHeaders = {}): Promise<any> {
    return await this.sendRequest(endpoint, 'POST', body, headers);
  }
  async sendPutRequest(endpoint: string, body: object = {}, headers: RequestHeaders = {}): Promise<any> {
    return await this.sendRequest(endpoint, 'PUT', body, headers);
  }
  async sendPatchRequest(endpoint: string, body: object = {}, headers: RequestHeaders = {}): Promise<any> {
    return await this.sendRequest(endpoint, 'PATCH', body, headers);
  }
  async sendDeleteRequest(endpoint: string, headers: RequestHeaders = {}): Promise<any> {
    return await this.sendRequest(endpoint, 'DELETE', {}, headers);
  }
}
export default new ApiHelper();