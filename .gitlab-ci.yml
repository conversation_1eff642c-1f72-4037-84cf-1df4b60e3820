include:
  - component: "$CI_SERVER_FQDN/to-be-continuous/node/gitlab-ci-node@${TBC_NODE_COMP_VERSION}"
    inputs:
      config-registry: "https://nexus.corp.zscaler.com/repository/zscaler-npm-registry"
      config-scoped-registries: "@fortawesome:https://nexus.corp.zscaler.com/repository/zscaler-npm-registry @zs-nimbus:https://nexus.corp.zscaler.com/repository/zscaler-npm-registry @zscaler:https://nexus.corp.zscaler.com/repository/zscaler-npm-registry @ztds:https://nexus.corp.zscaler.com/repository/zscaler-npm-registry @zuxp:https://nexus.corp.zscaler.com/repository/zscaler-npm-registry"
      image: "nexus.corp.zscaler.com:9016/node:${NODE_IMAGE_VERSION}"
      manager: "pnpm"
      build-args: '--node-options="--max-old-space-size=8192" -r build'
      test-args: "one-ui test:coverage"
      lint-enabled: true
      lint-args: "prelint && pnpm verify"
      audit-disabled: true
      semgrep-disabled: true
      install-extra-opts: "--registry=https://nexus.corp.zscaler.com/repository/zscaler-npm-registry/"
  - component: "$CI_SERVER_FQDN/to-be-continuous/docker/gitlab-ci-docker@${TBC_DOCKER_COMP_VERSION}"
    inputs:
      file: "./docker/dev/Dockerfile.gitlab"
      snapshot-image: "${CI_REGISTRY_IMAGE}:${TAG_VERSION}"
      release-image: "637423576360.dkr.ecr.us-west-2.amazonaws.com/${APP_NAME}:${TAG_VERSION}"
      skopeo-image: quay.io/skopeo/stable:${TBC_SKOPEO_IMAGE_VERSION}
      prod-publish-strategy: auto
      semrel-release-disabled: true
      #release-extra-tags: " \\g<major>.\\g<minor>\\g<build> \\g<major>\\g<build>"
  - component: "$CI_SERVER_FQDN/to-be-continuous/docker/gitlab-ci-docker-ecr@${TBC_DOCKER_COMP_VERSION}"
    inputs:
      aws-oidc-role-arn: "arn:aws:iam::637423576360:role/GitLabCI-prod20250410065525641400000002"
      aws-region: "us-west-2"

stages:
  - build
  - package-build
  - publish
  - deploy
  - deploy-app-dev
  - test
  - deploy-app-demo
  - deploy-app-quality
  - release-tag
  - deploy-app-stage
  - publish-semrel
  - deploy-app-prod

variables:
  PROD_REF: "/^(hotfix-|release-|feature-).*$|^(main|feature-one-api-phase2|feature/XC-7298-zpa-oneapi-integration|zdf|zdx-dev|zwa-dev)/"
  SUBSYSTEM_ENVS: "/^(zdf|zdx-dev|zwa-dev)$/"
  DEMO_FEATURE_BRANCH_REGEX: "/^demo.*$/"
  ECR_REGISTRY: "637423576360.dkr.ecr.us-west-2.amazonaws.com"
  NPM_CONFIG_STORE_DIR: ".npm/.pnpm-store"
  APP_NAME: "zuxp-ui"
  PAGES_DOMAIN: "xc.pages.corp.zscaler.com"
  PAGES_URL: "https://${PAGES_DOMAIN}/${PAGES_PREFIX}"
  GIT_AUTHOR_EMAIL: "<EMAIL>"

workflow:
  rules:
    # on non-prod, non-integration branches: prefer MR pipeline over branch pipeline
    - if: "$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS && $CI_COMMIT_REF_NAME !~ $PROD_REF && $CI_COMMIT_REF_NAME !~ $INTEG_REF"
      when: never
    - if: '$CI_COMMIT_MESSAGE =~ "/\[(ci skip|skip ci) on ([^],]*,)*prod(,[^],]*)*\]/"'
      when: never
    - when: always

.docker-base:
  variables:
    DOCKER_REGISTRY_SNAPSHOT_USER: "$CI_REGISTRY_USER"
    DOCKER_REGISTRY_SNAPSHOT_PASSWORD: "$CI_REGISTRY_PASSWORD"

docker-sbom:
  stage: test

docker-trivy:
  stage: test
  allow_failure: true
  artifacts:
    expire_in: 30 days

docker-hadolint:
  allow_failure: true
  artifacts:
    expire_in: 30 days

docker-healthcheck:
  stage: test

docker-kaniko-build:
  stage: publish

docker-publish:
  variables:
    KUBERNETES_POD_LABELS_INTERNET_ALLOW: "internet=allow"
    GIT_STRATEGY: none
  needs:
    - job: docker-kaniko-build
      artifacts: false 
    - job: build-version
      artifacts: true 
  rules:
    - !reference [.deploy_rules, rules]
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF
      when: on_success

node-sbom:
  allow_failure: true
  variables:
    KUBERNETES_CPU_REQUEST: "2"
    KUBERNETES_MEMORY_REQUEST: "12Gi"

node-lint:
  extends: .node-base
  stage: build
  script:
    - pnpm install
    - pnpm prelint && pnpm verify

build-version:
  stage: build
  script:
    - SELECTED_VERSION=$(grep '"version"' ./packages/xc/app/package.json | awk -F'"' '{print $4}')
    - SANITIZED_REF_NAME=$(echo "$CI_COMMIT_REF_NAME" | sed 's/\//-/g')
    - export TAG_VERSION="${SELECTED_VERSION}-${SANITIZED_REF_NAME}-${CI_PIPELINE_IID}-${CI_COMMIT_SHORT_SHA}"
    - export BUILD_VERSION="${CI_PIPELINE_CREATED_AT}_${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}"
    - export RELEASE_TAG_VERSION="${SELECTED_VERSION}-${CI_COMMIT_SHORT_SHA}"
    - echo "TAG_VERSION=${TAG_VERSION}" >> .env
    - echo "RELEASE_TAG_VERSION=${RELEASE_TAG_VERSION}" >> .env
    - echo "${RELEASE_TAG_VERSION}, ${TAG_VERSION}"
    - echo "NEXT_PUBLIC_BUILD_SHA=${CI_COMMIT_SHORT_SHA}" >> .env
    - echo "NEXT_PUBLIC_BUILD_VERSION=${BUILD_VERSION}" >> .env
    - echo "NEXT_PUBLIC_TAG_VERSION=${TAG_VERSION}" >> .env
    - echo "DOCKER_SNAPSHOT_IMAGE=${CI_REGISTRY_IMAGE}:${TAG_VERSION}" >> .env
  artifacts:
    reports:
      dotenv: .env
  rules:
    - if: "$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS && $CI_COMMIT_REF_NAME !~ $PROD_REF"
      when: never
    - if: '$CI_COMMIT_MESSAGE =~ "/\[(ci skip|skip ci) on ([^],]*,)*prod(,[^],]*)*\]/" && $CI_COMMIT_REF_NAME =~ $PROD_REF'
      when: never
    - when: always

.deploy_rules:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: "$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS && $CI_COMMIT_REF_NAME !~ $PROD_REF"
      when: never
    - if: '$CI_COMMIT_MESSAGE =~ "/\[(ci skip|skip ci) on ([^],]*,)*prod(,[^],]*)*\]/" && $CI_COMMIT_REF_NAME =~ $PROD_REF'
      when: never

node-build:
  extends: .node-base
  stage: build
  script:
    - npm install -g pnpm@${PNPM_VERSION}
    - export JEST_JUNIT_OUTPUT_DIR=./reports
    - export JEST_JUNIT_OUTPUT_NAME=node-test.jest.xml
    - if [[ "$NODE_BUILD_DISABLED" != "true" ]]; then $NODE_MANAGER $NODE_BUILD_ARGS; fi
    - |
      if [[ $CI_PIPELINE_SOURCE == "merge_request_event" ]]; then
        pnpm --node-options="--max-old-space-size=6144" one-ui build-storybook-test
      else
        pnpm --node-options="--max-old-space-size=6144" one-ui build-storybook
      fi
    - $NODE_MANAGER $NODE_TEST_ARGS
  coverage: '/^All files\s*\|\s*(\d+(?:\.\d+)?)/'
  variables:
    KUBERNETES_CPU_REQUEST: "3.5"
    KUBERNETES_MEMORY_REQUEST: "12Gi"
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: "$NODE_PROJECT_DIR/reports/cobertura-coverage.xml"
      junit:
        - $NODE_PROJECT_DIR/packages/xc/app/test-output/test-results.xml
    when: always
    name: "$CI_JOB_NAME artifacts from $CI_PROJECT_NAME on $CI_COMMIT_REF_SLUG"
    paths:
      - $NODE_PROJECT_DIR/packages/xc/app/.next/static
      - $NODE_PROJECT_DIR/packages/xc/app/.next/standalone
      - $NODE_PROJECT_DIR/packages/xc/app/public
      - $NODE_PROJECT_DIR/packages/xc/app/storybook-static
      - $NODE_PROJECT_DIR/packages/xc/app/test-output/test-results.xml
      - $NODE_PROJECT_DIR/reports/node-coverage.*
      # no way to override default coverage reports when using Mocha
      - $NODE_PROJECT_DIR/reports/lcov.info
      - $NODE_PROJECT_DIR/reports/cobertura-coverage.xml
    expire_in: 30 days
  needs:
    - job: build-version
  rules:
    # always if $NODE_BUILD_DISABLED not set
    - if: '$NODE_BUILD_DISABLED != "true"'
    # else (test only): apply test-policy
    - !reference [.test-policy, rules]

publish-storybook:
  stage: publish
  script:
    - echo "Storybook accessible through ${PAGES_URL}"
  publish: packages/xc/app/storybook-static
  variables:
    PAGES_PREFIX: ""
  pages:
    path_prefix: "$PAGES_PREFIX"
    expire_in: never
  environment:
    name: "${CI_COMMIT_BRANCH}"
    url: "${PAGES_URL}"
  artifacts:
    paths:
      - packages/xc/app/storybook-static
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        PAGES_PREFIX: ""
    - if: $CI_COMMIT_BRANCH =~ $PROD_REF
      variables:
        PAGES_PREFIX: "$CI_COMMIT_BRANCH"
    - when: never

publish-review-pages:
  stage: publish
  script:
    - echo "Storybook accessible through ${PAGES_URL}"
  publish: packages/xc/app/storybook-static
  pages:
    path_prefix: "$PAGES_PREFIX"
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: "${PAGES_URL}"
  artifacts:
    paths:
      - packages/xc/app/storybook-static
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      variables:
        PAGES_PREFIX: "mr-$CI_MERGE_REQUEST_IID"

.before_script:
  before_script:
    - >-
      sed -i \
        -e 's|http://archive.ubuntu.com/ubuntu|https://nexus.corp.zscaler.com/repository/ubuntu-oracular-proxy|g' \
        -e 's|http://security.ubuntu.com/ubuntu|https://nexus.corp.zscaler.com/repository/ubuntu-oracular-security-proxy|g' \
        -e 's|http://deb.debian.org/debian-security|https://nexus.corp.zscaler.com/repository/apt-bookworm-security-proxy|g' \
        -e 's|http://deb.debian.org/debian|https://nexus.corp.zscaler.com/repository/apt-bookworm-proxy|g' \
        /etc/apt/sources.list

    # Install GPG and gpg-agent
    - apt update && apt install -y gnupg gpg-agent

    # Fix GPG permissions in the pipeline environment
    - mkdir -p ~/.gnupg
    - chmod 700 ~/.gnupg

    # Configure gpg-agent for headless (non-interactive) CI pipeline environment
    - echo "use-agent" > ~/.gnupg/gpg.conf
    - echo "allow-loopback-pinentry" > ~/.gnupg/gpg-agent.conf
    - gpgconf --kill gpg-agent # Ensure the agent is restarted with the new configuration
    - gpgconf --launch gpg-agent

    # Import the GPG private key
    - echo "$XC_BOT_GPG_KEY_BASE64" | base64 -d | gpg --batch --yes --import
    - GPG_KEY_ID=$(gpg --list-secret-keys --keyid-format LONG "${GIT_AUTHOR_EMAIL}" | grep sec | awk '{print $2}' | awk -F/ '{print $2}')
    - echo "GPG Key ID:$GPG_KEY_ID"

.base_deploy_script:
  script:
    - |
      case "${DEPLOY_ENV}" in
        development/*) DEPLOY_ENV_STATIC="${DEPLOY_ENV#development/}" ;;
        quality/*) DEPLOY_ENV_STATIC="${DEPLOY_ENV#quality/}" ;;
        stage/alpha) DEPLOY_ENV_STATIC="alpha" ;;
        stage/beta/us-w2) DEPLOY_ENV_STATIC="beta" ;;
        stage/beta/eu-w1) DEPLOY_ENV_STATIC="beta-eu-w1" ;;
        prod/us-w2) DEPLOY_ENV_STATIC="prod" ;;
        prod/eu-w1) DEPLOY_ENV_STATIC="prod-eu-w1" ;;
        *) DEPLOY_ENV_STATIC="${DEPLOY_ENV}" ;;
      esac
    - echo "Transformed DEPLOY_ENV:$DEPLOY_ENV_STATIC"
    - git clone -b ${DEPLOY_ENV_STATIC} "https://oauth2:$XC_CICD_SERVICE_TOKEN@${CI_SERVER_HOST}/zscaler/xc/devops/xc-argocd-manifests.git"
    - cd xc-argocd-manifests
    - echo "version:${TAG_VERSION}"
    - git remote set-url origin "https://oauth2:$XC_CICD_SERVICE_TOKEN@${CI_SERVER_HOST}/zscaler/xc/devops/xc-argocd-manifests.git"
    - 'sed -i "s|^version:.*|version: ${TAG_VERSION}|" "${APP_NAME}"/Chart.yaml'
    - |
      if grep -q '^appVersion:' "${APP_NAME}/Chart.yaml"; then
        sed -i "s|^appVersion:.*|appVersion: ${TAG_VERSION}|" "${APP_NAME}/Chart.yaml"
      else
        echo "appVersion: ${TAG_VERSION}" >> "${APP_NAME}/Chart.yaml"
      fi
    - 'sed -i "/dependencies:/,/]/ s|version:.*|version: \"${HELM_CHART_VERSION}\"|" "${APP_NAME}"/Chart.yaml'
    - 'sed -i "/^${APP_NAME}:/,/^ *tag:/s|^\( *tag: *\).*|\1${TAG_VERSION}|" "${APP_NAME}"/values.yaml'
    - 'sed -i "s|^version:.*|version: ${TAG_VERSION}|" "${APP_NAME}"/values.yaml'
    - 'sed -i "/appVersion:/ s|appVersion:.*|appVersion: ${TAG_VERSION}|" "${APP_NAME}"/values.yaml'
    - 'sed -i "/dependencies:/,/]/ s|repository:.*|repository: oci://registry.gitlab.corp.zscaler.com/zscaler/xc/devops/xc-helm-charts/charts|" "${APP_NAME}"/Chart.yaml'
    - git config --global user.signingkey "$GPG_KEY_ID"
    - git config --global commit.gpgsign true
    - git config --global gpg.program gpg
    - git config --global user.email "${GIT_AUTHOR_EMAIL}"
    - git config --global user.name "XC CI Bot"
    - git add .
    - git commit -S -m "Updated ${APP_NAME} to ${TAG_VERSION} for ${DEPLOY_ENV_STATIC} env"
    - echo "Deploying ${TAG_VERSION} to ${DEPLOY_ENV_STATIC}"
    - git push origin HEAD:${DEPLOY_ENV_STATIC}

deploy-app-dev:
  stage: deploy-app-dev
  image: nexus.corp.zscaler.com:9016/bitnami/git:latest
  extends:
    - .before_script
    - .base_deploy_script
  parallel:
    matrix:
      - DEPLOY_ENV:
          - development/pre-dev
          - development/dev
          - development/zdx-dev
          - development/zwa-dev
          - development/demo
  environment:
    name: $DEPLOY_ENV
  rules:
    - !reference [.deploy_rules, rules]
    # Rule for deploying to pre-dev and dev for branches matching PROD_REF (excluding zdx-dev, zwa-dev)
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF && $CI_COMMIT_REF_NAME !~ $SUBSYSTEM_ENVS && $CI_COMMIT_REF_NAME !~ $DEMO_FEATURE_BRANCH_REGEX && ($DEPLOY_ENV == "development/pre-dev" || $DEPLOY_ENV == "development/dev")
      when: on_success
    # Rule for zdx-dev environment, deploy only from the zdx-dev branch
    - if: $CI_COMMIT_REF_NAME == "zdx-dev" && $DEPLOY_ENV == "development/zdx-dev"
      when: on_success
    # Rule for zwa-dev environment, deploy only from the zwa-dev branch
    - if: $CI_COMMIT_REF_NAME == "zwa-dev" && $DEPLOY_ENV == "development/zwa-dev"
      when: on_success
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF && $DEPLOY_ENV == "development/demo"
      when: manual
      allow_failure: true

# Stage:QA / Test
deploy-app-quality:
  stage: deploy-app-quality
  image: nexus.corp.zscaler.com:9016/bitnami/git:latest
  extends:
    - .before_script
    - .base_deploy_script
  parallel:
    matrix:
      - DEPLOY_ENV:
          - quality/qa
          - quality/test
  environment:
    name: $DEPLOY_ENV
  rules:
    - !reference [.deploy_rules, rules]
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF && $CI_COMMIT_REF_NAME !~ $SUBSYSTEM_ENVS && $CI_COMMIT_REF_NAME !~ $DEMO_FEATURE_BRANCH_REGEX && ($DEPLOY_ENV == "quality/qa" || $DEPLOY_ENV == "quality/test")
      when: manual
  needs:
    - job: build-version
      artifacts: true
    - job: deploy-app-dev
      parallel:
        matrix:
          - DEPLOY_ENV: development/pre-dev

create-release-tag:
  stage: release-tag
  image: nexus.corp.zscaler.com:9016/bitnami/git:latest
  extends:
    - .before_script
  script:
    - git remote set-url origin "https://oauth2:$XC_CICD_SERVICE_TOKEN@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - git config --global user.signingkey "$GPG_KEY_ID"
    - git config --global commit.gpgsign true
    - git config --global gpg.program gpg
    - git config --global user.email "${GIT_AUTHOR_EMAIL}"
    - git config --global user.name "XC CI Bot"
    - echo "Creating and pushing Git tag"
    - 'git tag -a "${RELEASE_TAG_VERSION}" -m "Automated Tag: Created by Pipeline ${CI_PIPELINE_ID} [skip ci on prod]" --sign'
    - git push origin "${RELEASE_TAG_VERSION}"
  rules:
    - !reference [.deploy_rules, rules]
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF && $CI_COMMIT_REF_NAME !~ $DEMO_FEATURE_BRANCH_REGEX
      when: on_success
  needs:
    - job: build-version
      artifacts: true
    - job: deploy-app-quality
      parallel:
        matrix:
          - DEPLOY_ENV: quality/test

# Stage: Alpha / Beta
deploy-app-stage:
  stage: deploy-app-stage
  image: nexus.corp.zscaler.com:9016/bitnami/git:latest
  extends:
    - .before_script
    - .base_deploy_script
  parallel:
    matrix:
      - DEPLOY_ENV:
          - stage/alpha
          - stage/beta/us-w2
          #- stage/beta/eu-w1
  environment:
    name: $DEPLOY_ENV
  rules:
    - !reference [.deploy_rules, rules]
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF && $CI_COMMIT_REF_NAME !~ $SUBSYSTEM_ENVS && $CI_COMMIT_REF_NAME !~ $DEMO_FEATURE_BRANCH_REGEX && ($DEPLOY_ENV == "stage/alpha" || $DEPLOY_ENV == "stage/beta/us-w2" || $DEPLOY_ENV == "stage/beta/eu-w1")
      when: manual
  needs:
    - job: build-version
      artifacts: true
    - job: deploy-app-quality
      parallel:
        matrix:
          - DEPLOY_ENV: quality/test

# Stage: Prod-US / Prod-EU
deploy-app-prod:
  stage: deploy-app-prod
  image: nexus.corp.zscaler.com:9016/bitnami/git:latest
  extends:
    #- .default_variables
    - .before_script
    - .base_deploy_script
  parallel:
    matrix:
      - DEPLOY_ENV:
          - prod/us-w2
          #- prod/eu-w1
  environment:
    name: $DEPLOY_ENV
  rules:
    - !reference [.deploy_rules, rules]
    - if: $CI_COMMIT_REF_NAME =~ $PROD_REF && $CI_COMMIT_REF_NAME !~ $SUBSYSTEM_ENVS && $CI_COMMIT_REF_NAME !~ $DEMO_FEATURE_BRANCH_REGEX && ($DEPLOY_ENV == "prod/us-w2" || $DEPLOY_ENV == "prod/eu-w1")
      when: manual
    - when: never
  needs:
    - job: build-version
      artifacts: true
    - job: deploy-app-stage
      parallel:
        matrix:
          - DEPLOY_ENV: stage/beta/us-w2
